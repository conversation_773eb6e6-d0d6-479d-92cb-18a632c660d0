/**
 * 🎨 ADMIN DASHBOARD V3 - UI/UX MODERNO
 * @file AdminDashboard.jsx
 * @description Dashboard Administrativo Principal - Portal Betina V3
 * @version 3.0.0
 * @admin true
 * @features Dark Mode, Glassmorphism, Animations, Responsive Design
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { IntegratedSystemDashboard } from './IntegratedSystemDashboard/IntegratedSystemDashboard'
import { SystemHealthMonitor } from './SystemHealthMonitor/SystemHealthMonitor'
import { AnalyzersMonitor } from './AnalyzersMonitor/AnalyzersMonitor'
import { UserManagement } from './UserManagement/UserManagement'
import { SystemLogs } from './SystemLogs/SystemLogs'
import styles from './AdminDashboard.module.css'

const AdminDashboard = ({ onBack }) => {
  // Estados principais
  const [activeTab, setActiveTab] = useState('system')
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loginInput, setLoginInput] = useState('')
  const [loginError, setLoginError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Estados para funcionalidades modernas
  const [lastActivity, setLastActivity] = useState(new Date())
  const [systemStatus, setSystemStatus] = useState('online')
  const [notifications, setNotifications] = useState([])
  const [isFullscreen, setIsFullscreen] = useState(false)

  // Senha admin - em produção usar hash/JWT
  const ADMIN_PASSWORD = 'betina2025'

  // Função de login melhorada
  const handleLogin = useCallback(async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setLoginError('')

    try {
      // Simular delay de autenticação com feedback visual
      await new Promise(resolve => setTimeout(resolve, 1200))

      if (loginInput === ADMIN_PASSWORD) {
        setIsAuthenticated(true)
        localStorage.setItem('adminAuth', 'true')
        localStorage.setItem('adminLoginTime', new Date().toISOString())

        // Adicionar notificação de sucesso
        setNotifications(prev => [...prev, {
          id: Date.now(),
          type: 'success',
          message: 'Login realizado com sucesso!',
          timestamp: new Date()
        }])
      } else {
        setLoginError('Senha incorreta. Tente novamente.')

        // Shake animation no input
        const input = document.querySelector(`.${styles.passwordInput}`)
        if (input) {
          input.style.animation = 'none'
          setTimeout(() => {
            input.style.animation = 'errorShake 0.5s ease-in-out'
          }, 10)
        }
      }
    } catch (error) {
      setLoginError('Erro interno. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }, [loginInput])

  // Função de logout melhorada
  const handleLogout = useCallback(() => {
    setIsAuthenticated(false)
    localStorage.removeItem('adminAuth')
    localStorage.removeItem('adminLoginTime')
    setLoginInput('')
    setActiveTab('system')
    setNotifications([])
  }, [])

  // Função para trocar de aba com animação
  const handleTabChange = useCallback((tabId) => {
    if (tabId !== activeTab) {
      setActiveTab(tabId)
      setLastActivity(new Date())
    }
  }, [activeTab])

  // Função para toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }, [])

  // Monitorar status do sistema
  useEffect(() => {
    const checkSystemStatus = () => {
      // Simular verificação de status
      const isOnline = navigator.onLine
      setSystemStatus(isOnline ? 'online' : 'offline')
    }

    checkSystemStatus()
    const interval = setInterval(checkSystemStatus, 30000) // Check every 30s

    window.addEventListener('online', checkSystemStatus)
    window.addEventListener('offline', checkSystemStatus)

    return () => {
      clearInterval(interval)
      window.removeEventListener('online', checkSystemStatus)
      window.removeEventListener('offline', checkSystemStatus)
    }
  }, [])

  // Auto-logout após inatividade (30 minutos)
  useEffect(() => {
    if (!isAuthenticated) return

    const checkInactivity = () => {
      const loginTime = localStorage.getItem('adminLoginTime')
      if (loginTime) {
        const timeDiff = Date.now() - new Date(loginTime).getTime()
        const thirtyMinutes = 30 * 60 * 1000

        if (timeDiff > thirtyMinutes) {
          handleLogout()
          alert('Sessão expirada por inatividade. Faça login novamente.')
        }
      }
    }

    const interval = setInterval(checkInactivity, 60000) // Check every minute
    return () => clearInterval(interval)
  }, [isAuthenticated, handleLogout])

  // Verificar autenticação salva
  useEffect(() => {
    const savedAuth = localStorage.getItem('adminAuth')
    if (savedAuth === 'true') {
      setIsAuthenticated(true)
      const loginTime = localStorage.getItem('adminLoginTime')
      if (loginTime) {
        setLastActivity(new Date(loginTime))
      }
    }
  }, [])

  // Configuração das abas com informações detalhadas
  const tabs = useMemo(() => [
    {
      id: 'system',
      label: 'Sistema Integrado',
      icon: '🖥️',
      description: 'Dashboard principal com métricas gerais',
      color: '#6366f1'
    },
    {
      id: 'health',
      label: 'Saúde do Sistema',
      icon: '🏥',
      description: 'Monitoramento de performance e recursos',
      color: '#10b981'
    },
    {
      id: 'analyzers',
      label: 'Analisadores',
      icon: '🔬',
      description: 'Status dos analisadores de dados',
      color: '#f59e0b'
    },
    {
      id: 'users',
      label: 'Usuários',
      icon: '👥',
      description: 'Gerenciamento de usuários e permissões',
      color: '#8b5cf6'
    },
    {
      id: 'logs',
      label: 'Logs',
      icon: '📋',
      description: 'Histórico de eventos e atividades',
      color: '#ef4444'
    }
  ], [])

  // Função para obter informações da aba ativa
  const activeTabInfo = useMemo(() => {
    return tabs.find(tab => tab.id === activeTab)
  }, [tabs, activeTab])

  // Tela de login moderna
  if (!isAuthenticated) {
    return (
      <div className={styles.loginContainer}>
        <div className={styles.loginBox}>
          <div className="login-header">
            <h2>🔐 Acesso Administrativo</h2>
            <p>Portal Betina V3 - Área Restrita</p>
            <div className="system-status">
              <span className={`status-indicator ${systemStatus}`}>
                {systemStatus === 'online' ? '🟢' : '🔴'}
              </span>
              Sistema {systemStatus === 'online' ? 'Online' : 'Offline'}
            </div>
          </div>

          <form onSubmit={handleLogin} className={styles.loginForm}>
            <div className={styles.inputGroup}>
              <label htmlFor="password">Senha de Administrador:</label>
              <input
                id="password"
                type="password"
                value={loginInput}
                onChange={(e) => setLoginInput(e.target.value)}
                placeholder="Digite a senha admin"
                disabled={isLoading}
                className={styles.passwordInput}
                autoComplete="current-password"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !isLoading && loginInput.trim()) {
                    handleLogin(e)
                  }
                }}
              />
            </div>

            {loginError && (
              <div className={styles.error}>
                ❌ {loginError}
              </div>
            )}

            <div className={styles.buttonGroup}>
              <button
                type="submit"
                disabled={isLoading || !loginInput.trim()}
                className={styles.loginButton}
              >
                {isLoading ? (
                  <>
                    <span className="loading-spinner">🔄</span>
                    Verificando...
                  </>
                ) : (
                  <>
                    🔓 Entrar
                  </>
                )}
              </button>

              <button
                type="button"
                onClick={onBack}
                className={styles.backButton}
                disabled={isLoading}
              >
                ← Voltar
              </button>
            </div>
          </form>

          <div className={styles.loginHint}>
            💡 Dica: A senha padrão é "betina2025"
          </div>

          <div className="login-footer">
            <small>
              🔒 Conexão segura •
              🕒 Última atualização: {new Date().toLocaleTimeString()}
            </small>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.adminContainer}>
      {/* Header Admin Moderno */}
      <header className={styles.adminHeader}>
        <div className={styles.headerLeft}>
          <h1>🛠️ Painel Administrativo</h1>
          <span className={styles.version}>Portal Betina V3</span>
          <div className="system-info">
            <span className={`status-badge ${systemStatus}`}>
              {systemStatus === 'online' ? '🟢 Online' : '🔴 Offline'}
            </span>
          </div>
        </div>

        <div className={styles.headerRight}>
          <div className="header-controls">
            <button
              onClick={toggleFullscreen}
              className="control-button"
              title={isFullscreen ? "Sair do modo tela cheia" : "Modo tela cheia"}
            >
              {isFullscreen ? '🗗' : '🗖'}
            </button>

            <div className="notifications-badge">
              🔔
              {notifications.length > 0 && (
                <span className="notification-count">{notifications.length}</span>
              )}
            </div>
          </div>

          <span className={styles.adminUser}>
            👤 Administrador
            <small>Ativo desde {lastActivity.toLocaleTimeString()}</small>
          </span>

          <button
            onClick={handleLogout}
            className={styles.logoutButton}
            title="Sair do painel administrativo"
          >
            🚪 Sair
          </button>
        </div>
      </header>

      {/* Navigation Tabs Moderna */}
      <nav className={styles.tabNavigation}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => handleTabChange(tab.id)}
            className={`${styles.tabButton} ${activeTab === tab.id ? styles.active : ''}`}
            title={tab.description}
            style={{
              '--tab-color': tab.color
            }}
          >
            <span className={styles.tabIcon}>{tab.icon}</span>
            <span className={styles.tabLabel}>{tab.label}</span>
            {activeTab === tab.id && (
              <span className="active-indicator">●</span>
            )}
          </button>
        ))}

        {/* Indicador de aba ativa */}
        <div className="tab-indicator" style={{
          '--active-color': activeTabInfo?.color || '#6366f1'
        }} />
      </nav>

      {/* Content Area Moderna */}
      <main className={styles.adminContent}>
        {activeTab === 'system' && (
          <div className={styles.tabContent}>
            <IntegratedSystemDashboard />
          </div>
        )}

        {activeTab === 'health' && (
          <div className={styles.tabContent}>
            <h2>
              🏥 Monitoramento de Saúde
              <span className="tab-description">{activeTabInfo?.description}</span>
            </h2>
            <SystemHealthMonitor />
          </div>
        )}

        {activeTab === 'analyzers' && (
          <div className={styles.tabContent}>
            <h2>
              🔬 Monitor de Analisadores
              <span className="tab-description">{activeTabInfo?.description}</span>
            </h2>
            <AnalyzersMonitor />
          </div>
        )}

        {activeTab === 'users' && (
          <div className={styles.tabContent}>
            <UserManagement />
          </div>
        )}

        {activeTab === 'logs' && (
          <div className={styles.tabContent}>
            <h2>
              📋 Logs do Sistema
              <span className="tab-description">{activeTabInfo?.description}</span>
            </h2>
            <SystemLogs />
          </div>
        )}
      </main>

      {/* Footer Moderno */}
      <footer className={styles.adminFooter}>
        <div className={styles.footerInfo}>
          <span>
            Portal Betina V3 - Sistema Administrativo
            <small>v3.0.0</small>
          </span>
          <div className="footer-stats">
            <span>Aba ativa: {activeTabInfo?.label}</span>
            <span>Status: {systemStatus}</span>
            <span>Última atualização: {new Date().toLocaleString()}</span>
          </div>
        </div>
      </footer>
    </div>
  )
}

export default AdminDashboard
