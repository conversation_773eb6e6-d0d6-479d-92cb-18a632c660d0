/**
 * @file AdminDashboard.jsx
 * @description Dashboard Administrativo Principal - Portal Betina V3
 * @version 3.0.0
 * @admin true
 */

import React, { useState, useEffect } from 'react'
import { IntegratedSystemDashboard } from './IntegratedSystemDashboard/IntegratedSystemDashboard'
import { SystemHealthMonitor } from './SystemHealthMonitor/SystemHealthMonitor'
import { AnalyzersMonitor } from './AnalyzersMonitor/AnalyzersMonitor'
import { UserManagement } from './UserManagement/UserManagement'
import { SystemLogs } from './SystemLogs/SystemLogs'
import styles from './AdminDashboard.module.css'

const AdminDashboard = ({ onBack }) => {
  const [activeTab, setActiveTab] = useState('system')
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loginInput, setLoginInput] = useState('')
  const [loginError, setLoginError] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  // Senha admin - em produção usar hash/JWT
  const ADMIN_PASSWORD = 'betina2025'

  const handleLogin = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setLoginError('')

    // Simular delay de autenticação
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (loginInput === ADMIN_PASSWORD) {
      setIsAuthenticated(true)
      localStorage.setItem('adminAuth', 'true')
    } else {
      setLoginError('Senha incorreta')
    }
    setIsLoading(false)
  }

  const handleLogout = () => {
    setIsAuthenticated(false)
    localStorage.removeItem('adminAuth')
    setLoginInput('')
  }

  // Verificar autenticação salva
  useEffect(() => {
    const savedAuth = localStorage.getItem('adminAuth')
    if (savedAuth === 'true') {
      setIsAuthenticated(true)
    }
  }, [])

  const tabs = [
    { id: 'system', label: 'Sistema Integrado', icon: '🖥️' },
    { id: 'health', label: 'Saúde do Sistema', icon: '🏥' },
    { id: 'analyzers', label: 'Analisadores', icon: '🔬' },
    { id: 'users', label: 'Usuários', icon: '👥' },
    { id: 'logs', label: 'Logs', icon: '📋' }
  ]

  if (!isAuthenticated) {
    return (
      <div className={styles.loginContainer}>
        <div className={styles.loginBox}>
          <h2>🔐 Acesso Administrativo</h2>
          <p>Portal Betina V3 - Área Restrita</p>
          
          <form onSubmit={handleLogin} className={styles.loginForm}>
            <div className={styles.inputGroup}>
              <label htmlFor="password">Senha de Administrador:</label>
              <input
                id="password"
                type="password"
                value={loginInput}
                onChange={(e) => setLoginInput(e.target.value)}
                placeholder="Digite a senha admin"
                disabled={isLoading}
                className={styles.passwordInput}
              />
            </div>
            
            {loginError && (
              <div className={styles.error}>
                ❌ {loginError}
              </div>
            )}
            
            <div className={styles.buttonGroup}>
              <button 
                type="submit" 
                disabled={isLoading || !loginInput.trim()}
                className={styles.loginButton}
              >
                {isLoading ? '🔄 Verificando...' : '🔓 Entrar'}
              </button>
              
              <button 
                type="button" 
                onClick={onBack}
                className={styles.backButton}
              >
                ← Voltar
              </button>
            </div>
          </form>
          
          <div className={styles.loginHint}>
            💡 Dica: A senha padrão é "betina2025"
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.adminContainer}>
      {/* Header Admin */}
      <header className={styles.adminHeader}>
        <div className={styles.headerLeft}>
          <h1>🛠️ Painel Administrativo</h1>
          <span className={styles.version}>Portal Betina V3</span>
        </div>
        
        <div className={styles.headerRight}>
          <span className={styles.adminUser}>👤 Administrador</span>
          <button 
            onClick={handleLogout}
            className={styles.logoutButton}
          >
            🚪 Sair
          </button>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className={styles.tabNavigation}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`${styles.tabButton} ${activeTab === tab.id ? styles.active : ''}`}
          >
            <span className={styles.tabIcon}>{tab.icon}</span>
            <span className={styles.tabLabel}>{tab.label}</span>
          </button>
        ))}
      </nav>

      {/* Content Area */}
      <main className={styles.adminContent}>
        {activeTab === 'system' && (
          <div className={styles.tabContent}>
            <h2>🖥️ Dashboard do Sistema Integrado</h2>
            <IntegratedSystemDashboard />
          </div>
        )}

        {activeTab === 'health' && (
          <div className={styles.tabContent}>
            <h2>🏥 Monitoramento de Saúde</h2>
            <SystemHealthMonitor />
          </div>
        )}

        {activeTab === 'analyzers' && (
          <div className={styles.tabContent}>
            <h2>🔬 Monitor de Analisadores</h2>
            <AnalyzersMonitor />
          </div>
        )}

        {activeTab === 'users' && (
          <div className={styles.tabContent}>
            <h2>👥 Gerenciamento de Usuários</h2>
            <UserManagement />
          </div>
        )}

        {activeTab === 'logs' && (
          <div className={styles.tabContent}>
            <h2>📋 Logs do Sistema</h2>
            <SystemLogs />
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className={styles.adminFooter}>
        <div className={styles.footerInfo}>
          <span>Portal Betina V3 - Sistema Administrativo</span>
          <span>Última atualização: {new Date().toLocaleString()}</span>
        </div>
      </footer>
    </div>
  )
}

export default AdminDashboard
