/**
 * @file useUnifiedGameLogic.js
 * @description Hook unificado para lógica de jogos - Portal Betina V3
 * @version 3.0.0 - Integrado com PortalBetinaV3 conforme arquitetura
 */

import { useState, useEffect, useContext, useRef, useCallback } from 'react'
import { SystemContext } from '../components/context/SystemContext.jsx'
import { getPortalBetinaV3 } from '../api/services/PortalBetinaV3.js'
import { v4 as uuidv4 } from 'uuid'

/**
 * Hook unificado para lógica de jogos
 * INTEGRAÇÃO COMPLETA: PortalBetinaV3 → MultisensoryCollector → PredictiveEngine → AdvancedMetrics
 */
export function useUnifiedGameLogic(gameType) {
  // Usar context de forma mais resiliente
  let systemContext = null;
  let user = null;
  let ttsEnabled = true;
  
  try {
    systemContext = useContext(SystemContext);
    if (systemContext) {
      user = systemContext.user;
      ttsEnabled = systemContext.ttsEnabled !== undefined ? systemContext.ttsEnabled : true;
    }
  } catch (error) {
    console.warn('⚠️ SystemContext not available, using defaults:', error.message);
  }
  
  const [sessionId, setSessionId] = useState(null)
  const [isSessionActive, setIsSessionActive] = useState(false)
  const [sessionMetrics, setSessionMetrics] = useState({})
  const portalRef = useRef(null)
  
  // Estados básicos do jogo (mantidos para compatibilidade)
  const [gameState, setGameState] = useState({
    score: 0,
    round: 1,
    accuracy: 100,
    totalCorrect: 0,
    totalAttempts: 0,
    difficulty: 'medium'
  })

  /**
   * Inicializa Portal Betina V3
   */
  useEffect(() => {
    const initializePortal = async () => {
      try {
        portalRef.current = await getPortalBetinaV3()
        console.log('✅ Portal Betina V3 connected to', gameType)
      } catch (error) {
        console.error('❌ Failed to initialize Portal Betina V3:', error)
      }
    }
    
    initializePortal()
  }, [gameType])

  /**
   * Cleanup: Finalizar sessão ativa quando componente for desmontado
   */
  useEffect(() => {
    return () => {
      if (sessionId && isSessionActive) {
        console.log('🧹 Cleaning up active session on unmount:', sessionId);
        
        // Finalização assíncrona sem bloquear o unmount
        (async () => {
          try {
            if (portalRef.current) {
              const result = await portalRef.current.finalizeSession(sessionId, { 
                reason: 'component_unmount',
                endTime: new Date().toISOString()
              });
              
              if (result.success) {
                console.log('✅ Session cleanup completed successfully');
              } else {
                console.warn('⚠️ Session cleanup completed with warnings:', result.message);
              }
            }
          } catch (error) {
            console.warn('⚠️ Error during session cleanup (non-critical):', error.message);
          }
        })();
      }
    };
  }, [sessionId, isSessionActive])

  /**
   * FUNÇÃO PRINCIPAL: Inicia sessão de jogo
   * Integra com PortalBetinaV3.startGameSession()
   */
  const startUnifiedSession = useCallback(async (difficulty = 'medium') => {
    try {
      if (!portalRef.current) {
        console.debug('🔄 Portal not ready, starting local session for', gameType)
        
        // Iniciar sessão local se portal não estiver pronto
        const localSessionId = `local-${gameType}-${uuidv4()}`
        setSessionId(localSessionId)
        setIsSessionActive(true)
        setSessionMetrics({
          sessionId: localSessionId,
          gameType,
          userId: user?.id || 'anonymous',
          difficulty,
          startTime: new Date().toISOString(),
          mode: 'local'
        })
        
        setGameState(prev => ({
          ...prev,
          difficulty,
          sessionId: localSessionId
        }))
        
        console.log(`🎮 Started local session for ${gameType}:`, localSessionId)
        return { 
          success: true, 
          sessionId: localSessionId,
          mode: 'local',
          multisensoryEnabled: false,
          predictiveEnabled: false
        }
      }
      
      const userId = user?.id || 'anonymous'
      const result = await portalRef.current.startGameSession(userId, gameType, difficulty)
      
      if (result.success) {
        setSessionId(result.sessionId)
        setIsSessionActive(true)
        setSessionMetrics({
          sessionId: result.sessionId,
          gameType,
          userId,
          difficulty,
          startTime: new Date().toISOString(),
          multisensoryEnabled: result.multisensoryEnabled,
          predictiveEnabled: result.predictiveEnabled,
          mode: 'portal'
        })
        
        setGameState(prev => ({
          ...prev,
          difficulty,
          sessionId: result.sessionId
        }))
        
        console.log(`🎮 Started unified session for ${gameType}:`, result.sessionId)
        return result
      }
      
      throw new Error(result.error || 'Failed to start session')
      
    } catch (error) {
      console.error('❌ Failed to start unified session, falling back to local:', error)
      
      // Fallback para sessão local em caso de erro
      const fallbackSessionId = `fallback-${gameType}-${uuidv4()}`
      setSessionId(fallbackSessionId)
      setIsSessionActive(true)
      setSessionMetrics({
        sessionId: fallbackSessionId,
        gameType,
        userId: user?.id || 'anonymous',
        difficulty,
        startTime: new Date().toISOString(),
        mode: 'fallback',
        error: error.message
      })
      
      setGameState(prev => ({
        ...prev,
        difficulty,
        sessionId: fallbackSessionId
      }))
      
      return { 
        success: true, 
        sessionId: fallbackSessionId,
        mode: 'fallback',
        multisensoryEnabled: false,
        predictiveEnabled: false,
        warning: 'Session started in fallback mode'
      }
    }
  }, [gameType, user])

  /**
   * FUNÇÃO PRINCIPAL: Registra interação do jogo
   * Integra com PortalBetinaV3.recordGameAction()
   */
  const recordInteraction = useCallback(async (actionType, data, isCorrect = null, duration = 0) => {
    try {
      // Se portal ou sessão não estão disponíveis, registrar localmente
      if (!portalRef.current || !sessionId) {
        // Não mostrar warning desnecessariamente, apenas debug
        console.debug('📝 Recording interaction locally (portal/session not ready):', {
          actionType,
          gameType,
          isCorrect,
          duration
        })
        
        // Atualizar estado local mesmo sem sessão ativa
        setGameState(prev => {
          const newTotalAttempts = prev.totalAttempts + 1
          const newTotalCorrect = prev.totalCorrect + (isCorrect ? 1 : 0)
          const newAccuracy = newTotalAttempts > 0 ? Math.round((newTotalCorrect / newTotalAttempts) * 100) : 100
          
          return {
            ...prev,
            totalAttempts: newTotalAttempts,
            totalCorrect: newTotalCorrect,
            accuracy: newAccuracy,
            score: prev.score + (isCorrect ? 10 : 0)
          }
        })
        
        return { 
          success: true, 
          mode: 'local',
          message: 'Interaction recorded locally' 
        }
      }
      
      const action = {
        type: actionType,
        data,
        isCorrect,
        duration,
        timestamp: new Date().toISOString(),
        gameType,
        round: gameState.round
      }
      
      const result = await portalRef.current.recordGameAction(sessionId, action)
      
      if (result.success) {
        // Atualizar estado do jogo
        setGameState(prev => {
          const newTotalAttempts = prev.totalAttempts + 1
          const newTotalCorrect = prev.totalCorrect + (isCorrect ? 1 : 0)
          const newAccuracy = newTotalAttempts > 0 ? Math.round((newTotalCorrect / newTotalAttempts) * 100) : 100
          
          return {
            ...prev,
            totalAttempts: newTotalAttempts,
            totalCorrect: newTotalCorrect,
            accuracy: newAccuracy,
            score: prev.score + (isCorrect ? 10 : 0)
          }
        })
        
        // Atualizar métricas da sessão
        setSessionMetrics(prev => ({
          ...prev,
          lastAction: action,
          totalInteractions: (prev.totalInteractions || 0) + 1,
          hasMultisensoryData: result.multisensoryData?.captured || false,
          hasPredictiveAnalysis: result.predictions?.available || false
        }))
        
        console.log(`📊 Recorded interaction: ${actionType}`, result.analysis?.processed ? '✅' : '⚠️')
        return result
      }
      
      throw new Error(result.error || 'Failed to record interaction')
      
    } catch (error) {
      console.error('❌ Failed to record interaction:', error)
      return { success: false, error: error.message }
    }
  }, [sessionId, gameType, gameState.round])

  /**
   * FUNÇÃO PRINCIPAL: Finaliza sessão
   * Integra com PortalBetinaV3.finalizeSession()
   */
  const endUnifiedSession = useCallback(async (finalData = {}) => {
    try {
      // Verificar se há sessão ativa para finalizar
      if (!sessionId || !isSessionActive) {
        console.log('ℹ️ No active session to finalize or session already finalized');
        return { success: true, message: 'No active session or session already finalized' };
      }
      
      // Se portal não está disponível, finalizar localmente
      if (!portalRef.current) {
        console.log('🔄 Finalizing session locally (portal not available)');
        setIsSessionActive(false);
        setSessionMetrics(prev => ({
          ...prev,
          endTime: new Date().toISOString(),
          finalData
        }));
        return { success: true, mode: 'local', message: 'Session finalized locally' };
      }
      
      const result = await portalRef.current.finalizeSession(sessionId, finalData)
      
      if (result.success) {
        setIsSessionActive(false)
        setSessionMetrics(prev => ({
          ...prev,
          endTime: new Date().toISOString(),
          finalReport: result.report,
          therapeuticAnalysis: result.report?.therapeuticAnalysis,
          multisensoryReport: result.report?.multisensoryReport,
          predictions: result.report?.futurePredictions,
          finalData
        }))
        
        console.log(`🏁 Finalized session ${sessionId}:`, {
          multisensory: !!result.report?.multisensoryReport,
          therapeutic: !!result.report?.therapeuticAnalysis,
          predictive: !!result.report?.futurePredictions
        })
        
        return result
      }
      
      throw new Error(result.error || 'Failed to finalize session')
      
    } catch (error) {
      console.warn('⚠️ Session finalization error (may already be finalized):', error.message);
      
      // Marcar como inativa mesmo com erro para evitar loops
      setIsSessionActive(false);
      
      return { 
        success: false, 
        error: error.message,
        gracefulFailure: true 
      };
    }
  }, [sessionId, isSessionActive])

  /**
   * Reseta sessão
   */
  const resetSession = useCallback(() => {
    setSessionId(null)
    setIsSessionActive(false)
    setSessionMetrics({})
    setGameState({
      score: 0,
      round: 1,
      accuracy: 100,
      totalCorrect: 0,
      totalAttempts: 0,
      difficulty: 'medium'
    })
    
    console.log('🔄 Session reset for', gameType)
  }, [gameType])

  /**
   * Obtém status da sessão
   */
  const getSessionStatus = useCallback(() => {
    if (!portalRef.current || !sessionId) {
      return { active: false }
    }
    
    return {
      active: isSessionActive,
      sessionId,
      gameType,
      metrics: sessionMetrics,
      gameState,
      portalStatus: portalRef.current.getSessionStatus(sessionId)
    }
  }, [sessionId, isSessionActive, sessionMetrics, gameState, gameType])

  /**
   * Repetir instrução (com TTS)
   */
  const repeatInstruction = useCallback(async (text) => {
    // Registrar uso de acessibilidade
    await recordInteraction('instruction_repeat', {
      text,
      accessibility: true,
      ttsEnabled
    })
    
    if (ttsEnabled && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text)
      utterance.lang = 'pt-BR'
      utterance.rate = 0.8
      speechSynthesis.speak(utterance)
    } else {
      alert(text)
    }
  }, [recordInteraction, ttsEnabled])

  return {
    // Estados do jogo
    gameState,
    setGameState,
    sessionMetrics,
    isSessionActive,
    sessionId,
    
    // Funções principais do Portal Betina V3
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    resetSession,
    getSessionStatus,
    
    // Funções auxiliares
    repeatInstruction,
    
    // Compatibilidade com sistemas existentes
    ttsEnabled,
    user,
    
    // Status do Portal
    portalReady: !!portalRef.current
  }
}

export default useUnifiedGameLogic
