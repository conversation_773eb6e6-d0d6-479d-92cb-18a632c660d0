/**
 * @file DashboardContainer.module.css
 * @description Estilos modulares para o container dos dashboards
 * @version 3.0.0
 */

/* Container principal */
.dashboardContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  z-index: 1;
  /* Garantir que o dashboard permaneça opaco */
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;
}

/* Estado quando o dashboard está "subindo" - deve sumir completamente */
.dashboardContainer.hidden {
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
}

/* Estado quando o dashboard está "descendo" - deve aparecer completamente */
.dashboardContainer.visible {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Botão de voltar */
.backButton {
  position: absolute;
  top: 1rem;
  left: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: none;
  border-radius: 8px;
  padding: 0.7rem 1rem;
  color: white;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 900;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
}

/* Header do dashboard */
.dashboardHeader {  padding: 5rem 2rem 2rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.dashboardTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #fff, #f0f9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-flex;
  align-items: center;
  gap: 1rem;
}

.premiumBadge {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 10px rgba(245, 158, 11, 0.3);
}

.dashboardSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 1.5rem;
  font-weight: 300;
}

/* Navegação por abas - FIXO */
.dashboardTabs {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  /* Garantir que as abas sejam sempre visíveis e opacas */
  opacity: 1;
  visibility: visible;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Conteúdo do dashboard - com margem para não ficar atrás do menu */
.dashboardContent {
  min-height: 100vh;
  position: relative;
  background: transparent;
  padding: 0;
  margin-top: 120px; /* Espaço para o menu fixo */
  z-index: 50; /* Menor que o menu */
}

.dashboardTab {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 1rem 2rem;
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-transform: capitalize;
  white-space: nowrap;
}

.dashboardTab:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.dashboardTab.active {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

.dashboardTab[aria-pressed="true"] {
  background: linear-gradient(135deg, #10b981, #047857);
  border-color: #10b981;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
}

/* Conteúdo do dashboard */
.dashboardContent {
  min-height: 60vh;
  position: relative;
  background: transparent;
  padding: 0;
  /* Garantir que o conteúdo não fique transparente */
  opacity: 1;
  visibility: visible;
  transition: opacity 0.3s ease-in-out;
}

/* Quando o dashboard está oculto, o conteúdo também deve sumir */
.dashboardContainer.hidden .dashboardContent {
  opacity: 0;
  visibility: hidden;
}

/* Wrapper para cada dashboard individual */
.dashboardWrapper {
  width: 100%;
  height: 100%;
  background: transparent;
  overflow-x: hidden;
}

/* Remover espaçamentos desnecessários para melhor integração */
.dashboardContent > div {
  margin: 0;
  padding: 0;
}

.dashboardWrapper > div {
  max-width: 100%;
  box-sizing: border-box;
}

/* Tela de login premium já está definida inline no componente */

/* Responsividade */
@media (max-width: 768px) {
  .dashboardHeader {
    padding: 5rem 1rem 1.5rem 1rem;
  }
  
  .dashboardTitle {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .dashboardSubtitle {
    font-size: 1rem;
  }
  
  .dashboardTabs {
    padding: 1rem;
    gap: 0.5rem;
  }
  
  .dashboardTab {
    padding: 0.8rem 1.5rem;
    font-size: 0.8rem;
  }
  
  .backButton {
    top: 1rem;
    left: 1rem;
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .dashboardTitle {
    font-size: 1.8rem;
  }
  
  .dashboardTabs {
    flex-direction: column;
    align-items: center;
    padding: 0.8rem;
  }
  
  .dashboardTab {
    width: 100%;
    max-width: 300px;
    justify-content: center;
    padding: 0.7rem 1rem;
    font-size: 0.75rem;
  }
  
  .dashboardContent {
    margin-top: 140px; /* Ajustar para tabs em coluna */
  }
}

/* Estilos para tela de login compacta */
.loginContainer {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  padding: 1rem;
}

.loginBox {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-width: 360px;
  width: 100%;
}

@media (max-width: 480px) {
  .loginBox {
    padding: 1.5rem;
    max-width: 320px;
    border-radius: 12px;
  }
}

.loginInput {
  width: 100%;
  padding: 0.8rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.loginInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.loginInput:focus {
  outline: none;
  border-color: rgba(59, 130, 246, 0.8);
  background: rgba(255, 255, 255, 0.15);
}

.loginButton {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: 100%;
}

.loginButton:disabled {
  background: rgba(107, 114, 128, 0.8);
  cursor: not-allowed;
}

.loginButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #16a34a, #15803d);
  transform: translateY(-1px);
}
