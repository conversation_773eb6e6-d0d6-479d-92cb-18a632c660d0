/**
 * 🎨 COLOR MATCH V3 - <PERSON>OGO DE CORES COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { ColorMatchCollectorsHub } from './collectors/index.js';
import { ColorMatchConfig } from './ColorMatchConfig.js';
import { ColorMatchMetrics } from './ColorMatchMetrics.js';
import styles from './ColorMatch.module.css';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - COLOR MATCH
const ACTIVITY_TYPES = {
  SPEED_CHALLENGE: {
    id: 'speed_challenge',
    name: 'Desafio de Velocidade',
    icon: '⚡',
    description: 'Identifique cores rapidamente',
    component: 'SpeedChallengeActivity'
  },
  COLOR_MEMORY: {
    id: 'color_memory',
    name: 'Memória de Cores',
    icon: '🧠',
    description: 'Lembre-se da sequência de cores',
    component: 'ColorMemoryActivity'
  },
  SHADE_DISCRIMINATION: {
    id: 'shade_discrimination',
    name: 'Discriminação de Tons',
    icon: '🔍',
    description: 'Diferencie tons similares',
    component: 'ShadeDiscriminationActivity'
  },
  SEQUENCE_COLORS: {
    id: 'sequence_colors',
    name: 'Sequência de Cores',
    icon: '📝',
    description: 'Complete sequências coloridas',
    component: 'SequenceColorsActivity'
  },
  GRADIENT_MATCHING: {
    id: 'gradient_matching',
    name: 'Combinação de Gradientes',
    icon: '🌈',
    description: 'Combine gradientes e transições',
    component: 'GradientMatchingActivity'
  }
};

function ColorMatchGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Hook unificado para lógica de jogos - integração com backend
  const {
    startUnifiedSession,
    endUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId,
    isSessionActive
  } = useUnifiedGameLogic('colormatch');

  const [showStartScreen, setShowStartScreen] = useState(true);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    score: 0,
    round: 1,
    targetColor: null,
    selectedAnswer: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'easy',
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.SPEED_CHALLENGE.id,
    activityCycle: [
      ACTIVITY_TYPES.SPEED_CHALLENGE.id,
      ACTIVITY_TYPES.COLOR_MEMORY.id,
      ACTIVITY_TYPES.SHADE_DISCRIMINATION.id,
      ACTIVITY_TYPES.SEQUENCE_COLORS.id,
      ACTIVITY_TYPES.GRADIENT_MATCHING.id
    ],
    activityIndex: 0,
    roundsPerActivity: 10,
    activityRoundCount: 1, // 🔥 Contador de rounds na atividade atual
    minRoundsPerActivity: 4, // 🔥 Mínimo de rounds antes de poder trocar
    maxRoundsPerActivity: 7, // 🔥 Máximo recomendado de rounds por atividade
    canSwitchActivity: false, // 🔥 Só pode trocar após mínimo de rounds
    
    // 🎯 Dados específicos de atividades
    activityData: {
      speedChallenge: { currentQuestion: null },
      colorMemory: { currentQuestion: null },
      shadeDiscrimination: { currentQuestion: null },
      sequenceColors: { currentQuestion: null },
      gradientMatching: { currentQuestion: null }
    }
  });

  // TTS state
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('colorMatch_ttsActive');
    return saved ? JSON.parse(saved) : ttsEnabled;
  });

  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('colorMatch_ttsActive', JSON.stringify(newState));
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);

  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) return;
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  const shuffleArray = useCallback((array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);

  const generateNameTheColor = (config) => {
    const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#FFA500'];
    const colorNames = ['Vermelho', 'Verde', 'Azul', 'Amarelo', 'Magenta', 'Laranja'];
    const targetIndex = Math.floor(Math.random() * colors.length);
    const targetColor = colors[targetIndex];
    const correctAnswer = colorNames[targetIndex];
    
    // Criar opções de nomes diferentes
    const wrongNames = colorNames.filter(name => name !== correctAnswer);
    const shuffledWrong = wrongNames.sort(() => Math.random() - 0.5).slice(0, 3);
    const allOptions = [correctAnswer, ...shuffledWrong].sort(() => Math.random() - 0.5);
    
    return {
      targetColor,
      colorName: correctAnswer,
      options: allOptions,
      correctAnswer,
      instruction: 'Qual é o nome desta cor?',
      type: 'name_the_color'
    };
  };

  const startGame = useCallback((difficulty) => {
    setShowStartScreen(false);
    setGameState(prev => ({
      ...prev,
      difficulty,
      roundStartTime: Date.now(),
      currentActivity: ACTIVITY_TYPES.SPEED_CHALLENGE.id,
      activityData: {
        ...prev.activityData,
        speedChallenge: generateNameTheColor(ColorMatchConfig.difficulties[difficulty] || ColorMatchConfig.difficulties.easy)
      }
    }));
    
    if (startUnifiedSession) {
      startUnifiedSession(difficulty);
    }
    
    setTimeout(() => {
      speak(`Bem-vindo ao Color Match! Vamos começar com Desafio de Velocidade. Identifique cores rapidamente.`);
    }, 1000);
  }, [startUnifiedSession, speak]);

  const handleAnswer = useCallback((answer) => {
    setGameState(prev => {
      const currentActivityData = prev.activityData[prev.currentActivity.replace('_', '')];
      const isCorrect = answer === currentActivityData.correctAnswer;
      
      return {
        ...prev,
        selectedAnswer: answer,
        showFeedback: true,
        score: isCorrect ? prev.score + 10 : prev.score
      };
    });
  }, []);

  const generateNewRound = useCallback(() => {
    setGameState(prev => {
      const newRound = prev.round + 1;
      const newActivityRoundCount = prev.activityRoundCount + 1;
      const newActivityContent = generateNameTheColor(ColorMatchConfig.difficulties[prev.difficulty] || ColorMatchConfig.difficulties.easy);
      
      return {
        ...prev,
        round: newRound,
        activityRoundCount: newActivityRoundCount,
        canSwitchActivity: newActivityRoundCount >= prev.minRoundsPerActivity,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [prev.currentActivity.replace('_', '')]: newActivityContent
        }
      };
    });
  }, []);

  const switchActivity = useCallback((activityId) => {
    setGameState(prev => {
      const newActivityContent = generateNameTheColor(ColorMatchConfig.difficulties[prev.difficulty] || ColorMatchConfig.difficulties.easy);
      
      return {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 1,
        canSwitchActivity: false,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [activityId.replace('_', '')]: newActivityContent
        }
      };
    });
  }, []);

  const renderSpeedChallengeActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          ⏱️ Responda rapidamente!
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div 
          className={styles.countingObject}
          style={{ 
            backgroundColor: data.targetColor,
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '2rem auto',
            border: '4px solid rgba(255,255,255,0.3)',
            boxShadow: '0 8px 25px rgba(0,0,0,0.3)'
          }}
        />
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option, index) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            style={{ fontSize: '1.1rem', padding: '1rem 1.5rem' }}
          >
            {option}
          </button>
        ))}
      </div>
    </div>
  );

  const renderActivityContent = () => {
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace('_', '')];
    if (!currentActivityData) return null;

    return renderSpeedChallengeActivity(currentActivityData);
  };

  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="🎨 Color Match V3"
        gameDescription="Jogo educativo de cores com múltiplas atividades"
        onStart={startGame}
        onBack={onBack}
        difficulties={[
          { id: 'easy', name: 'Fácil', description: 'Cores básicas e simples' },
          { id: 'medium', name: 'Médio', description: 'Cores variadas com alguns desafios' },
          { id: 'hard', name: 'Difícil', description: 'Cores complexas e tons similares' }
        ]}
      />
    );
  }

  return (
    <div className={styles.gameContainer}>
      <div className={styles.gameBoard}>
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🎨 Color Match V3
            <span className={styles.difficultyBadge}>
              {gameState.difficulty?.toUpperCase()}
            </span>
          </h1>
          
          <button 
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Menu de Atividades */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${gameState.currentActivity === activity.id ? styles.active : ''} ${!gameState.canSwitchActivity && gameState.currentActivity !== activity.id ? styles.disabled : ''}`}
              onClick={() => gameState.canSwitchActivity || gameState.currentActivity === activity.id ? switchActivity(activity.id) : null}
              disabled={!gameState.canSwitchActivity && gameState.currentActivity !== activity.id}
              title={
                !gameState.canSwitchActivity && gameState.currentActivity !== activity.id 
                  ? `Complete pelo menos ${gameState.minRoundsPerActivity} rounds na atividade atual antes de trocar`
                  : activity.description
              }
            >
              {activity.icon} {activity.name}
            </button>
          ))}
        </div>

        {/* Estatísticas do jogo */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.activityRoundCount}/{gameState.maxRoundsPerActivity}</div>
            <div className={styles.statLabel}>
              Rounds Atividade
              {gameState.activityRoundCount < gameState.minRoundsPerActivity && (
                <div style={{ fontSize: '0.7rem', color: '#ffa500' }}>
                  Mín: {gameState.minRoundsPerActivity}
                </div>
              )}
            </div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
        </div>

        {renderActivityContent()}

        {gameState.showFeedback && (
          <div className={styles.feedbackArea}>
            <div className={`${styles.feedbackMessage} ${gameState.selectedAnswer === gameState.activityData[gameState.currentActivity.replace('_', '')].correctAnswer ? styles.correct : styles.incorrect}`}>
              {gameState.selectedAnswer === gameState.activityData[gameState.currentActivity.replace('_', '')].correctAnswer ? '✅ Correto!' : '❌ Tente novamente!'}
            </div>
            
            <button 
              className={styles.nextButton}
              onClick={generateNewRound}
            >
              ➡️ Próxima Rodada
            </button>
          </div>
        )}

        <div className={styles.gameControls}>
          <button 
            className={styles.controlButton}
            onClick={onBack}
          >
            🏠 Finalizar Jogo
          </button>
        </div>
      </div>
    </div>
  );
}

export default ColorMatchGame;
