/**
 * @file SystemHealthMonitor.module.css
 * @description Estilos para o Monitor de Saúde do Sistema
 * @version 1.0.0
 */

.systemHealthMonitor {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.lastUpdate {
  color: #666;
  font-size: 0.9rem;
}

.refreshButton {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.refreshButton:hover {
  background: #0056b3;
}

.refreshButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.overallStatus {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.statusHealthy {
  border-left: 4px solid #28a745;
}

.statusWarning {
  border-left: 4px solid #ffc107;
}

.statusCritical {
  border-left: 4px solid #dc3545;
}

.statusIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.statusText {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.statusHealthyText {
  color: #28a745;
}

.statusWarningText {
  color: #ffc107;
}

.statusCriticalText {
  color: #dc3545;
}

.statusDescription {
  color: #666;
  font-size: 1rem;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.metricCard {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.metricHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.metricTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.metricStatus {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.statusOk {
  background: #d4edda;
  color: #155724;
}

.statusWarning {
  background: #fff3cd;
  color: #856404;
}

.statusError {
  background: #f8d7da;
  color: #721c24;
}

.metricValue {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.5rem;
}

.metricDescription {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  transition: width 0.3s ease;
}

.progressOk {
  background: #28a745;
}

.progressWarning {
  background: #ffc107;
}

.progressCritical {
  background: #dc3545;
}

.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.serviceCard {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.serviceIcon {
  font-size: 2rem;
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.serviceStatus {
  font-size: 0.9rem;
}

.serviceHealthy {
  color: #28a745;
}

.serviceDown {
  color: #dc3545;
}

.serviceDegraded {
  color: #ffc107;
}

.alertsSection {
  margin-top: 2rem;
}

.alertsTitle {
  font-size: 1.3rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.alertsList {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.alertItem {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.alertItem:last-child {
  border-bottom: none;
}

.alertIcon {
  font-size: 1.2rem;
  margin-top: 0.2rem;
}

.alertCritical .alertIcon {
  color: #dc3545;
}

.alertWarning .alertIcon {
  color: #ffc107;
}

.alertInfo .alertIcon {
  color: #17a2b8;
}

.alertContent {
  flex: 1;
}

.alertTitle {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.25rem;
}

.alertMessage {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.alertTime {
  color: #999;
  font-size: 0.8rem;
}

.noAlerts {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
  }

  .servicesGrid {
    grid-template-columns: 1fr;
  }

  .overallStatus {
    padding: 1rem;
  }

  .statusIcon {
    font-size: 2rem;
  }

  .statusText {
    font-size: 1.2rem;
  }
}
