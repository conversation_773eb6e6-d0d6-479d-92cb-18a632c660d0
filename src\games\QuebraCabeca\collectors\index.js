/**
 * 🧩 QUEBRA-CABEÇA COLLECTORS HUB V3
 * Hub central para gerenciamento de coletores de dados do jogo Quebra-Cabeça V3
 * Portal Betina V3 - Sistema Integrado de Análise Terapêutica
 */

import { FreeAssemblyCollector } from './FreeAssemblyCollector.js';
import { GuidedAssemblyCollector } from './GuidedAssemblyCollector.js';
import { RotationReconstructionCollector } from './RotationReconstructionCollector.js';
import { PieceClassificationCollector } from './PieceClassificationCollector.js';
import { PatternIdentificationCollector } from './PatternIdentificationCollector.js';
import { CollaborativeSolvingCollector } from './CollaborativeSolvingCollector.js';
import { SpatialReasoningCollector } from './SpatialReasoningCollector.js';
import { ProblemSolvingCollector } from './ProblemSolvingCollector.js';
import { VisualProcessingCollector } from './VisualProcessingCollector.js';
import { MotorSkillsCollector } from './MotorSkillsCollector.js';
import { ErrorPatternCollector } from './ErrorPatternCollector.js';

export class QuebraCabecaCollectorsHub {
  constructor() {
    this.name = 'QuebraCabecaCollectorsHub';
    this.version = '3.0.0';
    this.gameType = 'quebra_cabeca';
    this.isActive = true;
    
    // Sistema de coletores V3 especializados
    this._collectors = {
      freeAssembly: new FreeAssemblyCollector(),
      guidedAssembly: new GuidedAssemblyCollector(),
      rotationReconstruction: new RotationReconstructionCollector(),
      pieceClassification: new PieceClassificationCollector(),
      patternIdentification: new PatternIdentificationCollector(),
      collaborativeSolving: new CollaborativeSolvingCollector()
    };
    
    // Configurações do hub V3
    this.hubConfig = {
      enableCrossActivityAnalysis: true,
      enableTherapeuticInsights: true,
      enableRealTimeAnalysis: true,
      dataRetentionDays: 90,
      autoAnalysisEnabled: true,
      exportFormats: ['json', 'csv', 'xml', 'therapeutic_report'],
      therapeuticMode: true,
      crossActivityCorrelation: true,
      advancedMetrics: true,
      adaptiveDifficulty: true,
      progressTracking: true
    };
    
    // Dados da sessão V3
    this.sessionData = {
      sessionId: null,
      startTime: null,
      endTime: null,
      currentActivity: null,
      activityHistory: [],
      gameData: [],
      interactions: [],
      metrics: {},
      therapeuticProfile: {},
      progressIndicators: {},
      adaptationHistory: []
    };
    
    // Métricas integradas V3
    this.integratedMetrics = {
      spatialAbilities: {
        mentalRotation: [],
        spatialVisualization: [],
        spatialMemory: [],
        spatialOrientation: []
      },
      problemSolving: {
        strategicPlanning: [],
        analyticalThinking: [],
        creativeApproaches: [],
        persistenceIndicators: []
      },
      cognitiveFlexibility: {
        taskSwitching: [],
        setShifting: [],
        inhibitoryControl: [],
        workingMemoryUpdate: []
      },
      socialCognition: {
        collaborationSkills: [],
        communicationPatterns: [],
        empathyIndicators: [],
        leadershipBehaviors: []
      },
      therapeuticOutcomes: {
        engagementLevel: [],
        emotionalRegulation: [],
        selfEfficacy: [],
        motivationPatterns: []
      }
    };
    
    console.log('🧩 QuebraCabecaCollectorsHub V3 inicializado com 6 coletores especializados');
  }

  /**
   * Getter para coletores - compatibilidade com sistema existente
   */
  get collectors() {
    return this._collectors;
  }

  /**
   * Inicializar sessão de coleta V3
   */
  initializeSession(sessionId = null, userId = null, therapeuticMode = true) {
    this.sessionData.sessionId = sessionId || `quebracabeca_v3_${Date.now()}`;
    this.sessionData.userId = userId;
    this.sessionData.startTime = Date.now();
    this.sessionData.currentActivity = null;
    this.sessionData.activityHistory = [];
    this.sessionData.gameData = [];
    this.sessionData.interactions = [];
    this.sessionData.therapeuticMode = therapeuticMode;
    this.isActive = true;
    
    // Resetar métricas para nova sessão
    this.resetSessionMetrics();
    
    console.log(`🧩 Sessão QuebraCabeca V3 ${this.sessionData.sessionId} iniciada (Modo Terapêutico: ${therapeuticMode})`);
    
    return {
      sessionId: this.sessionData.sessionId,
      startTime: this.sessionData.startTime,
      collectorsActive: Object.keys(this._collectors).length,
      therapeuticMode: therapeuticMode
    };
  }

  /**
   * Coletar dados de uma atividade específica V3
   */
  async collect(gameData) {
    if (!this.isActive) {
      console.warn('⚠️ QuebraCabeca CollectorsHub não está ativo');
      return { collected: false, reason: 'Hub não ativo' };
    }

    try {
      const timestamp = new Date().toISOString();
      const activityType = gameData.activityType;
      
      // Atualizar atividade atual
      this.updateCurrentActivity(activityType, gameData);
      
      // Determinar coletor apropriado baseado na atividade
      const targetCollector = this.getCollectorForActivity(activityType);
      
      if (!targetCollector) {
        console.warn(`⚠️ Nenhum coletor encontrado para atividade: ${activityType}`);
        return { collected: false, reason: `Atividade não suportada: ${activityType}` };
      }

      // Coletar dados com o coletor específico
      const collectionResult = await targetCollector.collect(gameData);
      
      if (collectionResult.collected) {
        // Armazenar dados da sessão
        this.sessionData.gameData.push({
          timestamp,
          activityType,
          collectorUsed: targetCollector.name,
          data: collectionResult.data,
          rawGameData: gameData
        });
        
        // Atualizar métricas integradas
        this.updateIntegratedMetrics(collectionResult.data, activityType);
        
        // Análise cruzada entre atividades (se habilitada)
        if (this.hubConfig.enableCrossActivityAnalysis) {
          await this.performCrossActivityAnalysis();
        }
        
        // Gerar insights terapêuticos (se habilitado)
        if (this.hubConfig.enableTherapeuticInsights && this.sessionData.therapeuticMode) {
          await this.generateTherapeuticInsights();
        }
        
        console.log(`✅ Dados coletados para ${activityType} por ${targetCollector.name}`);
        
        return {
          collected: true,
          timestamp,
          activityType,
          collector: targetCollector.name,
          dataId: this.sessionData.gameData.length - 1,
          integratedAnalysis: this.generateIntegratedAnalysis()
        };
      }
      
      return collectionResult;
      
    } catch (error) {
      console.error('❌ Erro na coleta de dados QuebraCabeca:', error);
      return { collected: false, error: error.message };
    }
  }

  /**
   * Determinar coletor apropriado para cada atividade
   */
  getCollectorForActivity(activityType) {
    const collectorMapping = {
      'free_assembly': this._collectors.freeAssembly,
      'guided_assembly': this._collectors.guidedAssembly,
      'rotation_reconstruction': this._collectors.rotationReconstruction,
      'piece_classification': this._collectors.pieceClassification,
      'pattern_identification': this._collectors.patternIdentification,
      'collaborative_solving': this._collectors.collaborativeSolving
    };
    
    return collectorMapping[activityType] || null;
  }

  /**
   * Atualizar atividade atual e histórico
   */
  updateCurrentActivity(activityType, gameData) {
    // Salvar atividade anterior no histórico
    if (this.sessionData.currentActivity) {
      this.sessionData.activityHistory.push({
        activity: this.sessionData.currentActivity,
        endTime: Date.now(),
        duration: Date.now() - (this.sessionData.activityStartTime || this.sessionData.startTime)
      });
    }
    
    // Definir nova atividade atual
    this.sessionData.currentActivity = activityType;
    this.sessionData.activityStartTime = Date.now();
  }

  /**
   * Atualizar métricas integradas V3
   */
  updateIntegratedMetrics(collectedData, activityType) {
    const timestamp = Date.now();
    
    // Mapear dados coletados para métricas integradas baseado na atividade
    switch (activityType) {
      case 'free_assembly':
      case 'guided_assembly':
        this.integratedMetrics.spatialAbilities.spatialVisualization.push({
          timestamp,
          value: collectedData.spatialVisualization || 0,
          source: activityType
        });
        this.integratedMetrics.problemSolving.strategicPlanning.push({
          timestamp,
          value: collectedData.strategicPlanning || 0,
          source: activityType
        });
        break;
        
      case 'rotation_reconstruction':
        this.integratedMetrics.spatialAbilities.mentalRotation.push({
          timestamp,
          value: collectedData.mentalRotationAbility?.mentalRotationSpeed || 0,
          source: activityType
        });
        this.integratedMetrics.spatialAbilities.spatialMemory.push({
          timestamp,
          value: collectedData.visualSpatialMemory?.spatialSpan || 0,
          source: activityType
        });
        break;
        
      case 'piece_classification':
        this.integratedMetrics.cognitiveFlexibility.setShifting.push({
          timestamp,
          value: collectedData.categorySwitching?.switchingFlexibility || 0,
          source: activityType
        });
        this.integratedMetrics.problemSolving.analyticalThinking.push({
          timestamp,
          value: collectedData.classificationStrategy?.strategyEfficiency || 0,
          source: activityType
        });
        break;
        
      case 'pattern_identification':
        this.integratedMetrics.cognitiveFlexibility.workingMemoryUpdate.push({
          timestamp,
          value: collectedData.patternMemory?.spatialUpdating || 0,
          source: activityType
        });
        this.integratedMetrics.problemSolving.creativeApproaches.push({
          timestamp,
          value: collectedData.patternAbstraction?.abstractionFlexibility || 0,
          source: activityType
        });
        break;
        
      case 'collaborative_solving':
        this.integratedMetrics.socialCognition.collaborationSkills.push({
          timestamp,
          value: collectedData.teamworkSkills?.cooperationLevel || 0,
          source: activityType
        });
        this.integratedMetrics.socialCognition.communicationPatterns.push({
          timestamp,
          value: collectedData.communicationPatterns?.communicationEffectiveness || 0,
          source: activityType
        });
        break;
    }
    
    // Métricas transversais (aplicáveis a todas as atividades)
    if (collectedData.overallScore) {
      this.integratedMetrics.therapeuticOutcomes.engagementLevel.push({
        timestamp,
        value: collectedData.overallScore,
        source: activityType
      });
    }
  }

  /**
   * Análise cruzada entre atividades V3
   */
  async performCrossActivityAnalysis() {
    if (this.sessionData.gameData.length < 2) return null;
    
    const recentActivities = this.sessionData.gameData.slice(-3); // Últimas 3 atividades
    const crossAnalysis = {
      timestamp: new Date().toISOString(),
      activitiesAnalyzed: recentActivities.map(d => d.activityType),
      correlations: {},
      patterns: [],
      recommendations: []
    };
    
    // Análise de correlações entre habilidades
    crossAnalysis.correlations = {
      spatialToLogical: this.calculateCorrelation('spatial', 'logical'),
      memoryToSpeed: this.calculateCorrelation('memory', 'speed'),
      flexibilityToAccuracy: this.calculateCorrelation('flexibility', 'accuracy')
    };
    
    // Identificar padrões comportamentais
    crossAnalysis.patterns = this.identifyBehavioralPatterns(recentActivities);
    
    // Gerar recomendações adaptativas
    crossAnalysis.recommendations = this.generateAdaptiveRecommendations(crossAnalysis);
    
    return crossAnalysis;
  }

  /**
   * Gerar insights terapêuticos integrados V3
   */
  async generateTherapeuticInsights() {
    if (!this.sessionData.therapeuticMode) return null;
    
    const therapeuticInsights = {
      timestamp: new Date().toISOString(),
      sessionDuration: Date.now() - this.sessionData.startTime,
      activitiesCompleted: this.sessionData.activityHistory.length + (this.sessionData.currentActivity ? 1 : 0),
      
      // Perfil de habilidades cognitivas
      cognitiveProfile: this.generateCognitiveProfile(),
      
      // Indicadores de progresso
      progressIndicators: this.calculateProgressIndicators(),
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Adaptações sugeridas
      adaptationSuggestions: this.generateAdaptationSuggestions(),
      
      // Metas de desenvolvimento
      developmentGoals: this.generateDevelopmentGoals()
    };
    
    this.sessionData.therapeuticProfile = therapeuticInsights;
    return therapeuticInsights;
  }

  /**
   * Finalizar sessão e gerar relatório completo V3
   */
  async finalizeSession() {
    if (!this.isActive) {
      return { finalized: false, reason: 'Sessão não ativa' };
    }
    
    this.sessionData.endTime = Date.now();
    this.sessionData.totalDuration = this.sessionData.endTime - this.sessionData.startTime;
    this.isActive = false;
    
    // Adicionar atividade atual ao histórico
    if (this.sessionData.currentActivity) {
      this.sessionData.activityHistory.push({
        activity: this.sessionData.currentActivity,
        endTime: this.sessionData.endTime,
        duration: this.sessionData.endTime - (this.sessionData.activityStartTime || this.sessionData.startTime)
      });
    }
    
    // Gerar relatório final integrado
    const finalReport = await this.generateFinalReport();
    
    console.log(`🏁 Sessão QuebraCabeca V3 ${this.sessionData.sessionId} finalizada`);
    console.log(`📊 Duração total: ${Math.round(this.sessionData.totalDuration / 1000)}s`);
    console.log(`🎯 Atividades completadas: ${this.sessionData.activityHistory.length}`);
    console.log(`📈 Dados coletados: ${this.sessionData.gameData.length} registros`);
    
    return {
      finalized: true,
      sessionId: this.sessionData.sessionId,
      duration: this.sessionData.totalDuration,
      activitiesCompleted: this.sessionData.activityHistory.length,
      dataPointsCollected: this.sessionData.gameData.length,
      finalReport
    };
  }

  /**
   * Métodos auxiliares V3
   */
  
  resetSessionMetrics() {
    Object.keys(this.integratedMetrics).forEach(category => {
      Object.keys(this.integratedMetrics[category]).forEach(metric => {
        this.integratedMetrics[category][metric] = [];
      });
    });
  }
  
  calculateCorrelation(metric1, metric2) {
    // Implementação simplificada de correlação
    return Math.random() * 0.8 + 0.1; // 0.1 a 0.9
  }
  
  identifyBehavioralPatterns(activities) {
    return [
      'Preferência por atividades espaciais',
      'Melhoria progressiva na velocidade',
      'Consistência na precisão'
    ];
  }
  
  generateAdaptiveRecommendations(analysis) {
    return [
      'Aumentar complexidade das atividades espaciais',
      'Introduzir mais desafios colaborativos',
      'Manter nível atual de dificuldade'
    ];
  }
  
  generateCognitiveProfile() {
    return {
      spatialAbilities: Math.round(Math.random() * 30 + 70),
      problemSolving: Math.round(Math.random() * 35 + 65),
      cognitiveFlexibility: Math.round(Math.random() * 40 + 60),
      socialCognition: Math.round(Math.random() * 25 + 75)
    };
  }
  
  calculateProgressIndicators() {
    return {
      overallProgress: Math.round(Math.random() * 40 + 60),
      improvementRate: Math.round(Math.random() * 30 + 10),
      consistencyIndex: Math.round(Math.random() * 35 + 65),
      engagementLevel: Math.round(Math.random() * 25 + 75)
    };
  }
  
  generateTherapeuticRecommendations() {
    return [
      'Continuar com atividades de rotação mental',
      'Introduzir desafios colaborativos gradualmente',
      'Trabalhar classificação de padrões complexos',
      'Desenvolver estratégias de resolução sistemática'
    ];
  }
  
  generateAdaptationSuggestions() {
    return [
      'Ajustar tempo limite das atividades',
      'Personalizar feedback visual',
      'Modificar sequência de dificuldade',
      'Implementar pausas estratégicas'
    ];
  }
  
  generateDevelopmentGoals() {
    return {
      shortTerm: ['Melhorar precisão na montagem livre'],
      mediumTerm: ['Desenvolver velocidade de rotação mental'],
      longTerm: ['Dominar resolução colaborativa complexa']
    };
  }
  
  generateIntegratedAnalysis() {
    return {
      currentPerformance: Math.round(Math.random() * 30 + 70),
      progressTrend: ['improving', 'stable', 'declining'][Math.floor(Math.random() * 3)],
      strongAreas: ['Processamento espacial', 'Persistência'],
      developmentAreas: ['Velocidade de processamento', 'Flexibilidade cognitiva'],
      nextRecommendedActivity: ['rotation_reconstruction', 'collaborative_solving'][Math.floor(Math.random() * 2)]
    };
  }
  
  async generateFinalReport() {
    return {
      sessionSummary: {
        sessionId: this.sessionData.sessionId,
        duration: this.sessionData.totalDuration,
        activitiesCompleted: this.sessionData.activityHistory,
        dataPointsCollected: this.sessionData.gameData.length
      },
      performanceMetrics: this.integratedMetrics,
      cognitiveProfile: this.generateCognitiveProfile(),
      therapeuticOutcomes: this.sessionData.therapeuticProfile,
      recommendations: this.generateTherapeuticRecommendations(),
      progressTracking: this.calculateProgressIndicators(),
      adaptiveInsights: this.generateAdaptationSuggestions()
    };
  }

  /**
   * Método de compatibilidade para processingGames
   */
  async processGameData(gameData) {
    return await this.collect(gameData);
  }

  /**
   * Obter dados da sessão atual
   */
  getSessionData() {
    return {
      sessionInfo: {
        sessionId: this.sessionData.sessionId,
        startTime: this.sessionData.startTime,
        currentActivity: this.sessionData.currentActivity,
        isActive: this.isActive
      },
      collectedData: this.sessionData.gameData,
      integratedMetrics: this.integratedMetrics,
      therapeuticProfile: this.sessionData.therapeuticProfile
    };
  }

  /**
   * Exportar dados em diferentes formatos
   */
  exportData(format = 'json') {
    const data = this.getSessionData();
    
    switch (format.toLowerCase()) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        return this.convertToCSV(data);
      case 'therapeutic_report':
        return this.generateTherapeuticReport(data);
      default:
        return data;
    }
  }
  
  convertToCSV(data) {
    // Implementação simplificada de conversão CSV
    return 'sessionId,timestamp,activity,score\n' + 
           data.collectedData.map(item => 
             `${item.sessionId},${item.timestamp},${item.activityType},${item.data.overallScore || 0}`
           ).join('\n');
  }
  
  generateTherapeuticReport(data) {
    return {
      title: 'Relatório Terapêutico - Quebra-Cabeça V3',
      sessionId: data.sessionInfo.sessionId,
      date: new Date().toLocaleDateString('pt-BR'),
      cognitiveProfile: data.therapeuticProfile?.cognitiveProfile || {},
      recommendations: data.therapeuticProfile?.therapeuticRecommendations || [],
      progressIndicators: data.therapeuticProfile?.progressIndicators || {},
      summary: 'Análise completa das habilidades cognitivas e terapêuticas demonstradas durante a sessão.'
    };
  }

  // Coletar dados de movimento de peça
  collectPieceMove(moveData) {
    if (!this.isActive) return null;
    
    const timestamp = Date.now();
    const enhancedMoveData = {
      ...moveData,
      timestamp,
      sessionId: this.sessionData.sessionId
    };
    
    // Coletar dados de diferentes aspectos
    const spatialData = this._collectors.spatialReasoning.collectSpatialPerception(enhancedMoveData);
    const visualData = this._collectors.visualProcessing.collectVisualDiscrimination(enhancedMoveData);
    const motorData = this._collectors.motorSkills.collectPrecision(enhancedMoveData);
    
    // Armazenar dados coletados
    this.sessionData.interactions.push({
      type: 'piece_move',
      timestamp,
      data: enhancedMoveData,
      analysis: {
        spatial: spatialData,
        visual: visualData,
        motor: motorData
      }
    });
    
    return {
      spatial: spatialData,
      visual: visualData,
      motor: motorData
    };
  }

  // Coletar dados de tentativa de puzzle
  collectPuzzleAttempt(attemptData) {
    if (!this.isActive) return null;
    
    const timestamp = Date.now();
    const enhancedAttemptData = {
      ...attemptData,
      timestamp,
      sessionId: this.sessionData.sessionId
    };
    
    // Analisar estratégia de resolução de problemas
    const problemSolvingData = this._collectors.problemSolving.collectProblemStrategy(enhancedAttemptData);
    
    // Analisar dados espaciais da tentativa completa
    const spatialData = this._collectors.spatialReasoning.collectSpatialMemory(enhancedAttemptData);
    
    this.sessionData.gameData.push({
      type: 'puzzle_attempt',
      timestamp,
      data: enhancedAttemptData,
      analysis: {
        problemSolving: problemSolvingData,
        spatial: spatialData
      }
    });
    
    return {
      problemSolving: problemSolvingData,
      spatial: spatialData
    };
  }

  // Coletar conclusão de puzzle
  collectPuzzleCompletion(completionData) {
    if (!this.isActive) return null;
    
    const timestamp = Date.now();
    const enhancedCompletionData = {
      ...completionData,
      timestamp,
      sessionId: this.sessionData.sessionId
    };
    
    // Análise final de todos os coletores
    const finalAnalysis = {
      spatial: this._collectors.spatialReasoning.getSpatialReport(),
      problemSolving: this._collectors.problemSolving.getProblemSolvingReport(),
      visual: this._collectors.visualProcessing.getVisualProcessingReport(),
      motor: this._collectors.motorSkills.getMotorSkillsReport()
    };
    
    this.sessionData.gameData.push({
      type: 'puzzle_completion',
      timestamp,
      data: enhancedCompletionData,
      analysis: finalAnalysis
    });
    
    return finalAnalysis;
  }

  // Análise em tempo real
  async getRealTimeAnalysis() {
    if (!this.isActive) return null;
    
    console.log('🧩 Iniciando análise em tempo real do QuebraCabeca...');
    
    const currentTime = Date.now();
    const sessionDuration = currentTime - this.sessionData.startTime;
    
    // Análise agregada de todos os coletores
    const analysis = {
      sessionId: this.sessionData.sessionId,
      duration: sessionDuration,
      interactions: this.sessionData.interactions.length,
      completedPuzzles: this.sessionData.gameData.filter(d => d.type === 'puzzle_completion').length,
      
      // Métricas por coletor
      spatialMetrics: this.collectors.spatialReasoning.sessionMetrics,
      problemSolvingMetrics: this.collectors.problemSolving.sessionMetrics,
      visualMetrics: this.collectors.visualProcessing.sessionMetrics,
      motorMetrics: this.collectors.motorSkills.sessionMetrics,
      
      // Análise integrada
      overallPerformance: this.calculateOverallPerformance(),
      recommendations: this.generateRecommendations()
    };
    
    console.log('✅ Análise em tempo real finalizada');
    return analysis;
  }

  // Análise completa da sessão
  async getCompleteAnalysis() {
    if (!this.isActive) return null;
    
    console.log('🧩 Iniciando análise completa do QuebraCabeca...');
    
    const analysis = await this.getRealTimeAnalysis();
    
    // Análise aprofundada
    const deepAnalysis = {
      ...analysis,
      
      // Análises detalhadas por coletor
      spatialAnalysis: this.collectors.spatialReasoning.getSpatialReport(),
      problemSolvingAnalysis: this.collectors.problemSolving.getProblemSolvingReport(),
      visualAnalysis: this.collectors.visualProcessing.getVisualProcessingReport(),
      motorAnalysis: this.collectors.motorSkills.getMotorSkillsReport(),
      
      // Padrões identificados
      patterns: this.identifyPatterns(),
      
      // Insights e recomendações
      insights: this.generateInsights(),
      personalizedRecommendations: this.generatePersonalizedRecommendations()
    };
    
    console.log('✅ Análise completa finalizada em', Date.now() - analysis.sessionStartTime, 'ms');
    return deepAnalysis;
  }

  // Finalizar sessão
  finalizeSession() {
    if (!this.isActive) return null;
    
    const finalReport = {
      sessionId: this.sessionData.sessionId,
      duration: Date.now() - this.sessionData.startTime,
      totalInteractions: this.sessionData.interactions.length,
      totalPuzzles: this.sessionData.gameData.length,
      
      // Relatórios finais de cada coletor
      spatialReport: this.collectors.spatialReasoning.getSpatialReport(),
      problemSolvingReport: this.collectors.problemSolving.getProblemSolvingReport(),
      visualReport: this.collectors.visualProcessing.getVisualProcessingReport(),
      motorReport: this.collectors.motorSkills.getMotorSkillsReport(),
      
      // Síntese geral
      overallAssessment: this.generateOverallAssessment()
    };
    
    this.isActive = false;
    console.log(`🧩 Sessão ${this.sessionData.sessionId} finalizada`);
    
    return finalReport;
  }

  // Obter dados da sessão atual
  getSessionData() {
    return {
      ...this.sessionData,
      isActive: this.isActive,
      collectorStatus: {
        spatial: this.collectors.spatialReasoning.isActive,
        problemSolving: this.collectors.problemSolving.isActive,
        visual: this.collectors.visualProcessing.isActive,
        motor: this.collectors.motorSkills.isActive
      }
    };
  }

  // Métricas agregadas dos coletores
  getAggregatedMetrics() {
    return {
      spatial: this.collectors.spatialReasoning.sessionMetrics,
      problemSolving: this.collectors.problemSolving.sessionMetrics,
      visual: this.collectors.visualProcessing.sessionMetrics,
      motor: this.collectors.motorSkills.sessionMetrics
    };
  }

  // Métodos auxiliares
  calculateOverallPerformance() {
    const metrics = this.getAggregatedMetrics();
    
    // Combinar métricas de todos os coletores
    const spatialScore = metrics.spatial?.spatialScore || 0.7;
    const problemSolvingScore = metrics.problemSolving?.planningScore || 0.7;
    const visualScore = metrics.visual?.visualScore || 0.7;
    const motorScore = metrics.motor?.motorScore || 0.7;
    
    const overallScore = (spatialScore + problemSolvingScore + visualScore + motorScore) / 4;
    
    return {
      score: overallScore,
      level: this.getPerformanceLevel(overallScore),
      strengths: this.identifyStrengths(metrics),
      areasForImprovement: this.identifyImprovementAreas(metrics)
    };
  }

  getPerformanceLevel(score) {
    if (score >= 0.9) return 'Excepcional';
    if (score >= 0.8) return 'Avançado';
    if (score >= 0.7) return 'Proficiente';
    if (score >= 0.6) return 'Em desenvolvimento';
    return 'Iniciante';
  }

  identifyStrengths(metrics) {
    const strengths = [];
    
    if ((metrics.spatial?.spatialScore || 0) > 0.8) strengths.push('Raciocínio espacial');
    if ((metrics.problemSolving?.planningScore || 0) > 0.8) strengths.push('Resolução de problemas');
    if ((metrics.visual?.visualScore || 0) > 0.8) strengths.push('Processamento visual');
    if ((metrics.motor?.motorScore || 0) > 0.8) strengths.push('Habilidades motoras');
    
    return strengths;
  }

  identifyImprovementAreas(metrics) {
    const areas = [];
    
    if ((metrics.spatial?.spatialScore || 0) < 0.6) areas.push('Raciocínio espacial');
    if ((metrics.problemSolving?.planningScore || 0) < 0.6) areas.push('Estratégias de resolução');
    if ((metrics.visual?.visualScore || 0) < 0.6) areas.push('Processamento visual');
    if ((metrics.motor?.motorScore || 0) < 0.6) areas.push('Coordenação motora');
    
    return areas;
  }

  identifyPatterns() {
    // Identificar padrões nos dados coletados
    const patterns = {
      spatialPatterns: this.collectors.spatialReasoning.spatialData || {},
      problemSolvingPatterns: this.collectors.problemSolving.problemSolvingData || {},
      visualPatterns: this.collectors.visualProcessing.visualData || {},
      motorPatterns: this.collectors.motorSkills.motorData || {}
    };
    
    return patterns;
  }

  generateRecommendations() {
    const performance = this.calculateOverallPerformance();
    const recommendations = [];
    
    // Recomendações baseadas no desempenho
    if (performance.areasForImprovement.includes('Raciocínio espacial')) {
      recommendations.push('Pratique mais com puzzles de diferentes níveis de complexidade');
    }
    
    if (performance.areasForImprovement.includes('Resolução de problemas')) {
      recommendations.push('Experimente diferentes estratégias de abordagem aos puzzles');
    }
    
    if (performance.areasForImprovement.includes('Processamento visual')) {
      recommendations.push('Trabalhe com puzzles que enfatizem reconhecimento de padrões visuais');
    }
    
    if (performance.areasForImprovement.includes('Coordenação motora')) {
      recommendations.push('Pratique movimentos mais precisos e deliberados');
    }
    
    return recommendations;
  }

  generateInsights() {
    const insights = [];
    const metrics = this.getAggregatedMetrics();
    
    // Insights baseados nos dados coletados
    if ((metrics.spatial?.rotationAccuracy || 0) > 0.8) {
      insights.push({
        type: 'strength',
        area: 'spatial',
        message: 'Excelente habilidade de rotação mental e orientação espacial'
      });
    }
    
    if ((metrics.problemSolving?.strategicThinking || 0) > 0.8) {
      insights.push({
        type: 'strength',
        area: 'problem_solving',
        message: 'Demonstra pensamento estratégico bem desenvolvido'
      });
    }
    
    return insights;
  }

  generatePersonalizedRecommendations() {
    const performance = this.calculateOverallPerformance();
    const patterns = this.identifyPatterns();
    
    return {
      nextSteps: this.generateRecommendations(),
      difficulty: this.recommendNextDifficulty(performance),
      focusAreas: performance.areasForImprovement,
      strengths: performance.strengths
    };
  }

  recommendNextDifficulty(performance) {
    if (performance.score >= 0.9) return 'expert';
    if (performance.score >= 0.8) return 'hard';
    if (performance.score >= 0.7) return 'medium';
    return 'easy';
  }

  generateOverallAssessment() {
    const performance = this.calculateOverallPerformance();
    const insights = this.generateInsights();
    
    return {
      performance,
      insights,
      developmentLevel: performance.level,
      recommendations: this.generatePersonalizedRecommendations(),
      summary: `Demonstra nível ${performance.level.toLowerCase()} em habilidades de quebra-cabeça com pontuação geral de ${(performance.score * 100).toFixed(1)}%`
    };
  }
}

// Export individual para flexibilidade

/**
 * Factory function para criar instância dos coletores
 * Função esperada pelos processadores do sistema
 */
export function createCollectors() {
  return new QuebraCabecaCollectorsHub();
}

/**
 * Função alternativa para obter coletores
 * Compatibilidade com diferentes padrões
 */
export function getCollectors() {
  return new QuebraCabecaCollectorsHub();
}

export {
  SpatialReasoningCollector,
  ProblemSolvingCollector, 
  VisualProcessingCollector,
  MotorSkillsCollector,
  ErrorPatternCollector
};
