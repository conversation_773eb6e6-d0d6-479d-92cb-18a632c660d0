/**
 * 🔬 TESTE COMPLETO DO SISTEMA MULTISSENSORIAL + BANCO DE DADOS
 * Verifica: Coleta multissensorial → Processamento → Salvamento no banco → Dashboard
 * Portal Betina V3
 */

import { MultisensoryMetricsCollector } from '../api/services/multisensoryAnalysis/multisensoryMetrics.js';
import { getSystemOrchestrator } from '../api/services/core/SystemOrchestrator.js';
import DatabaseManager from '../database/index.js';
import { Client } from 'pg';

// Configuração do banco de dados
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER || 'betina_user',
  password: process.env.DB_PASSWORD || 'betina_password',
  database: process.env.DB_NAME || 'betina_db'
};

/**
 * Classe para testar integração completa
 */
class MultisensoryDatabaseTester {
  constructor() {
    this.dbClient = null;
    this.multisensoryCollector = null;
    this.systemOrchestrator = null;
    this.testResults = {
      databaseConnection: false,
      tablesExist: false,
      multisensoryCollection: false,
      dataProcessing: false,
      databaseSaving: false,
      dashboardVisualization: false,
      errors: []
    };
  }

  /**
   * 1. Testar conexão com banco de dados
   */
  async testDatabaseConnection() {
    console.log('🔌 TESTANDO CONEXÃO COM BANCO DE DADOS...');
    
    try {
      this.dbClient = new Client(DB_CONFIG);
      await this.dbClient.connect();
      
      // Testar query simples
      const result = await this.dbClient.query('SELECT NOW() as current_time');
      console.log('✅ Conexão com banco estabelecida:', result.rows[0].current_time);
      
      this.testResults.databaseConnection = true;
      return true;
    } catch (error) {
      console.error('❌ Erro na conexão com banco:', error.message);
      this.testResults.errors.push(`Database connection: ${error.message}`);
      return false;
    }
  }

  /**
   * 2. Verificar se tabelas necessárias existem
   */
  async testDatabaseTables() {
    console.log('\n📋 VERIFICANDO TABELAS DO BANCO...');
    
    const requiredTables = [
      'game_sessions',
      'game_metrics', 
      'multisensory_data',
      'therapeutic_analysis',
      'user_progress',
      'sensor_calibration'
    ];

    try {
      for (const table of requiredTables) {
        const result = await this.dbClient.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);

        const exists = result.rows[0].exists;
        console.log(`${exists ? '✅' : '❌'} Tabela ${table}: ${exists ? 'EXISTS' : 'MISSING'}`);
        
        if (!exists) {
          // Criar tabela se não existir
          await this.createMissingTable(table);
        }
      }
      
      this.testResults.tablesExist = true;
      return true;
    } catch (error) {
      console.error('❌ Erro ao verificar tabelas:', error.message);
      this.testResults.errors.push(`Tables check: ${error.message}`);
      return false;
    }
  }

  /**
   * Criar tabelas faltantes
   */
  async createMissingTable(tableName) {
    const tableSchemas = {
      multisensory_data: `
        CREATE TABLE IF NOT EXISTS multisensory_data (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          sensor_type VARCHAR(100) NOT NULL,
          sensor_data JSONB NOT NULL,
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          calibration_data JSONB,
          processing_metadata JSONB
        );
      `,
      sensor_calibration: `
        CREATE TABLE IF NOT EXISTS sensor_calibration (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) NOT NULL,
          device_id VARCHAR(255),
          sensor_type VARCHAR(100) NOT NULL,
          calibration_data JSONB NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      game_sessions: `
        CREATE TABLE IF NOT EXISTS game_sessions (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) UNIQUE NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          session_data JSONB NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      game_metrics: `
        CREATE TABLE IF NOT EXISTS game_metrics (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          metrics_data JSONB NOT NULL,
          analysis_data JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      therapeutic_analysis: `
        CREATE TABLE IF NOT EXISTS therapeutic_analysis (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          analysis_type VARCHAR(100) NOT NULL,
          analysis_data JSONB NOT NULL,
          confidence_score DECIMAL(3,2),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      user_progress: `
        CREATE TABLE IF NOT EXISTS user_progress (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          progress_data JSONB NOT NULL,
          milestone_data JSONB,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `
    };

    if (tableSchemas[tableName]) {
      await this.dbClient.query(tableSchemas[tableName]);
      console.log(`✅ Tabela ${tableName} criada com sucesso`);
    }
  }

  /**
   * 3. Testar coleta multissensorial
   */
  async testMultisensoryCollection() {
    console.log('\n🔬 TESTANDO COLETA MULTISSENSORIAL...');
    
    try {
      this.multisensoryCollector = new MultisensoryMetricsCollector();
      
      // Simular sessão de jogo
      const sessionId = `test_multisensory_${Date.now()}`;
      const userId = 'test_user_multisensory';
      
      // Iniciar coleta
      await this.multisensoryCollector.startSession(sessionId, userId);
      console.log('✅ Sessão multissensorial iniciada');
      
      // Simular dados de sensores
      const mockSensorData = {
        touch: [
          { x: 100, y: 200, pressure: 0.8, timestamp: Date.now() },
          { x: 150, y: 250, pressure: 0.6, timestamp: Date.now() + 100 }
        ],
        accelerometer: [
          { x: 0.1, y: 0.2, z: 9.8, timestamp: Date.now() },
          { x: 0.15, y: 0.18, z: 9.82, timestamp: Date.now() + 100 }
        ],
        gyroscope: [
          { alpha: 45, beta: 30, gamma: 15, timestamp: Date.now() }
        ]
      };

      // Injetar dados de teste
      this.multisensoryCollector.sensorData = mockSensorData;
      
      // Finalizar sessão e obter dados
      const sessionData = await this.multisensoryCollector.endSession();
      console.log('✅ Dados multissensoriais coletados:', Object.keys(sessionData));
      
      this.testResults.multisensoryCollection = true;
      return sessionData;
    } catch (error) {
      console.error('❌ Erro na coleta multissensorial:', error.message);
      this.testResults.errors.push(`Multisensory collection: ${error.message}`);
      return null;
    }
  }

  /**
   * 4. Testar processamento através do SystemOrchestrator
   */
  async testDataProcessing(sensorData) {
    console.log('\n⚙️ TESTANDO PROCESSAMENTO DE DADOS...');
    
    try {
      // Usar banco real
      const dbManager = new DatabaseManager();
      await dbManager.initialize();
      
      this.systemOrchestrator = await getSystemOrchestrator(dbManager);
      
      // Dados de teste com sensores
      const gameData = {
        sessionId: `test_processing_${Date.now()}`,
        userId: 'test_user_processing',
        gameId: 'ColorMatch',
        sessionDuration: 120000,
        accuracy: 0.85,
        multisensoryData: sensorData,
        sensorCalibration: {
          touchSensitivity: 0.8,
          accelerometerBaseline: { x: 0, y: 0, z: 9.8 },
          gyroscopeBaseline: { alpha: 0, beta: 0, gamma: 0 }
        }
      };

      // Processar através do orquestrador
      const result = await this.systemOrchestrator.processGameMetrics(
        gameData.userId,
        gameData.gameId,
        gameData
      );

      console.log('✅ Dados processados pelo SystemOrchestrator');
      console.log('📊 Resultado:', {
        success: result.success,
        hasMultisensoryAnalysis: !!result.multisensoryAnalysis,
        hasSensorData: !!result.sensorData
      });

      this.testResults.dataProcessing = true;
      return result;
    } catch (error) {
      console.error('❌ Erro no processamento:', error.message);
      this.testResults.errors.push(`Data processing: ${error.message}`);
      return null;
    }
  }

  /**
   * 5. Testar salvamento no banco de dados
   */
  async testDatabaseSaving(processedData) {
    console.log('\n💾 TESTANDO SALVAMENTO NO BANCO...');
    
    try {
      // Salvar dados multissensoriais
      const multisensoryInsert = await this.dbClient.query(`
        INSERT INTO multisensory_data (session_id, user_id, game_id, sensor_type, sensor_data, calibration_data)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id;
      `, [
        processedData.sessionId || 'test_session',
        processedData.userId || 'test_user',
        processedData.gameId || 'ColorMatch',
        'combined',
        JSON.stringify(processedData.multisensoryData || {}),
        JSON.stringify(processedData.sensorCalibration || {})
      ]);

      console.log('✅ Dados multissensoriais salvos no banco, ID:', multisensoryInsert.rows[0].id);

      // Salvar métricas do jogo
      const metricsInsert = await this.dbClient.query(`
        INSERT INTO game_metrics (session_id, user_id, game_id, metrics_data, analysis_data)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id;
      `, [
        processedData.sessionId || 'test_session',
        processedData.userId || 'test_user', 
        processedData.gameId || 'ColorMatch',
        JSON.stringify(processedData.therapeuticMetrics || {}),
        JSON.stringify(processedData.therapeuticAnalysis || {})
      ]);

      console.log('✅ Métricas do jogo salvas no banco, ID:', metricsInsert.rows[0].id);

      this.testResults.databaseSaving = true;
      return true;
    } catch (error) {
      console.error('❌ Erro ao salvar no banco:', error.message);
      this.testResults.errors.push(`Database saving: ${error.message}`);
      return false;
    }
  }

  /**
   * 6. Testar visualização no dashboard (simulado)
   */
  async testDashboardVisualization() {
    console.log('\n📊 TESTANDO VISUALIZAÇÃO NO DASHBOARD...');
    
    try {
      // Consultar dados salvos para dashboard
      const dashboardData = await this.dbClient.query(`
        SELECT 
          gm.session_id,
          gm.user_id,
          gm.game_id,
          gm.metrics_data,
          md.sensor_data,
          md.calibration_data,
          gm.created_at
        FROM game_metrics gm
        LEFT JOIN multisensory_data md ON gm.session_id = md.session_id
        WHERE gm.user_id LIKE 'test_user%'
        ORDER BY gm.created_at DESC
        LIMIT 5;
      `);

      console.log('✅ Dados recuperados para dashboard:', dashboardData.rows.length, 'registros');
      
      // Simular processamento para dashboard
      const dashboardMetrics = dashboardData.rows.map(row => ({
        sessionId: row.session_id,
        gameId: row.game_id,
        accuracy: row.metrics_data?.accuracy || 0,
        sensorActivity: Object.keys(row.sensor_data || {}).length,
        calibrationStatus: !!row.calibration_data,
        timestamp: row.created_at
      }));

      console.log('📈 Métricas processadas para dashboard:', dashboardMetrics);

      this.testResults.dashboardVisualization = true;
      return dashboardMetrics;
    } catch (error) {
      console.error('❌ Erro na visualização do dashboard:', error.message);
      this.testResults.errors.push(`Dashboard visualization: ${error.message}`);
      return null;
    }
  }

  /**
   * Executar todos os testes
   */
  async runCompleteTest() {
    console.log('🚀 INICIANDO TESTE COMPLETO DO SISTEMA MULTISSENSORIAL + BANCO');
    console.log('='.repeat(80));

    try {
      // 1. Conexão com banco
      const dbConnected = await this.testDatabaseConnection();
      if (!dbConnected) return this.testResults;

      // 2. Verificar tabelas
      const tablesOk = await this.testDatabaseTables();
      if (!tablesOk) return this.testResults;

      // 3. Coleta multissensorial
      const sensorData = await this.testMultisensoryCollection();
      if (!sensorData) return this.testResults;

      // 4. Processamento
      const processedData = await this.testDataProcessing(sensorData);
      if (!processedData) return this.testResults;

      // 5. Salvamento
      const savedOk = await this.testDatabaseSaving(processedData);
      if (!savedOk) return this.testResults;

      // 6. Dashboard
      const dashboardData = await this.testDashboardVisualization();
      if (!dashboardData) return this.testResults;

      console.log('\n🎉 TODOS OS TESTES CONCLUÍDOS COM SUCESSO!');
      return this.testResults;

    } catch (error) {
      console.error('💥 ERRO CRÍTICO NO TESTE:', error);
      this.testResults.errors.push(`Critical error: ${error.message}`);
      return this.testResults;
    } finally {
      // Fechar conexão
      if (this.dbClient) {
        await this.dbClient.end();
      }
    }
  }

  /**
   * Gerar relatório final
   */
  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📋 RELATÓRIO FINAL DO TESTE MULTISSENSORIAL + BANCO');
    console.log('='.repeat(80));

    const tests = [
      { name: 'Conexão com Banco', status: this.testResults.databaseConnection },
      { name: 'Tabelas do Banco', status: this.testResults.tablesExist },
      { name: 'Coleta Multissensorial', status: this.testResults.multisensoryCollection },
      { name: 'Processamento de Dados', status: this.testResults.dataProcessing },
      { name: 'Salvamento no Banco', status: this.testResults.databaseSaving },
      { name: 'Visualização Dashboard', status: this.testResults.dashboardVisualization }
    ];

    tests.forEach(test => {
      console.log(`${test.status ? '✅' : '❌'} ${test.name}`);
    });

    const successCount = tests.filter(t => t.status).length;
    const successRate = (successCount / tests.length * 100).toFixed(1);

    console.log(`\n📊 TAXA DE SUCESSO: ${successRate}% (${successCount}/${tests.length})`);

    if (this.testResults.errors.length > 0) {
      console.log('\n🚨 ERROS ENCONTRADOS:');
      this.testResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    const allPassed = successCount === tests.length;
    console.log(`\n🎯 RESULTADO GERAL: ${allPassed ? '✅ SISTEMA TOTALMENTE FUNCIONAL' : '⚠️ SISTEMA COM PROBLEMAS'}`);
    
    return allPassed;
  }
}

// Executar teste se chamado diretamente
if (typeof window === 'undefined') {
  const isMainModule = process.argv[1] && process.argv[1].includes('test-multisensory-database-integration.js');
  
  if (isMainModule) {
    const tester = new MultisensoryDatabaseTester();
    
    tester.runCompleteTest()
      .then(() => {
        const success = tester.generateReport();
        process.exit(success ? 0 : 1);
      })
      .catch(error => {
        console.error('💥 ERRO CRÍTICO:', error);
        process.exit(1);
      });
  }
}

export { MultisensoryDatabaseTester };
