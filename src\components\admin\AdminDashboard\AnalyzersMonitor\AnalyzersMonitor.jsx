/**
 * @file AnalyzersMonitor.jsx
 * @description Monitor de Analisadores Especializados - Área Administrativa
 * @version 1.0.0
 * @admin true
 */

import React, { useState, useEffect } from 'react'
import styles from './AnalyzersMonitor.module.css'

const AnalyzersMonitor = () => {
  const [analyzersData, setAnalyzersData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedAnalyzer, setSelectedAnalyzer] = useState(null)

  // Simular dados dos analisadores
  const loadAnalyzersData = async () => {
    try {
      const mockData = {
        behavioral_analyzer: {
          status: 'healthy',
          name: '<PERSON><PERSON>ador Comportamental',
          icon: '🧠',
          metrics: {
            analysesPerformed: Math.floor(Math.random() * 100) + 50,
            patternsDetected: Math.floor(Math.random() * 20) + 10,
            lastAnalysis: Date.now() - (Math.random() * 600000),
            cacheHitRate: (0.7 + Math.random() * 0.2).toFixed(3),
            avgProcessingTime: Math.floor(Math.random() * 500) + 200
          },
          recentAnalyses: [
            { childId: 'child_123', game: 'ColorMatch', score: 0.85, timestamp: Date.now() - 300000 },
            { childId: 'child_456', game: 'MemoryGame', score: 0.92, timestamp: Date.now() - 600000 },
            { childId: 'child_789', game: 'PadroesVisuais', score: 0.78, timestamp: Date.now() - 900000 }
          ]
        },
        cognitive_analyzer: {
          status: 'healthy',
          name: 'Analisador Cognitivo',
          icon: '🧩',
          metrics: {
            cognitiveAssessments: Math.floor(Math.random() * 80) + 30,
            domainsAnalyzed: 4,
            lastAssessment: Date.now() - (Math.random() * 400000),
            avgConfidence: (0.8 + Math.random() * 0.15).toFixed(3),
            processingAccuracy: (0.85 + Math.random() * 0.1).toFixed(3)
          },
          domains: ['attention', 'memory', 'executive_function', 'language'],
          recentAssessments: [
            { domain: 'attention', score: 0.88, childId: 'child_123' },
            { domain: 'memory', score: 0.91, childId: 'child_456' },
            { domain: 'executive_function', score: 0.76, childId: 'child_789' }
          ]
        },
        progress_analyzer: {
          status: 'healthy',
          name: 'Analisador de Progresso',
          icon: '📈',
          metrics: {
            progressReports: Math.floor(Math.random() * 60) + 20,
            milestonesDetected: Math.floor(Math.random() * 15) + 5,
            lastReport: Date.now() - (Math.random() * 500000),
            improvementRate: (0.6 + Math.random() * 0.3).toFixed(3),
            trendsIdentified: Math.floor(Math.random() * 10) + 3
          },
          trends: ['improving', 'stable', 'needs_attention'],
          milestones: [
            { name: 'Primeira sessão completa', achieved: true },
            { name: 'Precisão acima de 80%', achieved: true },
            { name: 'Nível 5 alcançado', achieved: false }
          ]
        },
        session_analyzer: {
          status: 'healthy',
          name: 'Analisador de Sessão',
          icon: '📊',
          metrics: {
            sessionsAnalyzed: Math.floor(Math.random() * 120) + 50,
            realTimeAnalyses: Math.floor(Math.random() * 80) + 30,
            lastSessionAnalysis: Date.now() - (Math.random() * 200000),
            avgEngagement: (0.75 + Math.random() * 0.2).toFixed(3),
            sessionCompletionRate: (0.85 + Math.random() * 0.1).toFixed(3)
          },
          engagementLevels: ['high', 'medium', 'low'],
          recentSessions: [
            { sessionId: 'session_001', engagement: 0.92, duration: 180000 },
            { sessionId: 'session_002', engagement: 0.78, duration: 240000 },
            { sessionId: 'session_003', engagement: 0.85, duration: 150000 }
          ]
        },
        therapeutic_analyzer: {
          status: 'healthy',
          name: 'Analisador Terapêutico',
          icon: '🎯',
          metrics: {
            therapeuticAnalyses: Math.floor(Math.random() * 40) + 15,
            interventionsRecommended: Math.floor(Math.random() * 25) + 10,
            lastTherapeuticAnalysis: Date.now() - (Math.random() * 700000),
            outcomeSuccess: (0.7 + Math.random() * 0.25).toFixed(3),
            goalsAchieved: Math.floor(Math.random() * 8) + 3
          },
          approaches: ['ABA', 'TEACCH', 'DIR_Floortime'],
          interventions: [
            { type: 'behavioral', priority: 'high', status: 'active' },
            { type: 'cognitive', priority: 'medium', status: 'planned' },
            { type: 'social', priority: 'low', status: 'completed' }
          ]
        }
      }

      setAnalyzersData(mockData)
    } catch (error) {
      console.error('Erro ao carregar dados dos analisadores:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAnalyzersData()
    const interval = setInterval(loadAnalyzersData, 45000) // Atualizar a cada 45s
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return '#4CAF50'
      case 'warning': return '#FF9800'
      case 'unhealthy': return '#F44336'
      default: return '#9E9E9E'
    }
  }

  const formatTime = (timestamp) => {
    const diff = Date.now() - timestamp
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) return `${hours}h ${minutes % 60}m atrás`
    return `${minutes}m atrás`
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando dados dos analisadores...</p>
      </div>
    )
  }

  return (
    <div className={styles.analyzersMonitor}>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(370px, 1fr))', gap: '24px', margin: '24px 0' }}>
        {Object.entries(analyzersData).map(([key, analyzer]) => (
          <div key={key} style={{
            background: 'rgba(255, 255, 255, 0.13)',
            borderRadius: '16px',
            padding: '28px',
            border: '1.5px solid rgba(255, 255, 255, 0.25)',
            boxShadow: '0 4px 24px rgba(0,0,0,0.12)',
            backdropFilter: 'blur(12px)',
            transition: 'transform 0.2s ease',
            cursor: 'pointer',
            transform: selectedAnalyzer === key ? 'scale(1.03)' : 'scale(1)',
          }}
          onClick={() => setSelectedAnalyzer(selectedAnalyzer === key ? null : key)}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '18px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <span style={{ fontSize: '40px', filter: 'drop-shadow(0 2px 6px #0002)' }}>
                  {analyzer.icon}
                </span>
                <div>
                  <h3 style={{ 
                    margin: 0, 
                    fontSize: '22px', 
                    fontWeight: 'bold', 
                    color: '#fff',
                    marginBottom: '4px',
                    letterSpacing: '0.5px',
                  }}>
                    {analyzer.name}
                  </h3>
                  <span style={{ 
                    color: getStatusColor(analyzer.status),
                    fontSize: '16px',
                    fontWeight: 'bold',
                    textTransform: 'lowercase',
                    letterSpacing: '0.5px',
                  }}>
                    {analyzer.status}
                  </span>
                </div>
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
              {Object.entries(analyzer.metrics).map(([metricKey, value]) => (
                <div key={metricKey} style={{
                  background: 'rgba(0, 0, 0, 0.32)',
                  borderRadius: '10px',
                  padding: '14px 16px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '6px',
                  boxShadow: '0 2px 8px #0001',
                }}>
                  <span style={{ 
                    fontSize: '13px', 
                    color: '#e0e0e0',
                    textTransform: 'lowercase',
                    fontWeight: '500',
                    letterSpacing: '0.2px',
                  }}>
                    {metricKey.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                  </span>
                  <span style={{ 
                    fontSize: '18px', 
                    fontWeight: 'bold', 
                    color: '#fff',
                    lineHeight: '1.2',
                    textShadow: '0 1px 4px #0002',
                  }}>
                    {metricKey.includes('Time') || metricKey.includes('Analysis') || metricKey.includes('Assessment')
                      ? formatTime(value)
                      : value
                    }
                  </span>
                </div>
              ))}
            </div>

            {selectedAnalyzer === key && (
              <div style={{
                marginTop: '18px',
                padding: '18px',
                background: 'rgba(0, 0, 0, 0.22)',
                borderRadius: '10px',
                borderTop: '2px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 2px 8px #0001',
              }}>
                <h4 style={{ 
                  margin: '0 0 12px 0', 
                  fontSize: '16px', 
                  color: '#fff',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '7px',
                  fontWeight: 'bold',
                  letterSpacing: '0.3px',
                }}>
                  📋 Detalhes Adicionais
                </h4>
                
                {analyzer.recentAnalyses && (
                        borderRadius: '4px',
                        padding: '6px 8px',
                        marginBottom: '4px',
                        fontSize: '11px',
                        color: '#fff'
                      }}>
                        <span>{analysis.childId}</span>
                        <span>{analysis.game}</span>
                        <span>Score: {analysis.score}</span>
                        <span>{formatTime(analysis.timestamp)}</span>
                      </div>
                    ))}
                  </div>
                )}

                {analyzer.domains && (
                  <div className={styles.detailSection}>
                    <h5>Domínios Cognitivos:</h5>
                    <div className={styles.domainsList}>
                      {analyzer.domains.map(domain => (
                        <span key={domain} className={styles.domainTag}>
                          {domain.replace(/_/g, ' ')}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {analyzer.approaches && (
                  <div className={styles.detailSection}>
                    <h5>Abordagens Terapêuticas:</h5>
                    <div className={styles.approachesList}>
                      {analyzer.approaches.map(approach => (
                        <span key={approach} className={styles.approachTag}>
                          {approach}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '20px',
        margin: '20px 0',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          gap: '20px'
        }}>
          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>🔬</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#fff', marginBottom: '2px' }}>
              {Object.keys(analyzersData).length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Analisadores Ativos</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>📈</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {Object.values(analyzersData).reduce((sum, analyzer) => 
                sum + (analyzer.metrics.analysesPerformed || analyzer.metrics.cognitiveAssessments || analyzer.metrics.progressReports || analyzer.metrics.sessionsAnalyzed || analyzer.metrics.therapeuticAnalyses || 0), 0
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Total de Análises</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>⚡</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', marginBottom: '2px' }}>
              {(Object.values(analyzersData).reduce((sum, analyzer) => 
                sum + parseFloat(analyzer.metrics.cacheHitRate || analyzer.metrics.avgConfidence || analyzer.metrics.improvementRate || analyzer.metrics.avgEngagement || analyzer.metrics.outcomeSuccess || 0.8), 0
              ) / Object.keys(analyzersData).length).toFixed(2)}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Performance Média</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>✅</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {Object.values(analyzersData).filter(a => a.status === 'healthy').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Saudáveis</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export { AnalyzersMonitor }
export default AnalyzersMonitor
