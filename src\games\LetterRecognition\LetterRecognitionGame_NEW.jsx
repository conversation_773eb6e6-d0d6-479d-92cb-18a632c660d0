/**
 * 📚 LETTER RECOGNITION GAME - Portal Betina V3
 * Layout baseado no padrão MemoryGame
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { SystemContext } from '../../components/context/SystemContext.jsx';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';

// Importa estilos modulares
import styles from './LetterRecognition.module.css';

// Configurações do jogo
const LETTERS = [
  { id: 'a', letter: 'A', sound: 'A', example: '🐝 Abelha', color: '#FF6B6B' },
  { id: 'b', letter: 'B', sound: 'Bê', example: '⚽ Bola', color: '#4ECDC4' },
  { id: 'c', letter: 'C', sound: 'Cê', example: '🏠 Casa', color: '#45B7D1' },
  { id: 'd', letter: 'D', sound: 'Dê', example: '🐕 Dado', color: '#96CEB4' },
  { id: 'e', letter: 'E', sound: 'É', example: '🐘 Elefante', color: '#FECA57' }
];

const ACTIVITY_TYPES = {
  letter_selection: {
    id: 'letter_selection',
    name: 'Seleção de Letras',
    icon: '🔤',
    description: 'Encontre a letra indicada'
  },
  sound_matching: {
    id: 'sound_matching', 
    name: 'Som e Letra',
    icon: '🔊',
    description: 'Relacione sons com letras'
  },
  word_formation: {
    id: 'word_formation',
    name: 'Formar Palavras',
    icon: '📝',
    description: 'Monte palavras com letras'
  }
};

function LetterRecognitionGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const sessionIdRef = useRef(uuidv4());
  
  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();
  
  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO
  // =====================================================
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('letterRecognition_ttsActive');
    return saved !== null ? JSON.parse(saved) : ttsEnabled;
  });

  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newValue = !prev;
      localStorage.setItem('letterRecognition_ttsActive', JSON.stringify(newValue));
      return newValue;
    });
  }, []);

  // Hook unificado
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('letter_recognition');

  // Estado local do jogo
  const [gameState, setGameState] = useState({
    status: 'menu', // 'menu', 'playing', 'paused', 'finished'
    currentActivity: 'letter_selection',
    targetLetter: null,
    letters: [],
    score: 0,
    round: 1,
    accuracy: 100,
    activityRoundCount: 0
  });

  // =====================================================
  // 🎯 FUNÇÕES PRINCIPAIS DO JOGO
  // =====================================================

  const startGame = useCallback(async (difficulty = 'medium') => {
    try {
      // Iniciar sessão unificada
      const sessionResult = await startUnifiedSession(difficulty);
      
      if (sessionResult.success) {
        console.log('✅ Sessão LetterRecognition iniciada:', sessionResult.sessionId);
      }

      // Configurar jogo local
      const targetLetter = LETTERS[Math.floor(Math.random() * LETTERS.length)];
      const shuffledLetters = [...LETTERS].sort(() => Math.random() - 0.5);

      setGameState({
        status: 'playing',
        currentActivity: 'letter_selection',
        targetLetter,
        letters: shuffledLetters,
        score: 0,
        round: 1,
        accuracy: 100,
        activityRoundCount: 1
      });

    } catch (error) {
      console.error('❌ Erro ao iniciar jogo:', error);
      // Iniciar em modo local mesmo com erro
      const targetLetter = LETTERS[Math.floor(Math.random() * LETTERS.length)];
      const shuffledLetters = [...LETTERS].sort(() => Math.random() - 0.5);

      setGameState({
        status: 'playing',
        currentActivity: 'letter_selection',
        targetLetter,
        letters: shuffledLetters,
        score: 0,
        round: 1,
        accuracy: 100,
        activityRoundCount: 1
      });
    }
  }, [startUnifiedSession]);

  const handleLetterClick = useCallback(async (selectedLetter) => {
    const isCorrect = selectedLetter.id === gameState.targetLetter.id;
    
    try {
      // Registrar interação
      if (recordInteraction) {
        await recordInteraction('letter_selection', {
          selectedLetter: selectedLetter.id,
          targetLetter: gameState.targetLetter.id,
          isCorrect
        }, isCorrect);
      }

      // Atualizar estado local
      setGameState(prev => ({
        ...prev,
        score: prev.score + (isCorrect ? 10 : 0),
        round: prev.round + 1,
        activityRoundCount: prev.activityRoundCount + 1
      }));

      // Gerar nova rodada
      setTimeout(() => {
        const newTargetLetter = LETTERS[Math.floor(Math.random() * LETTERS.length)];
        const newShuffledLetters = [...LETTERS].sort(() => Math.random() - 0.5);
        
        setGameState(prev => ({
          ...prev,
          targetLetter: newTargetLetter,
          letters: newShuffledLetters
        }));
      }, 1500);

    } catch (error) {
      console.error('❌ Erro ao processar clique:', error);
    }
  }, [gameState.targetLetter, recordInteraction]);

  const changeActivity = useCallback((activityId) => {
    if (gameState.activityRoundCount >= 4) {
      setGameState(prev => ({
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0
      }));
    }
  }, [gameState.activityRoundCount]);

  // =====================================================
  // 🎨 RENDERIZADORES DE ATIVIDADES
  // =====================================================

  const renderLetterSelection = () => (
    <div className={styles.letterSelectionActivity}>
      {gameState.targetLetter && (
        <>
          <div className={styles.letterInstruction}>
            <h3>Encontre a letra:</h3>
            <div className={styles.targetLetter}>
              {gameState.targetLetter.letter}
            </div>
            <p>{gameState.targetLetter.example}</p>
          </div>
          
          <div className={styles.lettersGrid}>
            {gameState.letters.map((letter, index) => (
              <motion.button
                key={letter.id}
                className={styles.letterCard}
                onClick={() => handleLetterClick(letter)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                style={{ backgroundColor: letter.color }}
              >
                {letter.letter}
              </motion.button>
            ))}
          </div>
        </>
      )}
    </div>
  );

  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case 'letter_selection':
        return renderLetterSelection();
      case 'sound_matching':
        return <div className={styles.activityPlaceholder}>🔊 Som e Letra - Em desenvolvimento</div>;
      case 'word_formation':
        return <div className={styles.activityPlaceholder}>📝 Formar Palavras - Em desenvolvimento</div>;
      default:
        return renderLetterSelection();
    }
  };

  // =====================================================
  // 🎨 RENDER PRINCIPAL
  // =====================================================

  // Tela de menu inicial
  if (gameState.status === 'menu') {
    return (
      <GameStartScreen
        title="📚 Reconhecimento de Letras"
        description="Aprenda letras, sons e palavras de forma divertida!"
        features={[
          "🔤 Reconhecimento visual de letras",
          "🔊 Associação de sons e letras", 
          "📝 Formação de palavras",
          "🎯 Progressão adaptativa"
        ]}
        onStartGame={startGame}
        onBack={onBack}
      />
    );
  }

  // Jogo principal
  return (
    <div 
      className={`${styles.letterRecognitionGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      <div className={styles.gameContent}>
        {/* Header do jogo */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>📚 Reconhecimento de Letras</h1>
          <button 
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            aria-label={ttsActive ? "Desativar TTS" : "Ativar TTS"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* 🎯 SISTEMA DE ATIVIDADES - PADRÃO MEMORYGAME EXATO */}
        {gameState.status === 'playing' && (
          <>
            {/* Menu de Atividades */}
            <div className={styles.activityMenu}>
              {Object.values(ACTIVITY_TYPES).map((activity) => {
                const isCurrentActivity = gameState.currentActivity === activity.id;
                const canSwitch = gameState.activityRoundCount >= 4 || isCurrentActivity;
                
                return (
                  <button
                    key={activity.id}
                    className={`${styles.activityButton} ${
                      isCurrentActivity ? styles.active : ''
                    }`}
                    onClick={() => changeActivity(activity.id)}
                    title={
                      !canSwitch 
                        ? `Complete ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro`
                        : activity.description
                    }
                    disabled={!canSwitch}
                  >
                    <span className={styles.activityIcon}>{activity.icon}</span>
                    <span className={styles.activityName}>{activity.name}</span>
                    {isCurrentActivity && (
                      <span className={styles.activeIndicator}>●</span>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Estatísticas do jogo */}
            <div className={styles.gameStats}>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.round}</div>
                <div className={styles.statLabel}>Rodada</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.score}</div>
                <div className={styles.statLabel}>Pontos</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.accuracy}%</div>
                <div className={styles.statLabel}>Precisão</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.activityRoundCount}/4+</div>
                <div className={styles.statLabel}>Progresso</div>
              </div>
            </div>

            {/* Interface da atividade atual */}
            {renderCurrentActivity()}
          </>
        )}
      </div>
    </div>
  );
}

export default LetterRecognitionGame;
