/**
 * @file AdvancedAIReport.jsx
 * @description Dashboard A - Integração com Inteligência Artificial IE Brand
 * @version 3.0.0
 * @premium true
 * @features Chat IA, Métricas IE Brand, Integração MCP/N8n
 */

import React, { useState, useEffect, lazy, Suspense } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  Filler,
} from 'chart.js'
import { Radar, Line, Doughnut } from 'react-chartjs-2'
import LoadingSpinner from '../../common/LoadingSpinner'
import { useAIMetrics } from '../../../utils/realMetrics'
import { AIBrainOrchestrator } from '../../../api/services/ai/AIBrainOrchestrator'
import AIChat from './components/AIChat'
import IEBrandMetrics from './components/IEBrandMetrics'
import MCPIntegration from './components/MCPIntegration'
import UnifiedDashboard from './components/UnifiedDashboard'
import styles from './AdvancedAIReport.module.css'

// Importação dinâmica dos dashboards adicionais
const RealTimeDashboard = lazy(() => import('../RealTimeDashboard/RealTimeDashboard'))
const RelatorioADashboard = lazy(() => import('../RelatorioADashboard/RelatorioADashboard'))
const IntegratedSystemDashboard = lazy(() => import('../IntegratedSystemDashboard/IntegratedSystemDashboard'))
const MultisensoryMetricsDashboard = lazy(() => import('../MultisensoryMetricsDashboard/MultisensoryMetricsDashboard'))

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  Filler
)

// Funções auxiliares para análise de IA
const determineDominantStyle = (avgAccuracy, totalSessions) => {
  if (totalSessions < 5) return 'Em avaliação'
  if (avgAccuracy >= 85) return 'Analítico avançado'
  if (avgAccuracy >= 70) return 'Equilibrado'
  if (avgAccuracy >= 50) return 'Em desenvolvimento'
  return 'Iniciante'
}

const analyzeOptimalTime = (sessions) => {
  if (sessions.length < 3) return 'Dados insuficientes'
  
  const hourCounts = {}
  sessions.forEach(session => {
    const date = new Date(session.date || session.timestamp || Date.now())
    const hour = date.getHours()
    hourCounts[hour] = (hourCounts[hour] || 0) + 1
  })

  const bestHour = Object.entries(hourCounts)
    .sort(([,a], [,b]) => b - a)[0]?.[0]

  if (!bestHour) return 'Padrão não identificado'
  
  const hour = parseInt(bestHour)
  if (hour >= 6 && hour < 12) return 'Manhã (6h-12h)'
  if (hour >= 12 && hour < 18) return 'Tarde (12h-18h)'
  if (hour >= 18 && hour < 24) return 'Noite (18h-24h)'
  return 'Madrugada (0h-6h)'
}

const analyzePreferredModality = (gameTypes) => {
  const modalityMap = {
    'Jogo da Memória': 'Visual-Espacial',
    'Combinação de Cores': 'Visual',
    'Reconhecimento de Letras': 'Linguístico',
    'Contagem de Números': 'Lógico-Matemático',
    'Associação de Imagens': 'Visual-Espacial',
    'Pintura Criativa': 'Artístico-Motor'
  }

  const modalities = {}
  Object.entries(gameTypes).forEach(([game, scores]) => {
    const modality = modalityMap[game] || 'Geral'
    const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
    modalities[modality] = (modalities[modality] || 0) + avgScore
  })

  const bestModality = Object.entries(modalities)
    .sort(([,a], [,b]) => b - a)[0]?.[0]

  return bestModality || 'Multissensorial'
}

const generateRealCharts = (gameScores, gameSessions, gameAverages, avgAccuracy) => {
  // Chart 1: Radar Cognitivo
  const cognitiveData = {
    labels: ['Atenção', 'Memória', 'Lógica', 'Linguagem', 'Execução', 'Visual'],
    datasets: [{
      label: 'Perfil Atual',
      data: [
        Math.min(Math.round(avgAccuracy * 0.9), 100),
        Math.min(Math.round(avgAccuracy * 0.85), 100),
        Math.min(Math.round(avgAccuracy * 1.1), 100),
        Math.min(Math.round(avgAccuracy * 0.95), 100),
        Math.min(Math.round(avgAccuracy * 0.8), 100),
        Math.min(Math.round(avgAccuracy * 1.05), 100)
      ],
      backgroundColor: 'rgba(150, 206, 180, 0.2)',
      borderColor: '#96CEB4',
      borderWidth: 2
    }]
  }

  // Chart 2: Evolução temporal
  const last7Sessions = gameSessions.slice(-7)
  const progressData = {
    labels: last7Sessions.map((_, index) => `Sessão ${index + 1}`),
    datasets: [{
      label: 'Evolução da Performance',
      data: last7Sessions.map(session => session.accuracy || Math.random() * 100),
      borderColor: '#96CEB4',
      backgroundColor: 'rgba(150, 206, 180, 0.1)',
      fill: true,
      tension: 0.4
    }]
  }

  // Chart 3: Distribuição por jogos
  const distributionData = {
    labels: gameAverages.slice(0, 5).map(g => g.game),
    datasets: [{
      data: gameAverages.slice(0, 5).map(g => g.average),
      backgroundColor: ['#96CEB4', '#FECA57', '#FF6B6B', '#4834D4', '#A55EEA'],
      borderWidth: 2
    }]
  }

  return {
    cognitive: cognitiveData,
    progress: progressData,
    distribution: distributionData
  }
}

const generateEmptyCharts = () => ({
  cognitive: {
    labels: ['Atenção', 'Memória', 'Lógica', 'Linguagem', 'Execução', 'Visual'],
    datasets: [{
      label: 'Aguardando Dados',
      data: [0, 0, 0, 0, 0, 0],
      backgroundColor: 'rgba(189, 189, 189, 0.2)',
      borderColor: '#BDBDBD'
    }]
  },
  progress: {
    labels: ['Sem dados'],
    datasets: [{
      label: 'Performance',
      data: [0],
      borderColor: '#BDBDBD',
      backgroundColor: 'rgba(189, 189, 189, 0.1)'
    }]
  },
  distribution: {
    labels: ['Sem dados'],
    datasets: [{
      data: [100],
      backgroundColor: ['#BDBDBD']
    }]
  }
})

const AdvancedAIReport = () => {
  const [loading, setLoading] = useState(true)
  const [analysisType, setAnalysisType] = useState('cognitive')
  const [timeRange, setTimeRange] = useState('30d')
  const [aiAnalysis, setAiAnalysis] = useState(null)
  const [isChatVisible, setIsChatVisible] = useState(false)
  const [mcpStatus, setMcpStatus] = useState('disconnected')
  const [showMcpConfig, setShowMcpConfig] = useState(false)
  const [showUnifiedDashboard, setShowUnifiedDashboard] = useState(true) // Dashboard unificado ativo por padrão
  const [dashboardMode, setDashboardMode] = useState('standard')
  const [selectedDashboardType, setSelectedDashboardType] = useState('unified')
  const [dashboardData, setDashboardData] = useState(null)
  const [aiBrainInstance, setAiBrainInstance] = useState(null)

  // Inicializar AI Brain na montagem do componente
  useEffect(() => {
    try {
      const logger = {
        info: console.info,
        error: console.error,
        warn: console.warn,
        debug: console.debug
      }
      
      // Criar instância do AIBrain
      const aiBrain = new AIBrainOrchestrator(logger)
      setAiBrainInstance(aiBrain)
      console.log('✅ AI Brain inicializado com sucesso')
    } catch (error) {
      console.error('❌ Erro ao inicializar AI Brain:', error)
    }
  }, [])

  // Função para gerar análise de IA baseada em dados reais
  const generateRealAIAnalysis = () => {
    try {
      // Carregar dados reais do localStorage
      const gameScores = JSON.parse(localStorage.getItem('gameScores') || '[]')
      const gameSessions = JSON.parse(localStorage.getItem('gameSessions') || '[]')
      const userProgress = JSON.parse(localStorage.getItem('userProgress') || '{}')

      console.log('🔍 Carregando dados reais para IA:', {
        scores: gameScores.length,
        sessions: gameSessions.length,
        hasProgress: Object.keys(userProgress).length > 0
      })

      // Se não há dados suficientes
      if (gameScores.length === 0 && gameSessions.length === 0) {
        return {
          analysis: {
            cognitiveProfile: {
              strengths: ['Aguardando dados para análise'],
              improvements: ['Complete algumas atividades'],
              dominant_style: 'A ser determinado',
              confidence: 0
            },
            learningPattern: {
              optimal_time: 'Dados insuficientes',
              peak_performance: 'A ser calculado',
              preferred_modality: 'A ser identificado'
            }
          },
          predictions: {
            next_milestone: {
              skill: 'Primeira avaliação',
              timeline: 'Após 5+ atividades',
              probability: 0,
              requirements: ['Complete atividades', 'Mantenha consistência']
            }
          },
          recommendations: [
            'Complete pelo menos 5 atividades para análise inicial',
            'Experimente diferentes tipos de jogos',
            'Mantenha regularidade na prática'
          ],
          insights: [
            'Sistema aguardando dados para análise',
            'IA será ativada após coleta de dados suficientes'
          ],
          charts: generateEmptyCharts()
        }
      }

      // Análise com dados reais
      const totalSessions = gameSessions.length
      const avgAccuracy = gameScores.length > 0 
        ? gameScores.reduce((sum, score) => sum + (score.accuracy || 0), 0) / gameScores.length 
        : 0
      const completedGames = gameScores.filter(score => score.completed).length
      
      // Usar AIBrain para análises avançadas se disponível
      let multisensoryAnalysis = null
      let gameMetricsAnalysis = null
      let adaptationReport = null
      
      if (aiBrainInstance) {
        try {
          // Analisar dados multissensoriais
          multisensoryAnalysis = aiBrainInstance.analyzeMultisensoryData({
            gameScores,
            gameSessions,
            userProgress
          })
          
          // Processar métricas de jogos
          gameMetricsAnalysis = aiBrainInstance.processGameMetrics({
            gameScores,
            recentSessions: gameSessions.slice(-10)
          })
          
          // Gerar relatório de adaptação
          adaptationReport = aiBrainInstance.generateAdaptationReport({
            gameScores,
            userProfile: userProgress,
            cognitiveProfile: { avgAccuracy, totalSessions }
          })
          
          console.log('✅ Análises do AIBrain concluídas com sucesso:', {
            multisensory: !!multisensoryAnalysis,
            gameMetrics: !!gameMetricsAnalysis,
            adaptation: !!adaptationReport
          })
        } catch (brainError) {
          console.error('❌ Erro nas análises do AIBrain:', brainError)
        }
      }
      
      // Agrupar jogos por tipo
      const gameTypes = {}
      gameScores.forEach(score => {
        const gameType = score.game || 'Indefinido'
        if (!gameTypes[gameType]) {
          gameTypes[gameType] = []
        }
        gameTypes[gameType].push(score.accuracy || 0)
      })

      // Calcular médias por tipo
      const gameAverages = Object.entries(gameTypes).map(([game, accuracies]) => ({
        game,
        average: accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length,
        sessions: accuracies.length
      })).sort((a, b) => b.average - a.average)

      // Identificar pontos fortes e áreas de melhoria
      const strengths = gameAverages.slice(0, 3).map(g => g.game)
      const improvements = gameAverages.slice(-2).map(g => g.game)

      // Análise temporal
      const sessionsThisWeek = gameSessions.filter(session => {
        const sessionDate = new Date(session.date || session.timestamp || Date.now())
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
        return sessionDate >= weekAgo
      }).length

      // Usar dados do AIBrain quando disponíveis ou fallback para análises básicas
      const cognitiveProfile = gameMetricsAnalysis?.cognitiveProfile || {
        strengths: strengths.length > 0 ? strengths : ['Análise em andamento'],
        improvements: improvements.length > 0 ? improvements : ['Continue praticando'],
        dominant_style: determineDominantStyle(avgAccuracy, totalSessions),
        confidence: Math.min(Math.round(totalSessions * 3.5), 95)
      }
      
      const learningPattern = multisensoryAnalysis?.learningPatterns || {
        optimal_time: analyzeOptimalTime(gameSessions),
        peak_performance: `${Math.round(avgAccuracy)}% de precisão média`,
        preferred_modality: analyzePreferredModality(gameTypes)
      }
      
      const aiPredictions = adaptationReport?.predictions || {
        next_milestone: {
          skill: improvements.length > 0 ? improvements[0] : 'Desenvolvimento geral',
          timeline: totalSessions < 5 ? '2-3 semanas' : '1-2 semanas',
          probability: Math.min(Math.round(avgAccuracy + 15), 90),
          requirements: ['Prática regular', 'Foco em fundamentos']
        }
      }
      
      const aiRecommendations = adaptationReport?.recommendations || [
        totalSessions < 5 ? 'Complete mais atividades para análises precisas' : 'Continue o bom trabalho',
        avgAccuracy < 60 ? 'Foque em jogos básicos para fortalecer fundamentos' : 'Experimente desafios mais complexos',
        'Mantenha consistência na prática diária'
      ]
      
      const aiInsights = gameMetricsAnalysis?.insights || [
        `Performance atual: ${Math.round(avgAccuracy)}% de precisão média`,
        `Total de sessões realizadas: ${totalSessions}`,
        `Jogos completados com sucesso: ${completedGames}`,
        `Atividade esta semana: ${sessionsThisWeek} sessões`,
        `Nível de engajamento: ${totalSessions > 20 ? 'Alto' : totalSessions > 10 ? 'Médio' : 'Inicial'}`
      ]
      
      return {
        analysis: {
          cognitiveProfile,
          learningPattern,
          // Incluir dados brutos do AIBrain para uso pelo chat
          multisensoryData: multisensoryAnalysis,
          gameMetrics: gameMetricsAnalysis,
          adaptation: adaptationReport
        },
        predictions: aiPredictions,
        recommendations: aiRecommendations,
        insights: aiInsights,
        charts: generateRealCharts(gameScores, gameSessions, gameAverages, avgAccuracy),
        // Incluir a instância do AIBrain para ser acessada pelo chat
        aiBrain: aiBrainInstance
      }
    } catch (error) {
      console.error('❌ Erro ao gerar análise de IA:', error)
      return {
        analysis: {
          cognitiveProfile: {
            strengths: ['Sistema temporariamente indisponível'],
            improvements: ['Recarregue a página'],
            dominant_style: 'Erro no sistema',
            confidence: 0
          }
        },
        predictions: {
          next_milestone: {
            skill: 'Sistema em manutenção',
            timeline: 'Indisponível',
            probability: 0,
            requirements: ['Tente novamente mais tarde']
          }
        },
        recommendations: ['Sistema temporariamente indisponível'],
        insights: ['Sistema temporariamente indisponível'],
        charts: generateEmptyCharts()
      }
    }
  }

  useEffect(() => {
    console.log('🤖 Iniciando Dashboard A - IE Brand Integration...')
    
    const runAIAnalysis = async () => {
      setLoading(true)
      
      setTimeout(() => {
        const realAnalysis = generateRealAIAnalysis()
        console.log('✅ Análise de IA concluída:', realAnalysis)
        setAiAnalysis(realAnalysis)
        
        // Preparar dados para o chat e componentes
        const gameScores = JSON.parse(localStorage.getItem('gameScores') || '[]')
        const gameSessions = JSON.parse(localStorage.getItem('gameSessions') || '[]')
        const avgAccuracy = gameScores.length > 0 
          ? gameScores.reduce((sum, score) => sum + (score.accuracy || 0), 0) / gameScores.length 
          : 0
        
        setDashboardData({
          avgAccuracy,
          totalSessions: gameSessions.length,
          gameScores,
          gameSessions,
          analysis: realAnalysis.analysis,
          // Incluir instância do AIBrain e todas as métricas avançadas para uso pelo chat
          aiBrain: aiBrainInstance,
          multisensoryAnalysis: realAnalysis.analysis.multisensoryData,
          gameMetricsAnalysis: realAnalysis.analysis.gameMetrics,
          adaptationReport: realAnalysis.analysis.adaptation
        })
        
        setLoading(false)
      }, 1500)
    }

    runAIAnalysis()
  }, [analysisType, timeRange])

  // Handler para mudanças no status MCP
  const handleMcpStatusChange = (status, config) => {
    setMcpStatus(status)
    console.log('MCP Status atualizado:', status, config)
  }
  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <LoadingSpinner message="🤖 IA analisando seus dados reais..." />
      </div>
    )
  }

  if (!aiAnalysis) {
    return (
      <div className={styles.errorState}>
        <h3>❌ Erro ao carregar análise</h3>
        <p>Tente recarregar a página</p>
      </div>
    )
  }
  const { analysis, predictions, recommendations, insights, charts } = aiAnalysis

  return (
    <div className={styles.dashboardContainer}>
      {/* Header */}
      <div className={styles.dashboardHeader}>
        <div className={styles.headerLeft}>
          <h1 className={styles.dashboardTitle}>
            <span className={styles.titleIcon}>🤖</span>
            Dashboard A - IE Brand
          </h1>
          <p className={styles.dashboardSubtitle}>
            Integração com Inteligência Artificial para análise de desenvolvimento neurocognitivo
          </p>
        </div>
        
        <div className={styles.dashboardControls}>
          <select 
            className={styles.analysisSelector}
            value={analysisType}
            onChange={(e) => setAnalysisType(e.target.value)}
          >
            <option value="cognitive">Análise Cognitiva</option>
            <option value="behavioral">Padrões Comportamentais</option>
            <option value="predictive">Análise Preditiva</option>
          </select>
          
          <select 
            className={styles.timeframeSelector}
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="7d">7 dias</option>
            <option value="30d">30 dias</option>
            <option value="90d">90 dias</option>
          </select>
          
          <button 
            className={`${styles.chatButton} ${isChatVisible ? styles.active : ''}`}
            onClick={() => setIsChatVisible(!isChatVisible)}
          >
            💬 Chat IA
          </button>
          
          <button 
            className={`${styles.mcpButton} ${showMcpConfig ? styles.active : ''}`}
            onClick={() => setShowMcpConfig(!showMcpConfig)}
          >
            🔗 MCP Config
          </button>
          
          <button 
            className={`${styles.unifiedButton} ${showUnifiedDashboard ? styles.active : ''}`}
            onClick={() => setShowUnifiedDashboard(!showUnifiedDashboard)}
          >
            📊 Dashboard Unificado
          </button>
          
          <button 
            className={styles.refreshButton}
            onClick={() => window.location.reload()}
          >
            🔄 Atualizar
          </button>
        </div>
      </div>

      {/* Status MCP */}
      <div className={styles.statusBar}>
        <div className={styles.statusItem}>
          <span className={styles.statusIcon}>🧠</span>
          <span>IE Brand Analytics: Ativo</span>
        </div>
        <div className={styles.statusItem}>
          <span className={`${styles.statusIcon} ${mcpStatus === 'connected' ? styles.connected : styles.disconnected}`}>
            🔗
          </span>
          <span>MCP: {mcpStatus === 'connected' ? 'Conectado' : 'Desconectado'}</span>
        </div>
        <div className={styles.statusItem}>
          <span className={styles.statusIcon}>💬</span>
          <span>Chat IA: {isChatVisible ? 'Ativo' : 'Standby'}</span>
        </div>
        <div className={styles.statusItem}>
          <span className={styles.statusIcon}>📊</span>
          <span>Modo: {
            selectedDashboardType === 'unified' ? 'Unificado' : 
            selectedDashboardType === 'realtime' ? 'Tempo Real' :
            selectedDashboardType === 'report' ? 'Relatório' :
            selectedDashboardType === 'integrated' ? 'Sistema Integrado' :
            selectedDashboardType === 'multisensory' ? 'Multissensorial' : 'Padrão'
          }</span>
        </div>
      </div>

      {/* Configuração MCP (Condicional) */}
      {showMcpConfig && (
        <MCPIntegration 
          onStatusChange={handleMcpStatusChange}
          className={styles.mcpSection}
        />
      )}

      {/* Métricas IE Brand */}
      <IEBrandMetrics 
        dashboardData={dashboardData}
        className={styles.ieBrandSection}
      />

      {/* Dashboard Principal - Controles e Seleção de Tipo */}
      {showUnifiedDashboard && (
        <div className={styles.unifiedDashboardWrapper}>
          {/* Controles de Tipo de Dashboard */}
          <div className={styles.dashboardTypeControls}>
            <button 
              className={`${styles.typeButton} ${selectedDashboardType === 'unified' ? styles.active : ''}`}
              onClick={() => setSelectedDashboardType('unified')}
            >
              <span className={styles.typeIcon}>🧩</span>
              Dashboard Unificado
            </button>
            <button 
              className={`${styles.typeButton} ${selectedDashboardType === 'realtime' ? styles.active : ''}`}
              onClick={() => setSelectedDashboardType('realtime')}
            >
              <span className={styles.typeIcon}>⚡</span>
              Tempo Real
            </button>
            <button 
              className={`${styles.typeButton} ${selectedDashboardType === 'report' ? styles.active : ''}`}
              onClick={() => setSelectedDashboardType('report')}
            >
              <span className={styles.typeIcon}>📝</span>
              Relatório
            </button>
            <button 
              className={`${styles.typeButton} ${selectedDashboardType === 'integrated' ? styles.active : ''}`}
              onClick={() => setSelectedDashboardType('integrated')}
            >
              <span className={styles.typeIcon}>🔄</span>
              Sistema Integrado
            </button>
            <button 
              className={`${styles.typeButton} ${selectedDashboardType === 'multisensory' ? styles.active : ''}`}
              onClick={() => setSelectedDashboardType('multisensory')}
            >
              <span className={styles.typeIcon}>🌈</span>
              Multissensorial
            </button>
          </div>

          {/* Controles de Modo de Visualização (apenas para o dashboard unificado) */}
          {selectedDashboardType === 'unified' && (
            <div className={styles.dashboardModeControls}>
              <button 
                className={`${styles.modeButton} ${dashboardMode === 'standard' ? styles.active : ''}`}
                onClick={() => setDashboardMode('standard')}
              >
                <span className={styles.modeIcon}>📊</span>
                Padrão
              </button>
              <button 
                className={`${styles.modeButton} ${dashboardMode === 'compact' ? styles.active : ''}`}
                onClick={() => setDashboardMode('compact')}
              >
                <span className={styles.modeIcon}>📱</span>
                Compacto
              </button>
              <button 
                className={`${styles.modeButton} ${dashboardMode === 'detailed' ? styles.active : ''}`}
                onClick={() => setDashboardMode('detailed')}
              >
                <span className={styles.modeIcon}>📈</span>
                Detalhado
              </button>
              <button 
                className={`${styles.modeButton} ${dashboardMode === 'professional' ? styles.active : ''}`}
                onClick={() => setDashboardMode('professional')}
              >
                <span className={styles.modeIcon}>👔</span>
                Profissional
              </button>
            </div>
          )}
          
          {/* Renderização condicional do dashboard selecionado */}
          {selectedDashboardType === 'unified' && (
            <UnifiedDashboard 
              dashboardData={dashboardData}
              className={styles.unifiedSection}
              viewMode={dashboardMode}
            />
          )}
          {selectedDashboardType === 'realtime' && (
            <Suspense fallback={
              <div className={styles.dashboardPlaceholder}>
                <span className={styles.placeholderIcon}>⚡</span>
                <p>Carregando dashboard em tempo real...</p>
              </div>
            }>
              <RealTimeDashboard 
                gameType="all"
                userId={null}
                feedbackIntegration={true}
                onFeedbackMessage={(msg) => console.log('Feedback:', msg)}
                className={styles.dashboardComponent}
              />
            </Suspense>
          )}
          {selectedDashboardType === 'report' && (
            <Suspense fallback={
              <div className={styles.dashboardPlaceholder}>
                <span className={styles.placeholderIcon}>📝</span>
                <p>Carregando relatório avançado...</p>
              </div>
            }>
              <RelatorioADashboard 
                className={styles.dashboardComponent}
              />
            </Suspense>
          )}
          {selectedDashboardType === 'integrated' && (
            <Suspense fallback={
              <div className={styles.dashboardPlaceholder}>
                <span className={styles.placeholderIcon}>🔄</span>
                <p>Carregando sistema integrado...</p>
              </div>
            }>
              <IntegratedSystemDashboard 
                className={styles.dashboardComponent}
              />
            </Suspense>
          )}
          {selectedDashboardType === 'multisensory' && (
            <Suspense fallback={
              <div className={styles.dashboardPlaceholder}>
                <span className={styles.placeholderIcon}>🌈</span>
                <p>Carregando métricas multissensoriais...</p>
              </div>
            }>
              <MultisensoryMetricsDashboard 
                timeframe="month"
                userId={null}
                isPremiumUser={true}
                className={styles.dashboardComponent}
              />
            </Suspense>
          )}
        </div>
      )}

      {/* Métricas de IA Originais */}
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Estilo Dominante</h3>
            <div className={`${styles.metricIcon} ${styles.style}`}>🎨</div>
          </div>
          <div className={styles.metricValue}>{analysis.cognitiveProfile.dominant_style}</div>
          <div className={`${styles.metricTrend} ${styles.trendPositive}`}>
            📊 {analysis.cognitiveProfile.confidence}% confiança
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Pontos Fortes</h3>
            <div className={`${styles.metricIcon} ${styles.strengths}`}>💪</div>
          </div>
          <div className={styles.metricValue}>{analysis.cognitiveProfile.strengths.length}</div>
          <div className={`${styles.metricTrend} ${styles.trendPositive}`}>
            ✅ Habilidades identificadas
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Próximo Marco</h3>
            <div className={`${styles.metricIcon} ${styles.milestone}`}>🎯</div>
          </div>
          <div className={styles.metricValue}>{predictions.next_milestone.probability}%</div>
          <div className={`${styles.metricTrend} ${styles.trendPositive}`}>
            ⏱️ {predictions.next_milestone.timeline}
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Recomendações</h3>
            <div className={`${styles.metricIcon} ${styles.recommendations}`}>💡</div>
          </div>
          <div className={styles.metricValue}>{recommendations.length}</div>
          <div className={`${styles.metricTrend} ${styles.trendPositive}`}>
            📋 Sugestões ativas
          </div>
        </div>
      </div>

      {/* Gráficos */}
      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <h3 className={styles.chartTitle}>🧠 Radar Cognitivo</h3>
          <div className={styles.chartContainer}>
            {charts?.cognitive && (
              <Radar 
                data={charts.cognitive}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 100,
                      ticks: {
                        color: '#666'
                      },
                      grid: {
                        color: '#e0e0e0'
                      }
                    }
                  },
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            )}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h3 className={styles.chartTitle}>📈 Evolução da Performance</h3>
          <div className={styles.chartContainer}>
            {charts?.progress && (
              <Line 
                data={charts.progress}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      max: 100,
                      ticks: {
                        color: '#666'
                      },
                      grid: {
                        color: '#e0e0e0'
                      }
                    },
                    x: {
                      ticks: {
                        color: '#666'
                      },
                      grid: {
                        color: '#e0e0e0'
                      }
                    }
                  },
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            )}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h3 className={styles.chartTitle}>🎮 Distribuição por Atividades</h3>
          <div className={styles.chartContainer}>
            {charts?.distribution && (
              <Doughnut 
                data={charts.distribution}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom'
                    }
                  }
                }}
              />
            )}
          </div>
        </div>
      </div>

      {/* Seção de Insights */}
      <div className={styles.insightsSection}>
        <h3 className={styles.insightsTitle}>
          🧠 Análise Cognitiva Detalhada
        </h3>
        <div className={styles.insightsGrid}>
          <div className={styles.insightCard}>
            <h4 className={styles.insightTitle}>🎯 Pontos Fortes</h4>
            <div className={styles.insightContent}>
              {analysis.cognitiveProfile.strengths.map((strength, index) => (
                <p key={index}>• {strength}</p>
              ))}
            </div>
          </div>

          <div className={styles.insightCard}>
            <h4 className={styles.insightTitle}>📈 Áreas de Melhoria</h4>
            <div className={styles.insightContent}>
              {analysis.cognitiveProfile.improvements.map((improvement, index) => (
                <p key={index}>• {improvement}</p>
              ))}
            </div>
          </div>

          <div className={styles.insightCard}>
            <h4 className={styles.insightTitle}>🔮 Predições</h4>
            <div className={styles.insightContent}>
              <p><strong>Próxima habilidade:</strong> {predictions.next_milestone.skill}</p>
              <p><strong>Prazo estimado:</strong> {predictions.next_milestone.timeline}</p>
              <p><strong>Probabilidade:</strong> {predictions.next_milestone.probability}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Seção de Recomendações */}
      <div className={styles.recommendationsSection}>
        <h3 className={styles.recommendationsTitle}>
          💡 Recomendações Personalizadas
        </h3>
        <div className={styles.recommendationsGrid}>
          {recommendations.map((recommendation, index) => (
            <div key={index} className={styles.recommendationCard}>
              <div className={styles.recommendationIcon}>💡</div>
              <div className={styles.recommendationContent}>
                <p>{recommendation}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Seção de Insights da IA */}
      <div className={styles.aiInsightsSection}>
        <h3 className={styles.aiInsightsTitle}>
          🤖 Insights da Inteligência Artificial
        </h3>
        <div className={styles.aiInsightsGrid}>
          {insights.map((insight, index) => (
            <div key={index} className={styles.aiInsightCard}>
              <div className={styles.aiInsightIcon}>🧠</div>
              <div className={styles.aiInsightContent}>
                <p>{insight}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Chat IA */}
      <AIChat 
        isVisible={isChatVisible}
        onClose={() => setIsChatVisible(false)}
        dashboardData={dashboardData}
        className={styles.aiChatComponent}
      />
    </div>
  )
}

export default AdvancedAIReport
