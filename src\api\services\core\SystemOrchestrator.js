// Environment detection
const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';
const isNode = typeof process !== 'undefined' && process.versions && process.versions.node;

// Essential imports - based on real system implementation
import { AIBrainOrchestrator } from '../ai/AIBrainOrchestrator.js';
import { StructuredLogger } from './logging/StructuredLogger.js';
import { IntelligentCache } from './cache/IntelligentCache.js';
import { getHealthCheckService } from './health/HealthCheckService.js';
import { 
  getCognitiveAnalyzer, 
  getTherapeuticAnalyzer, 
  getBehavioralAnalyzer, 
  getProgressAnalyzer 
} from '../analysis/index.js';
import { GameSpecificProcessors } from '../processors/GameSpecificProcessors.js';
import crypto from 'crypto';

// System states and operation modes
export const SYSTEM_STATES = {
  INITIALIZING: 'initializing',
  READY: 'ready',
  RUNNING: 'running',
  ERROR: 'error',
};

export const OPERATION_MODES = {
  PRODUCTION: 'production',
  DEVELOPMENT: 'development',
  TESTING: 'testing',
};

const THERAPEUTIC_CONFIG = {
  retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 days
  maxSessionData: 1000,
  minSessionsForAnalysis: 3,
  minDataPointsForInsight: 10,
  confidenceThreshold: 0.75,
};

const THERAPEUTIC_SYSTEM_CONFIG = {
  therapeuticThresholds: {
    sessionDuration: 1800000, // 30 minutos max
    responseTime: 200, // ms
    memoryUsage: 150, // MB
    errorRate: 0.01, // 1%
    engagementRate: 0.7, // 70% mínimo
  },
  monitoring: {
    enabled: true,
    interval: 300000, // 5 minutos
    retentionPeriod: 7 * 24 * 60 * 60 * 1000, // 7 dias
    alertThresholds: {
      critical: 0.95,
      warning: 0.8,
      info: 0.6,
    },
  },
  optimization: {
    autoOptimize: true,
    optimizationInterval: 30 * 60 * 1000, // 30 minutos
    maxOptimizationCycles: 5,
    therapeuticTargets: {
      userEngagement: 0.8,
      sessionCompletion: 0.85,
      therapeuticGoalAchievement: 0.75,
    },
  },
  autism: {
    sensoryOptimization: true,
    cognitiveLoadManagement: true,
    adaptivePersonalization: true,
    therapeuticGoalTracking: true,
    behavioralPatternAnalysis: true,
    multisensoryIntegration: true,
  },
};

const GAME_METRICS_BASELINE = {
  ColorMatch: { avgMetrics: 62, avgCollectors: 6, avgAccuracy: 85.0, avgScore: 82.1, avgResponseTime: 2942 },
  ContagemNumeros: { avgMetrics: 89, avgCollectors: 11, avgAccuracy: 87.4, avgScore: 91.0, avgResponseTime: 1749 },
  ImageAssociation: { avgMetrics: 60, avgCollectors: 7, avgAccuracy: 88.2, avgScore: 91.6, avgResponseTime: 4105 },
  MusicalSequence: { avgMetrics: 70, avgCollectors: 9, avgAccuracy: 81.7, avgScore: 67.5, avgResponseTime: 2497 },
  QuebraCabeca: { avgMetrics: 73, avgCollectors: 9, avgAccuracy: 91.5, avgScore: 79.4, avgResponseTime: 2673 },
  MemoryGame: { avgMetrics: 42, avgCollectors: 5, avgAccuracy: 64.6, avgScore: 70.5, avgResponseTime: 3649 },
  PadroesVisuais: { avgMetrics: 40, avgCollectors: 5, avgAccuracy: 83.0, avgScore: 77.1, avgResponseTime: 2527 },
  LetterRecognition: { avgMetrics: 63, avgCollectors: 9, avgAccuracy: 79.4, avgScore: 75.0, avgResponseTime: 2089 },
  PatternMatching: { avgMetrics: 55, avgCollectors: 6, avgAccuracy: 86.3, avgScore: 84.2, avgResponseTime: 2150 },
  SequenceLearning: { avgMetrics: 48, avgCollectors: 6, avgAccuracy: 82.1, avgScore: 78.9, avgResponseTime: 2890 },
  CreativePainting: { avgMetrics: 35, avgCollectors: 6, avgAccuracy: 92.5, avgScore: 88.7, avgResponseTime: 5230 }
};

/**
 * Helper function to check if SystemOrchestrator is properly initialized
 * @returns {boolean} True if initialized
 */
export function isSystemOrchestratorInitialized() {
  return SystemOrchestrator.instance && 
    (SystemOrchestrator.instance.state === SYSTEM_STATES.READY || 
     SystemOrchestrator.instance.state === SYSTEM_STATES.RUNNING);
}

/**
 * Input Validator with enhanced validation
 */
class InputValidator {
  static validateGameInput(gameInput) {
    const errors = [];
    const warnings = [];

    if (!gameInput || typeof gameInput !== 'object') {
      errors.push('Game input must be an object');
      return { valid: false, errors, warnings };
    }

    if (!gameInput.gameId) errors.push('gameId is required');
    if (!gameInput.userId) errors.push('userId is required');
    if (!gameInput.sessionId) errors.push('sessionId is required');
    if (!gameInput.timestamp) errors.push('timestamp is required');

    if (gameInput.gameId && typeof gameInput.gameId !== 'string') errors.push('gameId must be a string');
    if (gameInput.userId && typeof gameInput.userId !== 'string') errors.push('userId must be a string');
    if (gameInput.sessionId && typeof gameInput.sessionId !== 'string') errors.push('sessionId must be a string');
    if (gameInput.timestamp && !Number.isInteger(gameInput.timestamp)) errors.push('timestamp must be a valid integer');

    if (gameInput.score !== undefined && (typeof gameInput.score !== 'number' || gameInput.score < 0 || gameInput.score > 100)) {
      errors.push('score must be a number between 0 and 100');
    }
    if (gameInput.responseTime !== undefined && (typeof gameInput.responseTime !== 'number' || gameInput.responseTime < 0 || gameInput.responseTime > 10000)) {
      errors.push('responseTime must be a number between 0 and 10000ms');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      sanitized: this.sanitizeGameInput(gameInput)
    };
  }

  static validateMetricsData(metricsData) {
    const errors = [];
    const warnings = [];

    if (!metricsData || typeof metricsData !== 'object') {
      errors.push('Metrics data must be an object');
      return { valid: false, errors, warnings };
    }

    if (!metricsData.userId) errors.push('userId is required');
    if (!metricsData.sessionId) errors.push('sessionId is required');
    if (!metricsData.gameId) errors.push('gameId is required');

    if (metricsData.therapeuticMetrics && typeof metricsData.therapeuticMetrics !== 'object') {
      warnings.push('therapeuticMetrics should be an object');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      sanitized: this.sanitizeMetricsData(metricsData)
    };
  }

  static validateEventData(eventData) {
    const errors = [];
    if (!eventData || typeof eventData !== 'object') {
      errors.push('Event data must be an object');
    }
    const required = ['sessionId', 'childId', 'sessionDuration'];
    const missing = required.filter(field => !eventData[field]);
    if (missing.length > 0) {
      errors.push(`Missing required fields: ${missing.join(', ')}`);
    }
    if (eventData.sessionDuration && (typeof eventData.sessionDuration !== 'number' || eventData.sessionDuration <= 0)) {
      errors.push('sessionDuration must be a positive number');
    }
    return {
      valid: errors.length === 0,
      errors,
      sanitized: this.sanitizeEventData(eventData)
    };
  }

  static sanitizeGameInput(gameInput) {
    const sanitized = { ...gameInput };
    if (sanitized.gameId) sanitized.gameId = String(sanitized.gameId).trim();
    if (sanitized.userId) sanitized.userId = String(sanitized.userId).trim();
    if (sanitized.sessionId) sanitized.sessionId = String(sanitized.sessionId).trim();
    if (sanitized.timestamp && !Number.isInteger(sanitized.timestamp)) {
      sanitized.timestamp = Date.now();
    }
    return sanitized;
  }

  static sanitizeMetricsData(metricsData) {
    const sanitized = { ...metricsData };
    if (sanitized.userId) sanitized.userId = String(sanitized.userId).trim();
    if (sanitized.sessionId) sanitized.sessionId = String(sanitized.sessionId).trim();
    if (sanitized.gameId) sanitized.gameId = String(sanitized.gameId).trim();
    return sanitized;
  }

  static sanitizeEventData(eventData) {
    const sanitized = { ...eventData };
    if (sanitized.type) sanitized.type = String(sanitized.type).trim();
    if (sanitized.timestamp && !Number.isInteger(sanitized.timestamp)) {
      sanitized.timestamp = Date.now();
    }
    return sanitized;
  }
}

/**
 * Therapeutic Component Manager
 */
class TherapeuticComponentManager {
  constructor(logger, config) {
    this.logger = logger;
    this.config = config;
    this.components = new Map();
    this.initializationOrder = [
      'metricsValidator',
      'multisensoryCollector',
      'cognitiveAnalyzer',
      'therapeuticAnalyzer',
      'behavioralAnalyzer',
      'sessionAnalyzer',
      'progressAnalyzer'
    ];
  }

  async initializeComponent(componentName, ComponentClass, args = []) {
    try {
      this.logger.info(`Initializing component: ${componentName}`);
      const component = new ComponentClass(...args);
      if (typeof component.initialize === 'function') {
        await component.initialize();
      }
      this.components.set(componentName, component);
      this.logger.info(`✅ ${componentName} initialized successfully`);
      return component;
    } catch (error) {
      this.logger.warn(`⚠️ Failed to initialize ${componentName}:`, { error: error.message });
      this.components.set(componentName, null);
      return null;
    }
  }

  getComponent(componentName) {
    return this.components.get(componentName) || null;
  }

  isComponentAvailable(componentName) {
    const component = this.components.get(componentName);
    return component !== null && component !== undefined;
  }

  async stopAllComponents() {
    for (const [name, component] of this.components) {
      try {
        if (component && typeof component.stop === 'function') {
          await component.stop();
          this.logger.info(`Component ${name} stopped`);
        }
      } catch (error) {
        this.logger.warn(`Error stopping component ${name}:`, { error: error.message });
      }
    }
    this.components.clear();
  }

  getComponentsStatus() {
    const status = {};
    for (const [name, component] of this.components) {
      status[name] = {
        available: component !== null && component !== undefined,
        initialized: component !== null
      };
    }
    return status;
  }
}

/**
 * Performance Manager with buffering and throttling
 */
class PerformanceManager {
  constructor() {
    this.buffer = new Map();
    this.throttleMap = new Map();
    this.maxBufferSize = THERAPEUTIC_CONFIG.maxSessionData;
    this.throttleDelay = 100;
    this.processingCache = new Map();
  }

  addToBuffer(key, data) {
    if (!this.buffer.has(key)) {
      this.buffer.set(key, []);
    }
    const bufferArray = this.buffer.get(key);
    bufferArray.push(data);
    if (bufferArray.length > this.maxBufferSize) {
      bufferArray.shift();
    }
  }

  throttle(key, callback) {
    if (this.throttleMap.has(key)) {
      clearTimeout(this.throttleMap.get(key));
    }
    const timeoutId = setTimeout(() => {
      callback();
      this.throttleMap.delete(key);
    }, this.throttleDelay);
    this.throttleMap.set(key, timeoutId);
  }

  getMemoryUsage() {
    if (isBrowser && performance.memory) {
      return {
        used: performance.memory.usedJSHeapSize / 1024 / 1024,
        total: performance.memory.totalJSHeapSize / 1024 / 1024
      };
    } else if (isNode && process.memoryUsage) {
      const mem = process.memoryUsage();
      return {
        used: mem.heapUsed / 1024 / 1024,
        total: mem.heapTotal / 1024 / 1024
      };
    }
    return { used: 0, total: 0 };
  }

  cacheProcessingResult(key, result) {
    this.processingCache.set(key, {
      result,
      timestamp: Date.now()
    });
    if (this.processingCache.size > this.maxBufferSize) {
      const oldestKey = Array.from(this.processingCache.keys())[0];
      this.processingCache.delete(oldestKey);
    }
  }

  getCachedResult(key) {
    const cached = this.processingCache.get(key);
    if (cached && Date.now() - cached.timestamp < THERAPEUTIC_CONFIG.retentionPeriod) {
      return cached.result;
    }
    this.processingCache.delete(key);
    return null;
  }

  cleanup() {
    this.buffer.clear();
    this.throttleMap.forEach(timeoutId => clearTimeout(timeoutId));
    this.throttleMap.clear();
    this.processingCache.clear();
  }
}

/**
 * Session Manager for session handling and locking
 */
class SessionManager {
  constructor(logger) {
    this.logger = logger;
    this.sessions = new Map();
    this.locks = new Map();
  }

  startSession(sessionId, userId, gameId) {
    if (this.locks.has(sessionId)) {
      throw new Error(`Session ${sessionId} is locked`);
    }
    this.locks.set(sessionId, true);
    this.sessions.set(sessionId, {
      sessionId,
      userId,
      gameId,
      startTime: Date.now(),
      lastActivity: Date.now(),
      status: 'active',
      metricsCount: 0
    });
    this.logger.info(`Session started: ${sessionId}`);
  }

  endSession(sessionId) {
    if (this.sessions.has(sessionId)) {
      this.sessions.delete(sessionId);
      this.locks.delete(sessionId);
      this.logger.info(`Session ended: ${sessionId}`);
    }
  }

  updateSession(sessionId, data) {
    if (this.sessions.has(sessionId)) {
      const session = this.sessions.get(sessionId);
      this.sessions.set(sessionId, { ...session, ...data, lastActivity: Date.now() });
    }
  }

  getSession(sessionId) {
    return this.sessions.get(sessionId) || null;
  }

  isSessionLocked(sessionId) {
    return this.locks.has(sessionId);
  }
}

/**
 * Metrics Processor for isolated metrics processing
 */
class MetricsProcessor {
  constructor(logger, cache) {
    this.logger = logger;
    this.cache = cache;
  }

  async processMetrics(metrics, processor) {
    const cacheKey = `metrics_${crypto.createHash('md5').update(JSON.stringify(metrics)).digest('hex')}`;
    const cachedResult = this.cache.get(cacheKey);
    if (cachedResult) {
      this.logger.debug('Cache hit for metrics processing', { cacheKey });
      return cachedResult;
    }
    const result = await processor.processMetrics(metrics);
    this.cache.set(cacheKey, result, 300000);
    return result;
  }
}

/**
 * Sensory Analyzer for audio, video, and touch data analysis
 */
class SensoryAnalyzer {
  constructor(logger) {
    this.logger = logger;
  }

  analyzeAudio(audioData) {
    try {
      if (!audioData || typeof audioData !== 'object') {
        return { volume: {}, pitch: {}, tone: {} };
      }
      const volume = this.analyzeVolume(audioData.volume || []);
      const pitch = this.analyzePitch(audioData.pitch || []);
      const tone = this.analyzeTone(audioData.tone || []);
      this.logger.info('Audio analysis completed', { volume, pitch, tone });
      return { volume, pitch, tone };
    } catch (error) {
      this.logger.error('❌ Error analyzing audio data:', { error: error.message });
      return { volume: {}, pitch: {}, tone: {} };
    }
  }

  analyzeVideo(videoData) {
    try {
      if (!videoData || typeof videoData !== 'object') {
        return { resolution: {}, frameRate: {}, color: {} };
      }
      const resolution = this.analyzeResolution(videoData.resolution || {});
      const frameRate = this.analyzeFrameRate(videoData.frameRate || []);
      const color = this.analyzeColor(videoData.color || {});
      this.logger.info('Video analysis completed', { resolution, frameRate, color });
      return { resolution, frameRate, color };
    } catch (error) {
      this.logger.error('❌ Error analyzing video data:', { error: error.message });
      return { resolution: {}, frameRate: {}, color: {} };
    }
  }

  analyzeTouch(touchData) {
    try {
      if (!touchData || typeof touchData !== 'object') {
        return { pressure: {}, duration: {}, location: {} };
      }
      const pressure = this.analyzePressure(touchData.pressure || []);
      const duration = this.analyzeDuration(touchData.duration || []);
      const location = this.analyzeLocation(touchData.location || {});
      this.logger.info('Touch analysis completed', { pressure, duration, location });
      return { pressure, duration, location };
    } catch (error) {
      this.logger.error('❌ Error analyzing touch data:', { error: error.message });
      return { pressure: {}, duration: {}, location: {} };
    }
  }

  analyzeVolume(volumeData) {
    try {
      if (!Array.isArray(volumeData) || volumeData.length === 0) {
        return { level: 0, variability: 0, backgroundNoise: 0 };
      }
      const level = this.analyzeLevel(volumeData);
      const variability = this.analyzeVariability(volumeData);
      const backgroundNoise = this.analyzeBackgroundNoise(volumeData);
      this.logger.info('Volume analysis completed', { level, variability, backgroundNoise });
      return { level, variability, backgroundNoise };
    } catch (error) {
      this.logger.error('❌ Error analyzing volume:', { error: error.message });
      return { level: 0, variability: 0, backgroundNoise: 0 };
    }
  }

  analyzePitch(pitchData) {
    try {
      if (!Array.isArray(pitchData) || pitchData.length === 0) {
        return { frequency: 0, variability: 0, timbre: 0 };
      }
      const frequency = this.analyzeFrequency(pitchData);
      const variability = this.analyzeVariability(pitchData);
      const timbre = this.analyzeTimbre(pitchData);
      this.logger.info('Pitch analysis completed', { frequency, variability, timbre });
      return { frequency, variability, timbre };
    } catch (error) {
      this.logger.error('❌ Error analyzing pitch:', { error: error.message });
      return { frequency: 0, variability: 0, timbre: 0 };
    }
  }

  analyzeTone(toneData) {
    try {
      if (!Array.isArray(toneData) || toneData.length === 0) {
        return { warmth: 0, brightness: 0, depth: 0 };
      }
      const warmth = this.analyzeWarmth(toneData);
      const brightness = this.analyzeBrightness(toneData);
      const depth = this.analyzeDepth(toneData);
      this.logger.info('Tone analysis completed', { warmth, brightness, depth });
      return { warmth, brightness, depth };
    } catch (error) {
      this.logger.error('❌ Error analyzing tone:', { error: error.message });
      return { warmth: 0, brightness: 0, depth: 0 };
    }
  }

  analyzeResolution(resolutionData) {
    try {
      if (!resolutionData || typeof resolutionData !== 'object') {
        return { width: 0, height: 0, aspectRatio: 0 };
      }
      const width = this.analyzeWidth(resolutionData.width || []);
      const height = this.analyzeHeight(resolutionData.height || []);
      const aspectRatio = this.analyzeAspectRatio(resolutionData);
      this.logger.info('Resolution analysis completed', { width, height, aspectRatio });
      return { width, height, aspectRatio };
    } catch (error) {
      this.logger.error('❌ Error analyzing resolution:', { error: error.message });
      return { width: 0, height: 0, aspectRatio: 0 };
    }
  }

  analyzeFrameRate(frameRateData) {
    try {
      if (!Array.isArray(frameRateData) || frameRateData.length === 0) {
        return { framesPerSecond: 0, variability: 0, droppedFrames: 0 };
      }
      const framesPerSecond = this.analyzeFramesPerSecond(frameRateData);
      const variability = this.analyzeVariability(frameRateData);
      const droppedFrames = this.analyzeDroppedFrames(frameRateData);
      this.logger.info('Frame rate analysis completed', { framesPerSecond, variability, droppedFrames });
      return { framesPerSecond, variability, droppedFrames };
    } catch (error) {
      this.logger.error('❌ Error analyzing frame rate:', { error: error.message });
      return { framesPerSecond: 0, variability: 0, droppedFrames: 0 };
    }
  }

  analyzeColor(colorData) {
    try {
      if (!colorData || typeof colorData !== 'object') {
        return { saturation: 0, brightness: 0, hue: 0 };
      }
      const saturation = this.analyzeSaturation(colorData.saturation || []);
      const brightness = this.analyzeBrightness(colorData.brightness || []);
      const hue = this.analyzeHue(colorData.hue || []);
      this.logger.info('Color analysis completed', { saturation, brightness, hue });
      return { saturation, brightness, hue };
    } catch (error) {
      this.logger.error('❌ Error analyzing color:', { error: error.message });
      return { saturation: 0, brightness: 0, hue: 0 };
    }
  }

  analyzePressure(pressureData) {
    try {
      if (!Array.isArray(pressureData) || pressureData.length === 0) {
        return { level: 0, variability: 0 };
      }
      const level = this.analyzeLevel(pressureData);
      const variability = this.analyzeVariability(pressureData);
      this.logger.info('Pressure analysis completed', { level, variability });
      return { level, variability };
    } catch (error) {
      this.logger.error('❌ Error analyzing pressure:', { error: error.message });
      return { level: 0, variability: 0 };
    }
  }

  analyzeDuration(durationData) {
    try {
      if (!Array.isArray(durationData) || durationData.length === 0) {
        return { length: 0, variability: 0 };
      }
      const length = this.analyzeLevel(durationData);
      const variability = this.analyzeVariability(durationData);
      this.logger.info('Duration analysis completed', { length, variability });
      return { length, variability };
    } catch (error) {
      this.logger.error('❌ Error analyzing duration:', { error: error.message });
      return { length: 0, variability: 0 };
    }
  }

  analyzeLocation(locationData) {
    try {
      if (!locationData || typeof locationData !== 'object') {
        return { x: 0, y: 0, z: 0 };
      }
      const x = this.analyzeX(locationData.x || []);
      const y = this.analyzeY(locationData.y || []);
      const z = this.analyzeZ(locationData.z || []);
      this.logger.info('Location analysis completed', { x, y, z });
      return { x, y, z };
    } catch (error) {
      this.logger.error('❌ Error analyzing location:', { error: error.message });
      return { x: 0, y: 0, z: 0 };
    }
  }

  analyzeLevel(data) {
    try {
      if (!Array.isArray(data) || data.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      const min = Math.min(...data);
      const max = Math.max(...data);
      const average = data.reduce((sum, val) => sum + val, 0) / data.length;
      return { min, max, average };
    } catch (error) {
      this.logger.error('❌ Error analyzing level:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeVariability(data) {
    try {
      if (!Array.isArray(data) || data.length === 0) {
        return { standardDeviation: 0, range: 0 };
      }
      const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
      const squaredDifferences = data.map(val => (val - mean) ** 2);
      const variance = squaredDifferences.reduce((sum, val) => sum + val, 0) / data.length;
      const standardDeviation = Math.sqrt(variance);
      const range = Math.max(...data) - Math.min(...data);
      return { standardDeviation, range };
    } catch (error) {
      this.logger.error('❌ Error analyzing variability:', { error: error.message });
      return { standardDeviation: 0, range: 0 };
    }
  }

  analyzeFrequency(frequencyData) {
    try {
      if (!Array.isArray(frequencyData) || frequencyData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(frequencyData);
    } catch (error) {
      this.logger.error('❌ Error analyzing frequency:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeTimbre(timbreData) {
    try {
      if (!Array.isArray(timbreData) || timbreData.length === 0) {
        return { richness: 0, brightness: 0, warmth: 0 };
      }
      const richness = this.calculateRichness(timbreData);
      const brightness = this.analyzeBrightness(timbreData);
      const warmth = this.analyzeWarmth(timbreData);
      return { richness, brightness, warmth };
    } catch (error) {
      this.logger.error('❌ Error analyzing timbre:', { error: error.message });
      return { richness: 0, brightness: 0, warmth: 0 };
    }
  }

  analyzeWarmth(warmthData) {
    try {
      if (!Array.isArray(warmthData) || warmthData.length === 0) {
        return { low: 0, high: 0, average: 0 };
      }
      return this.analyzeLevel(warmthData);
    } catch (error) {
      this.logger.error('❌ Error analyzing warmth:', { error: error.message });
      return { low: 0, high: 0, average: 0 };
    }
  }

  analyzeBrightness(brightnessData) {
    try {
      if (!Array.isArray(brightnessData) || brightnessData.length === 0) {
        return { low: 0, high: 0, average: 0 };
      }
      return this.analyzeLevel(brightnessData);
    } catch (error) {
      this.logger.error('❌ Error analyzing brightness:', { error: error.message });
      return { low: 0, high: 0, average: 0 };
    }
  }

  analyzeDepth(depthData) {
    try {
      if (!Array.isArray(depthData) || depthData.length === 0) {
        return { front: 0, back: 0, average: 0 };
      }
      return this.analyzeLevel(depthData);
    } catch (error) {
      this.logger.error('❌ Error analyzing depth:', { error: error.message });
      return { front: 0, back: 0, average: 0 };
    }
  }

  analyzeWidth(widthData) {
    try {
      if (!Array.isArray(widthData) || widthData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(widthData);
    } catch (error) {
      this.logger.error('❌ Error analyzing width:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeHeight(heightData) {
    try {
      if (!Array.isArray(heightData) || heightData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(heightData);
    } catch (error) {
      this.logger.error('❌ Error analyzing height:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeAspectRatio(aspectRatioData) {
    try {
      if (!aspectRatioData || typeof aspectRatioData !== 'object') {
        return { width: 0, height: 0, ratio: 0 };
      }
      const width = this.analyzeWidth(aspectRatioData.width || []);
      const height = this.analyzeHeight(aspectRatioData.height || []);
      const ratio = width.average && height.average ? width.average / height.average : 0;
      return { width, height, ratio };
    } catch (error) {
      this.logger.error('❌ Error analyzing aspect ratio:', { error: error.message });
      return { width: 0, height: 0, ratio: 0 };
    }
  }

  analyzeFramesPerSecond(fpsData) {
    try {
      if (!Array.isArray(fpsData) || fpsData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(fpsData);
    } catch (error) {
      this.logger.error('❌ Error analyzing frames per second:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeDroppedFrames(droppedFramesData) {
    try {
      if (!Array.isArray(droppedFramesData) || droppedFramesData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(droppedFramesData);
    } catch (error) {
      this.logger.error('❌ Error analyzing dropped frames:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeSaturation(saturationData) {
    try {
      if (!Array.isArray(saturationData) || saturationData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(saturationData);
    } catch (error) {
      this.logger.error('❌ Error analyzing saturation:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeHue(hueData) {
    try {
      if (!Array.isArray(hueData) || hueData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(hueData);
    } catch (error) {
      this.logger.error('❌ Error analyzing hue:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  calculateRichness(timbreData) {
    try {
      if (!Array.isArray(timbreData) || timbreData.length === 0) {
        return { low: 0, high: 0, average: 0 };
      }
      return this.analyzeLevel(timbreData);
    } catch (error) {
      this.logger.error('❌ Error calculating richness:', { error: error.message });
      return { low: 0, high: 0, average: 0 };
    }
  }

  calculateStandardDeviation(data) {
    try {
      if (!Array.isArray(data) || data.length === 0) {
        return 0;
      }
      const mean = data.reduce((sum, val) => sum + val, 0) / data.length;
      const squaredDifferences = data.map(val => (val - mean) ** 2);
      const variance = squaredDifferences.reduce((sum, val) => sum + val, 0) / data.length;
      return Math.sqrt(variance);
    } catch (error) {
      this.logger.error('❌ Error calculating standard deviation:', { error: error.message });
      return 0;
    }
  }

  analyzeX(xData) {
    try {
      if (!Array.isArray(xData) || xData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(xData);
    } catch (error) {
      this.logger.error('❌ Error analyzing x coordinate:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeY(yData) {
    try {
      if (!Array.isArray(yData) || yData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(yData);
    } catch (error) {
      this.logger.error('❌ Error analyzing y coordinate:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeZ(zData) {
    try {
      if (!Array.isArray(zData) || zData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(zData);
    } catch (error) {
      this.logger.error('❌ Error analyzing z coordinate:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }

  analyzeBackgroundNoise(backgroundNoiseData) {
    try {
      if (!Array.isArray(backgroundNoiseData) || backgroundNoiseData.length === 0) {
        return { min: 0, max: 0, average: 0 };
      }
      return this.analyzeLevel(backgroundNoiseData);
    } catch (error) {
      this.logger.error('❌ Error analyzing background noise:', { error: error.message });
      return { min: 0, max: 0, average: 0 };
    }
  }
}

/**
 * Mock classes for dependencies
 */
class MultisensoryMetricsCollector {
  constructor() {
    this.updateTherapeuticSettings = (optimizations) => {
      console.log('MultisensoryMetricsCollector: Therapeutic settings updated', optimizations);
    };
  }
}

class PredictiveAnalysisEngine {
  constructor() {
    this.analyze = async (data) => ({ predictions: [], confidence: 0.7 });
  }
}

class AdvancedMetricsEngine {
  constructor() {
    this.processMetrics = async (metrics) => ({ processed: true, ...metrics });
  }
}

class GameSessionManager {
  constructor() {
    this.sessions = new Map();
  }
  async initialize() {
    console.log('GameSessionManager initialized');
  }
}

class MetricsAggregator {
  constructor() {
    this.aggregatedData = new Map();
  }
  async initialize() {
    console.log('MetricsAggregator initialized');
  }
}

class RecommendationEngine {
  constructor() {
    this.recommendations = [];
  }
  async initialize() {
    console.log('RecommendationEngine initialized');
  }
}

class TherapeuticOptimizer {
  constructor() {
    this.optimizations = {};
  }
  async initialize() {
    console.log('TherapeuticOptimizer initialized');
  }
}

class MetricsValidator {
  constructor() {
    this.orchestrator = null;
    this.validationCount = 0;
    this.errorCount = 0;
    this.lastValidationTime = null;
  }
  setOrchestrator(orchestrator) {
    this.orchestrator = orchestrator;
  }
  validateMetrics(metrics) {
    this.validationCount++;
    this.lastValidationTime = Date.now();
    return { valid: true, metrics };
  }
  getMetrics() {
    return {
      totalValidations: this.validationCount,
      validationErrors: this.errorCount,
      lastValidationTime: this.lastValidationTime
    };
  }
}

/**
 * SystemOrchestrator - Main orchestrator for Portal Betina V3
 */
export class SystemOrchestrator {
  static instance = null;

  /**
   * @returns {Promise<SystemOrchestrator>} Singleton instance
   */
  static async getInstance(databaseService, config = {}, gameSpecificProcessors = null) {
    if (!SystemOrchestrator.instance) {
      if (!databaseService) {
        databaseService = {
          save: async (data) => ({ id: Date.now(), ...data }),
          find: async (query) => [],
          findOne: async (query) => null,
          update: async (id, data) => ({ id, ...data }),
          delete: async (id) => true,
          isConnected: () => true,
          saveCompleteSession: async (userId, gameId, sessionData) => ({ id: Date.now(), ...sessionData }),
          saveMetrics: async (userId, gameId, metrics, analysis) => ({ id: Date.now(), ...metrics }),
          saveSessionReport: async (report) => ({ id: Date.now(), ...report })
        };
      }
      SystemOrchestrator.instance = new SystemOrchestrator(databaseService, config, gameSpecificProcessors);
      await SystemOrchestrator.instance.initialize();
    }
    return SystemOrchestrator.instance;
  }

  static validateGameInput(gameInput) { return InputValidator.validateGameInput(gameInput); }
  static validateMetricsData(metricsData) { return InputValidator.validateMetricsData(metricsData); }
  static validateEventData(eventData) { return InputValidator.validateEventData(eventData); }
  static sanitizeGameInput(gameInput) { return InputValidator.sanitizeGameInput(gameInput); }
  static sanitizeMetricsData(metricsData) { return InputValidator.sanitizeMetricsData(metricsData); }
  static sanitizeEventData(eventData) { return InputValidator.sanitizeEventData(eventData); }

  /**
   * @constructor
   */
  constructor(databaseService, config = {}, gameSpecificProcessors) {
    this.logger = StructuredLogger.getInstance({
      serviceName: 'SystemOrchestrator',
      logLevel: config.logLevel || 'info'
    });
    this.cache = new IntelligentCache({
      maxSize: config.cacheSize || 1000,
      defaultTTL: config.cacheTTL || 300000,
      strategy: 'LRU'
    });
    this.healthCheck = getHealthCheckService({
      checkInterval: config.healthCheckInterval || 30000,
      cpuThreshold: 80,
      memoryThreshold: 85
    });
    this.performanceManager = new PerformanceManager();
    this.sessionManager = new SessionManager(this.logger);
    this.metricsProcessor = new MetricsProcessor(this.logger, this.cache);
    this.sensoryAnalyzer = new SensoryAnalyzer(this.logger);
    this.componentManager = new TherapeuticComponentManager(this.logger, config);
    this.db = databaseService;
    this.config = {
      enableMetricsService: true,
      enableGameSpecificProcessors: true,
      enableMultisensoryIntegration: true,
      enableAdvancedMetricsEngine: true,
      enablePredictiveAnalysis: true,
      enableMetricsValidator: true,
      orchestrationInterval: THERAPEUTIC_CONFIG.orchestrationInterval || 30000,
      retentionPeriod: THERAPEUTIC_CONFIG.retentionPeriod,
      maxSessionData: THERAPEUTIC_CONFIG.maxSessionData,
      ...config,
    };
    this.state = SYSTEM_STATES.INITIALIZING;
    this.mode = OPERATION_MODES.PRODUCTION;
    this.sessionData = new Map();
    this.therapeuticMetrics = new Map();
    this.intervals = new Map();
    this.lastEngagementAlert = 0;
    this.lastProgressAlert = 0;
    this.therapeuticSystems = {
      gameSpecificProcessors: gameSpecificProcessors || new GameSpecificProcessors(databaseService),
      metricsValidator: null,
      multisensoryCollector: null,
      advancedMetricsEngine: null,
      predictiveAnalysisEngine: null,
      cognitiveAnalyzer: null,
      therapeuticAnalyzer: null,
      behavioralAnalyzer: null,
      sessionAnalyzer: null,
      progressAnalyzer: null,
    };
    this.gameSessionManager = new GameSessionManager();
    this.metricsAggregator = new MetricsAggregator();
    this.recommendationEngine = new RecommendationEngine();
    this.therapeuticOptimizer = new TherapeuticOptimizer();
    this.aiBrain = new AIBrainOrchestrator({
      databaseService: this.db,
      systemOrchestrator: this,
      simulationMode: process.env.NODE_ENV !== 'production'
    });
    this.analyzersInitialized = false;
    this.statistics = {
      uptime: 0,
      totalSessions: 0,
      totalGamesPlayed: 0,
      totalMetricsProcessed: 0,
      averageSessionDuration: 0,
      averageEngagementRate: 0,
      therapeuticSuccessRate: 0,
      avgMetricsPerSession: THERAPEUTIC_CONFIG.avgMetricsPerSession || 50,
      avgCollectorsPerSession: THERAPEUTIC_CONFIG.avgCollectorsPerSession || 5,
      startTime: Date.now(),
      lastOptimization: null,
    };

    this.registerHealthCheckComponents();
    this.logger.info('SystemOrchestrator initialized', {
      type: 'system_init',
      config: this.config,
      mode: this.mode,
      state: this.state,
      timestamp: new Date().toISOString()
    });

    // 🚀 Auto-inicialização para transição automática para RUNNING
    setTimeout(() => {
      this.autoInitialize();
    }, 100); // Dar tempo para completar a construção

    // 🔒 Sistema de Controle de Idempotência - Prevenção de Requisições Duplicadas
    this.idempotencyManager = {
      processingKeys: new Map(), // Cache para requisições em processamento
      completedKeys: new Map(),  // Cache para requisições completas
      lockKeys: new Set(),       // Controle de concorrência
      duplicateAttempts: new Map(), // Contador de tentativas duplicadas
      
      // TTL para limpeza automática (5 minutos)
      cleanupInterval: setInterval(() => {
        const now = Date.now();
        const maxAge = 300000; // 5 minutos
        
        // Limpar processingKeys antigos
        for (const [key, timestamp] of this.idempotencyManager.processingKeys) {
          if (now - timestamp > maxAge) {
            this.idempotencyManager.processingKeys.delete(key);
          }
        }
        
        // Limpar completedKeys antigos
        for (const [key, timestamp] of this.idempotencyManager.completedKeys) {
          if (now - timestamp > maxAge) {
            this.idempotencyManager.completedKeys.delete(key);
          }
        }
        
        // Limpar duplicateAttempts antigos
        for (const [key, data] of this.idempotencyManager.duplicateAttempts) {
          if (now - data.lastAttempt > maxAge) {
            this.idempotencyManager.duplicateAttempts.delete(key);
          }
        }
      }, 60000) // Executar limpeza a cada minuto
    };
  }

  /**
   * Initialize the orchestrator
   * @returns {Promise<SystemOrchestrator>}
   */
  async initialize() {
    try {
      this.logger.info('Initializing SystemOrchestrator...');
      this.state = SYSTEM_STATES.INITIALIZING;
      await this.initializeCoreTools();
      await this.initializeEssentialComponents();
      await this.initializeAnalyzers();
      await this.setupDataFlow();
      this.startPerformanceMonitoring();
      this.startTherapeuticMonitoring();
      this.state = SYSTEM_STATES.READY;
      setTimeout(() => {
        this.state = SYSTEM_STATES.RUNNING;
        this.logger.info('🚀 System transitioned to RUNNING', { state: this.state });
      }, 500);
      this.analyzersInitialized = true;
      this.logger.info('SystemOrchestrator initialized successfully', {
        componentsActive: Object.values(this.therapeuticSystems).filter(c => c !== null).length,
        sessionsTracked: this.sessionData.size,
        metricsStored: this.therapeuticMetrics.size
      });
      return this;
    } catch (error) {
      this.state = SYSTEM_STATES.ERROR;
      this.logger.error('Error initializing system', { error: error.message });
      throw error;
    }
  }

  async initializeCoreTools() {
    try {
      this.logger.info('Initializing core tools...');
      await Promise.all([
        this.gameSessionManager.initialize(),
        this.metricsAggregator.initialize(),
        this.recommendationEngine.initialize(),
        this.therapeuticOptimizer.initialize()
      ]);
      this.logger.info('Core tools initialized successfully');
    } catch (error) {
      this.logger.error('Error initializing core tools:', { error: error.message });
      throw error;
    }
  }

  async initializeEssentialComponents() {
    const componentConfig = [
      { name: 'gameSpecificProcessors', cls: GameSpecificProcessors, args: [this.db] },
      { name: 'metricsValidator', cls: MetricsValidator, args: [] },
      { name: 'multisensoryCollector', cls: MultisensoryMetricsCollector, args: [] },
      { name: 'advancedMetricsEngine', cls: AdvancedMetricsEngine, args: [], condition: this.config.enableAdvancedMetricsEngine },
      { name: 'predictiveAnalysisEngine', cls: PredictiveAnalysisEngine, args: [], condition: this.config.enablePredictiveAnalysis },
      { name: 'cognitiveAnalyzer', cls: getCognitiveAnalyzer, args: [{ cache: this.cache, logger: this.logger }], isAsync: true },
      { name: 'therapeuticAnalyzer', cls: getTherapeuticAnalyzer, args: [{ cache: this.cache, logger: this.logger }], isAsync: true },
      { name: 'behavioralAnalyzer', cls: getBehavioralAnalyzer, args: [{ cache: this.cache, logger: this.logger }], isAsync: false },
      { name: 'progressAnalyzer', cls: getProgressAnalyzer, args: [{ cache: this.cache, logger: this.logger }], isAsync: true }
    ];

    const initResults = [];
    for (const { name, cls, args, condition = true, isAsync = false } of componentConfig) {
      if (!condition) {
        initResults.push(`${name}: SKIPPED (disabled by config)`);
        continue;
      }
      const component = isAsync ? await cls(...args) : await this.componentManager.initializeComponent(name, cls, args);
      this.therapeuticSystems[name] = component;
      initResults.push(`${name}: ${component ? 'OK' : 'FAILED'}`);
      if (name === 'metricsValidator' && component && component.setOrchestrator) {
        component.setOrchestrator(this);
      }
    }

    this.logger.info('Essential components initialized', {
      results: initResults,
      successful: initResults.filter(r => r.includes('OK')).length
    });
  }

  async initializeAnalyzers() {
    try {
      this.logger.info('🧠 Initializing specialized analyzers...');

      // Inicializar analisadores
      this.therapeuticSystems.behavioralAnalyzer = getBehavioralAnalyzer({ cache: this.cache, logger: this.logger });
      this.therapeuticSystems.cognitiveAnalyzer = await getCognitiveAnalyzer({ cache: this.cache, logger: this.logger });
      this.therapeuticSystems.therapeuticAnalyzer = await getTherapeuticAnalyzer({ cache: this.cache, logger: this.logger });
      this.therapeuticSystems.progressAnalyzer = await getProgressAnalyzer({ cache: this.cache, logger: this.logger });
      const { SessionAnalyzer } = await import('../analysis/SessionAnalyzer.js');
      this.therapeuticSystems.sessionAnalyzer = new SessionAnalyzer();

      // ✅ CORRIGIDO: Injetar SystemOrchestrator nos analisadores para evitar dependência circular
      const analyzersToInject = [
        'behavioralAnalyzer',
        'cognitiveAnalyzer',
        'therapeuticAnalyzer',
        'progressAnalyzer',
        'sessionAnalyzer'
      ];

      for (const analyzerName of analyzersToInject) {
        const analyzer = this.therapeuticSystems[analyzerName];
        if (analyzer && typeof analyzer.setSystemOrchestrator === 'function') {
          analyzer.setSystemOrchestrator(this);
          this.logger.info(`✅ SystemOrchestrator injetado em ${analyzerName}`);
        }
      }

      this.logger.info('✅ Analyzers initialized and connected successfully');
    } catch (error) {
      this.logger.error('❌ Error initializing analyzers:', { error: error.message });
    }
  }

  async setupDataFlow() {
    this.logger.info('Setting up data flow...');
    this.setupDataRetention();
    this.logger.info('Data flow configured');
  }

  setupDataRetention() {
    const retentionInterval = setInterval(() => {
      const now = Date.now();
      for (const [sessionId, sessionInfo] of this.sessionData.entries()) {
        if (now - sessionInfo.timestamp > this.config.retentionPeriod) {
          this.sessionData.delete(sessionId);
        }
      }
      if (this.sessionData.size > this.config.maxSessionData) {
        const entries = Array.from(this.sessionData.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        const toRemove = entries.slice(0, entries.length - this.config.maxSessionData);
        toRemove.forEach(([sessionId]) => this.sessionData.delete(sessionId));
      }
      for (const [key, data] of this.therapeuticMetrics.entries()) {
        if (now - data.timestamp > this.config.retentionPeriod) {
          this.therapeuticMetrics.delete(key);
        }
      }
    }, 60000);
    this.intervals.set('dataRetention', retentionInterval);
  }

  startPerformanceMonitoring() {
    const monitoringInterval = setInterval(() => {
      this.updateStatistics();
      this.performTherapeuticOptimization();
    }, this.config.orchestrationInterval);
    this.intervals.set('monitoring', monitoringInterval);
    this.logger.info('Performance monitoring started');
  }

  startTherapeuticMonitoring() {
    try {
      const therapeuticInterval = setInterval(() => {
        this.collectTherapeuticSystemMetrics();
        this.analyzeTherapeuticProgress();
        this.checkTherapeuticGoals();
      }, THERAPEUTIC_SYSTEM_CONFIG.monitoring.interval);
      this.intervals.set('therapeutic-monitoring', therapeuticInterval);
      this.setupTherapeuticAlerts();
      this.logger.info('Therapeutic monitoring started');
    } catch (error) {
      this.logger.error('Error starting therapeutic monitoring:', { error: error.message });
    }
  }

  setupTherapeuticAlerts() {
    try {
      this.therapeuticAlerts = {
        engagementThreshold: THERAPEUTIC_SYSTEM_CONFIG.optimization.therapeuticTargets.userEngagement,
        completionThreshold: THERAPEUTIC_SYSTEM_CONFIG.optimization.therapeuticTargets.sessionCompletion,
        goalAchievementThreshold: THERAPEUTIC_SYSTEM_CONFIG.optimization.therapeuticTargets.therapeuticGoalAchievement,
        responseTimeThreshold: THERAPEUTIC_SYSTEM_CONFIG.therapeuticThresholds.responseTime
      };
      this.logger.info('Therapeutic alerts configured', this.therapeuticAlerts);
    } catch (error) {
      this.logger.error('Error setting up therapeutic alerts:', { error: error.message });
    }
  }

  /**
   * Process game input with idempotency
   * @param {Object} data - Game input data
   * @returns {Promise<Object>}
   */
  async processGameInput(data) {
    const validation = InputValidator.validateGameInput(data);
    if (!validation.valid) {
      throw new Error(`Invalid game input: ${validation.errors.join(', ')}`);
    }
    
    // Verificar idempotência
    const idempotencyKey = this.generateIdempotencyKey('processGameInput', data);
    
    if (this.isDuplicateRequest(idempotencyKey)) {
      const cachedResult = this.getCachedRequestResult(idempotencyKey);
      if (cachedResult) {
        return cachedResult;
      }
    }
    if (!this.therapeuticSystems.gameSpecificProcessors.isGameSupported(data.gameId)) {
      throw new Error(`Game ${data.gameId} not supported`);
    }
    this.sessionManager.startSession(data.sessionId, data.userId, data.gameId);
    try {
      const processed = await this.therapeuticSystems.gameSpecificProcessors.processGameData(data.gameId, data);
      
      // Registrar requisição processada
      this.registerProcessedRequest(idempotencyKey, 'processGameInput', processed);
      
      this.sessionManager.updateSession(data.sessionId, {
        metricsCount: this.countGeneratedMetrics(processed),
        status: 'processed'
      });
      this.updateGameMetricsBaseline(data.gameId, processed);
      this.logger.info('Game input processed', { gameId: data.gameId, sessionId: data.sessionId });
      return processed;
    } catch (error) {
      this.logger.error('Error processing game input:', { error: error.message });
      throw error;
    } finally {
      this.sessionManager.endSession(data.sessionId);
    }
  }

  /**
   * Process game metrics with idempotency
   * @param {string} childId
   * @param {string} gameName
   * @param {Object} metrics
   * @returns {Promise<Object>}
   */
  async processGameMetrics(childId, gameName, metrics) {
    const validation = InputValidator.validateMetricsData({ userId: childId, sessionId: metrics.sessionId, gameId: gameName, therapeuticMetrics: metrics });
    if (!validation.valid) {
      throw new Error(`Invalid metrics data: ${validation.errors.join(', ')}`);
    }
    
    // Verificar idempotência
    const idempotencyKey = this.generateIdempotencyKey('processGameMetrics', metrics);
    
    if (this.isDuplicateRequest(idempotencyKey)) {
      const cachedResult = this.getCachedRequestResult(idempotencyKey);
      if (cachedResult) {
        return cachedResult;
      }
    }
    this.sessionManager.startSession(metrics.sessionId, childId, gameName);
    try {
      let processedData = null;
      if (this.therapeuticSystems.gameSpecificProcessors) {
        processedData = await this.therapeuticSystems.gameSpecificProcessors.processGameData(gameName, {
          ...metrics,
          userId: childId,
          sessionId: metrics.sessionId || `ai_brain_${Date.now()}`
        });
      }
      const therapeuticMetrics = await this.collectTherapeuticMetrics({ ...metrics, childId, gameId: gameName });
      const therapeuticAnalysis = await this.processTherapeuticData(therapeuticMetrics);
      const specializedAnalyses = await this.runSpecializedAnalyses({
        childId,
        gameName,
        metrics,
        therapeuticMetrics,
        therapeuticAnalysis,
        processedData
      });
      const result = {
        success: true,
        childId,
        gameName,
        sessionId: metrics.sessionId,
        timestamp: new Date().toISOString(),
        processedData,
        therapeuticMetrics,
        therapeuticAnalysis,
        specificAnalysis: processedData?.specificAnalysis || null,
        gameSpecificMetrics: processedData?.metrics || null,
        specializedAnalyses
      };
      if (this.db) {
        await this.db.saveCompleteSession(childId, gameName, {
          userId: childId,
          gameId: gameName,
          sessionId: metrics.sessionId,
          score: metrics.score || processedData?.metrics?.score || 0,
          level: metrics.level || 1,
          duration: metrics.timeSpent || metrics.duration || 0,
          accuracy: therapeuticMetrics.accuracy || 0,
          engagement: therapeuticMetrics.engagement || 0,
          completed: true,
          timestamp: new Date().toISOString(),
          rawData: metrics,
          processedMetrics: therapeuticMetrics,
          therapeuticAnalysis
        });
        await this.db.saveMetrics(childId, gameName, therapeuticMetrics, therapeuticAnalysis);
      }

      // ✅ CORRIGIDO: Definir cacheKey antes de usar
      const cacheKey = `game_metrics_${childId}_${gameName}_${metrics.sessionId}`;
      this.cache.set(cacheKey, result, 300000);
      
      // Registrar requisição processada
      this.registerProcessedRequest(idempotencyKey, 'processGameMetrics', result);
      
      this.sessionManager.updateSession(metrics.sessionId, {
        metricsCount: this.countGeneratedMetrics(result),
        status: 'processed'
      });
      this.logger.info('Game metrics processed', { childId, gameName });
      return result;
    } catch (error) {
      this.logger.error('Error processing game metrics:', { error: error.message });
      return { success: false, error: error.message, childId, gameName };
    } finally {
      this.sessionManager.endSession(metrics.sessionId);
    }
  }

  /**
   * Process therapeutic session with idempotency
   * @param {Object} sessionData
   * @returns {Promise<Object>}
   */
  async processTherapeuticSession(sessionData) {
    const validation = InputValidator.validateEventData(sessionData);
    if (!validation.valid) {
      throw new Error(`Invalid session data: ${validation.errors.join(', ')}`);
    }
    
    // Verificar idempotência
    const idempotencyKey = this.generateIdempotencyKey('processTherapeuticSession', sessionData);
    
    if (this.isDuplicateRequest(idempotencyKey)) {
      const cachedResult = this.getCachedRequestResult(idempotencyKey);
      if (cachedResult) {
        return cachedResult;
      }
    }
    this.sessionManager.startSession(sessionData.sessionId, sessionData.childId, sessionData.gameType || 'unknown');
    try {
      const gameResults = [];
      for (const gameInput of sessionData.games || []) {
        const gameResult = await this.processGameInput(gameInput);
        gameResults.push(gameResult);
      }
      const metrics = await this.collectTherapeuticMetrics(sessionData);
      const therapeuticData = await this.processTherapeuticData(metrics);
      const report = {
        sessionId: sessionData.sessionId,
        userId: sessionData.childId,
        timestamp: Date.now(),
        duration: sessionData.sessionDuration,
        gameResults,
        metrics,
        therapeuticData,
        adaptationNeeds: this.identifyAdaptiveNeeds(sessionData),
        recommendations: await this.recommendationEngine.generateRecommendations({
          metrics,
          patterns: therapeuticData.patterns,
          insights: therapeuticData.insights
        })
      };
      await this.db.saveSessionReport(report);
      this.sessionManager.updateSession(sessionData.sessionId, {
        metricsCount: this.countGeneratedMetrics(therapeuticData),
        engagement: metrics.engagement,
        accuracy: metrics.accuracy,
        score: gameResults.reduce((sum, result) => sum + (result.specificAnalysis?.baseMetrics?.score || 0), 0) / (gameResults.length || 1),
        progress: therapeuticData.patterns?.progress || 0,
        status: 'completed',
        duration: sessionData.sessionDuration
      });
      this.cache.set(cacheKey, report, 300000);
      this.statistics.totalSessions += 1;
      this.statistics.totalGamesPlayed += gameResults.length;
      this.statistics.totalMetricsProcessed += this.countGeneratedMetrics(therapeuticData);
      this.logger.info('Therapeutic session processed', { sessionId: sessionData.sessionId });
      return report;
    } catch (error) {
      this.logger.error('Error processing therapeutic session:', { error: error.message });
      this.sessionManager.updateSession(sessionData.sessionId, { status: 'error', lastError: error.message });
      throw error;
    } finally {
      this.sessionManager.endSession(sessionData.sessionId);
    }
  }

  /**
   * Run specialized analyses with concurrency limiting
   * @param {Object} analysisData
   * @returns {Promise<Object>}
   */
  async runSpecializedAnalyses(analysisData) {
    const limitConcurrency = async (tasks, maxConcurrent = 2) => {
      const results = [];
      for (let i = 0; i < tasks.length; i += maxConcurrent) {
        const chunk = tasks.slice(i, i + maxConcurrent);
        results.push(...await Promise.allSettled(chunk.map(task => task())));
      }
      return results;
    };

    try {
      const { childId, gameName, metrics, therapeuticMetrics, therapeuticAnalysis, processedData } = analysisData;
      const commonData = {
        childId,
        gameType: gameName,
        sessionId: metrics.sessionId || `session_${Date.now()}`,
        timestamp: new Date().toISOString(),
        rawMetrics: metrics,
        therapeuticMetrics,
        therapeuticAnalysis,
        processedData
      };

      const tasks = [
        () => this.therapeuticSystems.behavioralAnalyzer?.analyzeGameBehavior({
          ...commonData,
          behaviorData: {
            responseTime: metrics.responseTime,
            accuracy: metrics.accuracy,
            interactionPatterns: metrics.interactionPatterns || [],
            attentionMetrics: therapeuticMetrics.attention || {}
          }
        }),
        () => this.therapeuticSystems.cognitiveAnalyzer?.analyzeCognitiveSession({
          ...commonData,
          cognitiveData: {
            problemSolving: metrics.problemSolving || {},
            memory: metrics.memory || {},
            attention: metrics.attention || {},
            executiveFunction: metrics.executiveFunction || {}
          }
        }),
        () => this.therapeuticSystems.progressAnalyzer?.analyzeProgress({
          ...commonData,
          progressData: {
            currentScore: metrics.score,
            previousScores: metrics.previousScores || [],
            milestones: metrics.milestones || [],
            improvements: therapeuticAnalysis.improvements || []
          }
        }),
        () => this.therapeuticSystems.sessionAnalyzer?.analyzeSession({
          ...commonData,
          sessionData: {
            duration: metrics.sessionDuration,
            engagement: metrics.engagement || {},
            interactions: metrics.interactions || [],
            completionRate: metrics.completionRate || 0
          }
        }),
        () => this.therapeuticSystems.therapeuticAnalyzer?.analyzeTherapeuticEffectiveness(commonData.childId, 30)
      ];

      const results = await limitConcurrency(tasks);
      const analyses = {
        behavioral: this.processAnalysisResult(results[0], 'behavioral'),
        cognitive: this.processAnalysisResult(results[1], 'cognitive'),
        progress: this.processAnalysisResult(results[2], 'progress'),
        session: this.processAnalysisResult(results[3], 'session'),
        therapeutic: this.processAnalysisResult(results[4], 'therapeutic')
      };
      const combinedInsights = this.generateCombinedInsights(analyses);
      return {
        success: true,
        analyses,
        combinedInsights,
        metadata: {
          analysisTimestamp: new Date().toISOString(),
          analyzersUsed: Object.keys(analyses),
          dataQuality: this.assessDataQuality(commonData)
        }
      };
    } catch (error) {
      this.logger.error('Error in specialized analyses:', { error: error.message });
      return { success: false, error: error.message, analyses: {}, combinedInsights: { insights: [], recommendations: [] } };
    }
  }

  /**
   * Export metrics for Prometheus
   * @returns {string} Prometheus metrics format
   */
  exportMetricsForPrometheus() {
    const metrics = this.getMetrics();
    const lines = [];
    lines.push(`# HELP system_uptime System uptime in seconds\n# TYPE system_uptime gauge\nsystem_uptime ${metrics.system.uptime || 0}`);
    lines.push(`# HELP total_sessions Total number of sessions\n# TYPE total_sessions counter\ntotal_sessions ${metrics.sessions.totalSessions || 0}`);
    lines.push(`# HELP total_games_played Total games played\n# TYPE total_games_played counter\ntotal_games_played ${metrics.sessions.totalGamesPlayed || 0}`);
    lines.push(`# HELP total_metrics_processed Total metrics processed\n# TYPE total_metrics_processed counter\ntotal_metrics_processed ${metrics.sessions.totalMetricsProcessed || 0}`);
    lines.push(`# HELP avg_session_duration Average session duration\n# TYPE avg_session_duration gauge\navg_session_duration ${metrics.sessions.averageSessionDuration || 0}`);
    lines.push(`# HELP cache_hit_rate Cache hit rate\n# TYPE cache_hit_rate gauge\ncache_hit_rate ${(metrics.cache?.hitRate || 0).toFixed(3)}`);
    return lines.join('\n');
  }

  // Métodos restantes do SystemOrchestrator (mantidos do original com otimizações)
  async saveSessionToDatabase(sessionData) {
    try {
      this.logger.info('Saving session to database...', { sessionId: sessionData.sessionId });
      if (!this.db) {
        return { success: false, error: 'DatabaseService not available' };
      }
      const sessionRecord = {
        user_id: sessionData.childId,
        game_id: sessionData.gameType,
        session_id: sessionData.sessionId,
        score: sessionData.score || 0,
        level: sessionData.level || 1,
        duration: sessionData.timeSpent || 0,
        accuracy: sessionData.therapeuticMetrics?.accuracy || 0,
        completed: true,
        session_data: JSON.stringify(sessionData.rawMetrics || {}),
        created_at: new Date().toISOString()
      };
      let sessionSaved = false;
      if (typeof this.db.query === 'function') {
        const insertSessionQuery = `
          INSERT INTO game_sessions (user_id, game_id, session_id, score, level, duration, accuracy, completed, session_data, created_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
          RETURNING id;
        `;
        const sessionResult = await this.db.query(insertSessionQuery, [
          sessionRecord.user_id,
          sessionRecord.game_id,
          sessionRecord.session_id,
          sessionRecord.score,
          sessionRecord.level,
          sessionRecord.duration,
          sessionRecord.accuracy,
          sessionRecord.completed,
          sessionRecord.session_data,
          sessionRecord.created_at
        ]);
        sessionSaved = true;
        this.logger.info('Session saved to game_sessions', { sessionId: sessionData.sessionId });
      }
      let cognitiveMetricsSaved = false;
      if (sessionData.therapeuticMetrics) {
        const cognitiveMetrics = [
          { name: 'accuracy', value: sessionData.therapeuticMetrics.accuracy || 0 },
          { name: 'response_time', value: sessionData.therapeuticMetrics.responseTime || 1000 },
          { name: 'cognitive_load', value: sessionData.therapeuticMetrics.cognitiveLoad || 50 }
        ];
        for (const metric of cognitiveMetrics) {
          const insertMetricQuery = `
            INSERT INTO metrics_cognitive (session_id, user_id, game_id, metric_name, value, created_at)
            VALUES ($1, $2, $3, $4, $5, $6);
          `;
          await this.db.query(insertMetricQuery, [
            sessionData.sessionId,
            sessionData.childId,
            sessionData.gameType,
            metric.name,
            metric.value,
            new Date().toISOString()
          ]);
        }
        cognitiveMetricsSaved = true;
      }
      let behavioralMetricsSaved = false;
      if (sessionData.therapeuticMetrics) {
        const behavioralMetrics = [
          { name: 'engagement', value: sessionData.therapeuticMetrics.engagement || 50 },
          { name: 'completion_rate', value: sessionData.correctAnswers && sessionData.wrongAnswers ? 
            (sessionData.correctAnswers / (sessionData.correctAnswers + sessionData.wrongAnswers)) * 100 : 100 }
        ];
        for (const metric of behavioralMetrics) {
          const insertBehavioralQuery = `
            INSERT INTO metrics_behavioral (session_id, user_id, game_id, metric_name, value, created_at)
            VALUES ($1, $2, $3, $4, $5, $6);
          `;
          await this.db.query(insertBehavioralQuery, [
            sessionData.sessionId,
            sessionData.childId,
            sessionData.gameType,
            metric.name,
            metric.value,
            new Date().toISOString()
          ]);
        }
        behavioralMetricsSaved = true;
      }
      return {
        success: sessionSaved || cognitiveMetricsSaved || behavioralMetricsSaved,
        sessionSaved,
        cognitiveMetricsSaved,
        behavioralMetricsSaved,
        sessionId: sessionData.sessionId
      };
    } catch (error) {
      this.logger.error('Error saving session to database:', { error: error.message });
      return { success: false, error: error.message, sessionId: sessionData.sessionId };
    }
  }

  async collectTherapeuticMetrics(gameData) {
    try {
      const metrics = {
        timestamp: Date.now(),
        engagement: this.calculateEngagement(gameData),
        accuracy: this.calculateAccuracy(gameData),
        responseTime: this.calculateResponseTime(gameData),
        cognitiveLoad: this.assessCognitiveLoad(gameData),
        sensoryData: this.extractSensoryData(gameData, gameData.gameId)
      };
      this.therapeuticMetrics.set(`metrics_${gameData.sessionId}_${Date.now()}`, metrics);
      return metrics;
    } catch (error) {
      this.logger.error('Error collecting therapeutic metrics:', { error: error.message });
      return {};
    }
  }

  async processTherapeuticData(metrics) {
    try {
      const patterns = await this.analyzeTherapeuticPatterns(metrics);
      const insights = await this.generateTherapeuticInsights(metrics, patterns);
      const chartData = await this.generateChartData(metrics);
      const alerts = await this.generateAlerts(metrics);
      return { success: true, patterns, insights, chartData, alerts, timestamp: new Date().toISOString() };
    } catch (error) {
      this.logger.error('Error processing therapeutic data:', { error: error.message });
      return { success: false, error: error.message };
    }
  }

  async analyzeTherapeuticPatterns(metrics) {
    try {
      const patterns = {
        engagementTrend: 0,
        performanceTrend: 0,
        cognitiveLoadTrend: 0,
        sensoryPatterns: {},
        progress: 0
      };
      if (this.therapeuticSystems.behavioralAnalyzer) {
        const behavioralPatterns = await this.therapeuticSystems.behavioralAnalyzer.analyze(metrics);
        patterns.engagementTrend = behavioralPatterns.engagementTrend || 0;
        patterns.performanceTrend = behavioralPatterns.performanceTrend || 0;
      }
      if (this.therapeuticSystems.cognitiveAnalyzer) {
        const cognitiveAnalysis = await this.therapeuticSystems.cognitiveAnalyzer.analyze(metrics);
        patterns.cognitiveLoadTrend = cognitiveAnalysis.cognitiveLoadTrend || 0;
      }
      if (this.therapeuticSystems.multisensoryCollector) {
        patterns.sensoryPatterns = this.therapeuticSystems.multisensoryCollector.analyzeSensoryPatterns(metrics.sensoryData);
      }
      patterns.progress = this.calculateImprovementTrend(Array.from(this.sessionData.values()));
      return patterns;
    } catch (error) {
      this.logger.error('Error analyzing therapeutic patterns:', { error: error.message });
      return { engagementTrend: 0, performanceTrend: 0, cognitiveLoadTrend: 0, sensoryPatterns: {}, progress: 0 };
    }
  }

  async generateTherapeuticInsights(metrics, patterns) {
    try {
      const insights = {
        cognitive: {},
        behavioral: {},
        therapeutic: {},
        recommendations: []
      };
      if (this.therapeuticSystems.cognitiveAnalyzer) {
        insights.cognitive = await this.therapeuticSystems.cognitiveAnalyzer.generateInsights(metrics);
      }
      if (this.therapeuticSystems.behavioralAnalyzer) {
        insights.behavioral = await this.therapeuticSystems.behavioralAnalyzer.generateInsights(metrics, patterns);
      }
      if (this.therapeuticSystems.therapeuticAnalyzer) {
        insights.therapeutic = await this.therapeuticSystems.therapeuticAnalyzer.generateInsights(metrics, patterns);
      }
      if (patterns.progress > 0.1) {
        insights.recommendations.push('Continue current therapeutic approach due to positive progress');
      } else if (patterns.progress < -0.1) {
        insights.recommendations.push('Consider adjusting therapeutic approach due to negative progress trend');
      }
      if (metrics.engagement < THERAPEUTIC_SYSTEM_CONFIG.optimization.therapeuticTargets.userEngagement) {
        insights.recommendations.push('Implement engagement strategies to improve user interaction');
      }
      if (metrics.cognitiveLoad > 80) {
        insights.recommendations.push('Reduce cognitive load by simplifying tasks or increasing breaks');
      }
      return insights;
    } catch (error) {
      this.logger.error('Error generating therapeutic insights:', { error: error.message });
      return { cognitive: {}, behavioral: {}, therapeutic: {}, recommendations: [] };
    }
  }

  async generateChartData(metrics) {
    try {
      const chartData = {
        engagement: { labels: [], datasets: [{ data: [], label: 'Engagement Rate' }] },
        accuracy: { labels: [], datasets: [{ data: [], label: 'Accuracy' }] },
        responseTime: { labels: [], datasets: [{ data: [], label: 'Response Time (ms)' }] },
        cognitiveLoad: { labels: [], datasets: [{ data: [], label: 'Cognitive Load' }] }
      };
      const sessions = Array.from(this.sessionData.values())
        .filter(s => Date.now() - s.timestamp < THERAPEUTIC_SYSTEM_CONFIG.monitoring.retentionPeriod)
        .sort((a, b) => a.timestamp - b.timestamp);

      sessions.forEach(session => {
        const timestamp = new Date(session.timestamp).toISOString().split('T')[0];
        chartData.engagement.labels.push(timestamp);
        chartData.accuracy.labels.push(timestamp);
        chartData.responseTime.labels.push(timestamp);
        chartData.cognitiveLoad.labels.push(timestamp);

        chartData.engagement.datasets[0].data.push(session.engagement || 0);
        chartData.accuracy.datasets[0].data.push(session.accuracy || 0);
        chartData.responseTime.datasets[0].data.push(session.responseTime || 0);
        chartData.cognitiveLoad.datasets[0].data.push(session.cognitiveLoad || 0);
      });

      this.logger.info('Chart data generated', {
        engagementPoints: chartData.engagement.datasets[0].data.length,
        accuracyPoints: chartData.accuracy.datasets[0].data.length,
        responseTimePoints: chartData.responseTime.datasets[0].data.length,
        cognitiveLoadPoints: chartData.cognitiveLoad.datasets[0].data.length
      });

      return chartData;
    } catch (error) {
      this.logger.error('Error generating chart data:', { error: error.message });
      return {
        engagement: { labels: [], datasets: [{ data: [], label: 'Engagement Rate' }] },
        accuracy: { labels: [], datasets: [{ data: [], label: 'Accuracy' }] },
        responseTime: { labels: [], datasets: [{ data: [], label: 'Response Time (ms)' }] },
        cognitiveLoad: { labels: [], datasets: [{ data: [], label: 'Cognitive Load' }] }
      };
    }
  }

  /**
   * Generate alerts based on metrics
   * @param {Object} metrics - Metrics data
   * @returns {Promise<Array>}
   */
  async generateAlerts(metrics) {
    try {
      const alerts = [];
      if (metrics.engagement < this.therapeuticAlerts.engagementThreshold) {
        alerts.push({
          type: 'engagement',
          level: 'warning',
          message: `Low engagement detected: ${metrics.engagement.toFixed(2)}%`,
          timestamp: new Date().toISOString()
        });
      }
      if (metrics.responseTime > this.therapeuticAlerts.responseTimeThreshold) {
        alerts.push({
          type: 'responseTime',
          level: 'warning',
        });
      }
 if (metrics.responseTime > this.therapeuticAlerts.responseTimeThreshold) {
        alerts.push({
          type: 'responseTime',
          level: 'warning',
          message: `High response time detected: ${metrics.responseTime}ms`,
          timestamp: new Date().toISOString()
        });
      }
      if (metrics.cognitiveLoad > 80) {
        alerts.push({
          type: 'cognitiveLoad',
          level: 'warning',
          message: `High cognitive load detected: ${metrics.cognitiveLoad}`,
          timestamp: new Date().toISOString()
        });
      }
      if (alerts.length > 0) {
        this.logger.info('Alerts generated', { count: alerts.length });
      }
      return alerts;
    } catch (error) {
      this.logger.error('Error generating alerts:', { error: error.message });
      return [];
    }
  }

  /**
   * Process analysis result
   * @param {Object} analysisResult - Result from analyzer
   * @param {string} analysisType - Type of analysis
   * @returns {Object}
   */
  processAnalysisResult(analysisResult, analysisType) {
    try {
      if (analysisResult.status === 'fulfilled' && analysisResult.value) {
        return {
          type: analysisType,
          success: true,
          data: analysisResult.value,
          timestamp: new Date().toISOString()
        };
      } else {
        this.logger.warn(`Analysis failed for ${analysisType}`, {
          reason: analysisResult.reason?.message || 'Unknown error'
        });
        return {
          type: analysisType,
          success: false,
          error: analysisResult.reason?.message || 'Analysis failed',
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      this.logger.error(`Error processing ${analysisType} analysis result:`, { error: error.message });
      return { type: analysisType, success: false, error: error.message };
    }
  }

  /**
   * Generate combined insights from analysis results
   * @param {Object} results - Analysis results
   * @returns {Object}
   */
  generateCombinedInsights(results) {
    try {
      const insights = [];
      const recommendations = [];

      if (results.behavioral?.success) {
        insights.push(...(results.behavioral.data?.insights || []));
      }
      if (results.cognitive?.success) {
        insights.push(...(results.cognitive.data?.insights || []));
      }
      if (results.progress?.success) {
        insights.push(...(results.progress.data?.insights || []));
      }
      if (results.therapeutic?.success) {
        insights.push(...(results.therapeutic.data?.insights || []));
      }

      if (insights.length < THERAPEUTIC_CONFIG.minDataPointsForInsight) {
        recommendations.push('Insufficient data for comprehensive insights');
      } else {
        recommendations.push(...this.generateCorrelationInsights(results));
      }

      return { insights, recommendations };
    } catch (error) {
      this.logger.error('Error generating combined insights:', { error: error.message });
      return { insights: [], recommendations: [] };
    }
  }

  /**
   * Generate correlation insights
   * @param {Object} results - Analysis results
   * @returns {Array}
   */
  generateCorrelationInsights(results) {
    try {
      const recommendations = [];
      if (results.behavioral?.success && results.cognitive?.success) {
        const correlation = this.calculateCorrelation(
          results.behavioral.data?.engagement || [],
          results.cognitive.data?.cognitiveLoad || []
        );
        if (correlation > 0.7) {
          recommendations.push('Strong correlation between engagement and cognitive load detected');
        } else if (correlation < -0.7) {
          recommendations.push('Negative correlation between engagement and cognitive load detected');
        }
      }
      return recommendations;
    } catch (error) {
      this.logger.error('Error generating correlation insights:', { error: error.message });
      return [];
    }
  }

  /**
   * Calculate correlation between two datasets
   * @param {Array<number>} data1
   * @param {Array<number>} data2
   * @returns {number}
   */
  calculateCorrelation(data1, data2) {
    try {
      if (!data1.length || !data2.length || data1.length !== data2.length) return 0;
      const n = data1.length;
      const mean1 = data1.reduce((sum, val) => sum + val, 0) / n;
      const mean2 = data2.reduce((sum, val) => sum + val, 0) / n;
      let cov = 0, std1 = 0, std2 = 0;
      for (let i = 0; i < n; i++) {
        cov += (data1[i] - mean1) * (data2[i] - mean2);
        std1 += (data1[i] - mean1) ** 2;
        std2 += (data2[i] - mean2) ** 2;
      }
      std1 = Math.sqrt(std1 / n);
      std2 = Math.sqrt(std2 / n);
      return cov / (std1 * std2 * n) || 0;
    } catch (error) {
      this.logger.error('Error calculating correlation:', { error: error.message });
      return 0;
    }
  }

  /**
   * Assess data quality
   * @param {Object} data
   * @returns {Object}
   */
  assessDataQuality(data) {
    try {
      const quality = {
        completeness: 0,
        consistency: 0,
        validity: 0
      };
      const requiredFields = ['userId', 'sessionId', 'gameId', 'timestamp'];
      const totalFields = requiredFields.length + (data.metrics ? Object.keys(data.metrics).length : 0);
      const presentFields = requiredFields.filter(field => data[field] !== undefined).length +
        (data.metrics ? Object.values(data.metrics).filter(v => v !== undefined).length : 0);
      quality.completeness = (presentFields / totalFields) || 0;
      quality.consistency = data.timestamp && (Date.now() - data.timestamp < 3600000) ? 1 : 0;
      quality.validity = InputValidator.validateMetricsData(data).valid ? 1 : 0;
      return quality;
    } catch (error) {
      this.logger.error('Error assessing data quality:', { error: error.message });
      return { completeness: 0, consistency: 0, validity: 0 };
    }
  }

  /**
   * Deduplicate array
   * @param {Array} array
   * @returns {Array}
   */
  deduplicateArray(array) {
    try {
      return [...new Set(array.map(item => JSON.stringify(item)))].map(item => JSON.parse(item));
    } catch (error) {
      this.logger.error('Error deduplicating array:', { error: error.message });
      return array;
    }
  }

  /**
   * Perform therapeutic optimization
   */
  async performTherapeuticOptimization() {
    try {
      if (!this.config.optimization?.autoOptimize) return;
      const optimizations = {
        difficulty: this.calculateOptimalDifficulty(this.calculateCurrentEngagementLevels()),
        sensory: this.calculateOptimalSensorySettings(this.getCurrentProgressMetrics()),
        duration: this.calculateOptimalSessionDuration(Array.from(this.sessionData.values()))
      };
      await this.therapeuticOptimizer.applyOptimizations(optimizations);
      this.statistics.lastOptimization = new Date().toISOString();
      this.logger.info('Therapeutic optimization performed', { optimizations });
    } catch (error) {
      this.logger.error('Error performing therapeutic optimization:', { error: error.message });
    }
  }

  /**
   * Calculate optimal difficulty
   * @param {Object} engagementLevels
   * @returns {number}
   */
  calculateOptimalDifficulty(engagementLevels) {
    try {
      const engagement = engagementLevels.average || 0;
      return Math.min(10, Math.max(1, Math.round(engagement / 10)));
    } catch (error) {
      this.logger.error('Error calculating optimal difficulty:', { error: error.message });
      return 1;
    }
  }

  /**
   * Calculate optimal sensory settings
   * @param {Object} progressMetrics
   * @returns {Object}
   */
  calculateOptimalSensorySettings(progressMetrics) {
    try {
      return {
        visual: Math.min(100, Math.max(10, progressMetrics.visual || 50)),
        audio: Math.min(100, Math.max(10, progressMetrics.audio || 50)),
        tactile: Math.min(100, Math.max(10, progressMetrics.tactile || 50))
      };
    } catch (error) {
      this.logger.error('Error calculating sensory settings:', { error: error.message });
      return { visual: 50, audio: 50, tactile: 50 };
    }
  }

  /**
   * Calculate optimal session duration
   * @param {Array} sessions
   * @returns {number}
   */
  calculateOptimalSessionDuration(sessions) {
    try {
      const avgDuration = sessions.reduce((sum, session) => sum + (session.duration || 0), 0) / (sessions.length || 1);
      return Math.min(THERAPEUTIC_SYSTEM_CONFIG.therapeuticThresholds.sessionDuration, Math.max(300000, avgDuration));
    } catch (error) {
      this.logger.error('Error calculating optimal session duration:', { error: error.message });
      return 300000;
    }
  }

  /**
   * Generate game recommendations
   * @param {Array} activeSessions
   * @returns {Array}
   */
  async generateGameRecommendations(activeSessions) {
    try {
      const recommendations = await this.recommendationEngine.generateRecommendations({
        sessions: activeSessions,
        metrics: this.getCurrentProgressMetrics(),
        userPreferences: this.getUserSensoryPreferences()
      });
      return this.deduplicateArray(recommendations);
    } catch (error) {
      this.logger.error('Error generating game recommendations:', { error: error.message });
      return [];
    }
  }

  /**
   * Identify adaptive needs
   * @param {Object} rawData
   * @returns {Object}
   */
  identifyAdaptiveNeeds(rawData) {
    try {
      const needs = {
        sensory: {},
        cognitive: {},
        behavioral: {}
      };
      if (rawData.engagement < this.therapeuticAlerts.engagementThreshold) {
        needs.behavioral.engagement = 'increase';
      }
      if (rawData.cognitiveLoad > 80) {
        needs.cognitive.load = 'reduce';
      }
      if (rawData.sensoryData) {
        needs.sensory = this.sensoryAnalyzer.analyzeSensoryData(rawData.sensoryData);
      }
      return needs;
    } catch (error) {
      this.logger.error('Error identifying adaptive needs:', { error: error.message });
      return { sensory: {}, cognitive: {}, behavioral: {} };
    }
  }

  /**
   * Extract sensory data
   * @param {Object} rawData
   * @param {string} gameId
   * @returns {Object}
   */
  extractSensoryData(rawData, gameId) {
    try {
      const sensoryData = {
        audio: this.sensoryAnalyzer.analyzeAudio(rawData.audioData || {}),
        video: this.sensoryAnalyzer.analyzeVideo(rawData.videoData || {}),
        touch: this.sensoryAnalyzer.analyzeTouch(rawData.touchData || {})
      };
      this.logger.info('Sensory data extracted', { gameId, sensoryData });
      return sensoryData;
    } catch (error) {
      this.logger.error('Error extracting sensory data:', { error: error.message });
      return { audio: {}, video: {}, touch: {} };
    }
  }

  /**
   * Calculate engagement
   * @param {Object} gameData
   * @returns {number}
   */
  calculateEngagement(gameData) {
    try {
      const interactions = gameData.interactions || 0;
      const duration = gameData.duration || 1;
      return Math.min(100, (interactions / duration) * 1000);
    } catch (error) {
      this.logger.error('Error calculating engagement:', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate accuracy
   * @param {Object} gameData
   * @returns {number}
   */
  calculateAccuracy(gameData) {
    try {
      const correct = gameData.correctAnswers || 0;
      const total = (gameData.correctAnswers || 0) + (gameData.wrongAnswers || 0);
      return total > 0 ? (correct / total) * 100 : 0;
    } catch (error) {
      this.logger.error('Error calculating accuracy:', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate response time
   * @param {Object} gameData
   * @returns {number}
   */
  calculateResponseTime(gameData) {
    try {
      const responseTimes = gameData.responseTimes || [];
      return responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;
    } catch (error) {
      this.logger.error('Error calculating response time:', { error: error.message });
      return 0;
    }
  }

  /**
   * Assess cognitive load
   * @param {Object} gameData
   * @returns {number}
   */
  assessCognitiveLoad(gameData) {
    try {
      const complexity = gameData.taskComplexity || 1;
      const responseTime = this.calculateResponseTime(gameData);
      return Math.min(100, (responseTime / 1000) * complexity);
    } catch (error) {
      this.logger.error('Error assessing cognitive load:', { error: error.message });
      return 0;
    }
  }

  /**
   * Calculate improvement trend
   * @param {Array} sessions
   * @returns {number}
   */
  calculateImprovementTrend(sessions) {
    try {
      const scores = sessions
        .filter(s => s.score !== undefined)
        .map(s => s.score)
        .sort((a, b) => a.timestamp - b.timestamp);
      if (scores.length < 2) return 0;
      const differences = scores.slice(1).map((score, i) => score - scores[i]);
      return differences.reduce((sum, diff) => sum + diff, 0) / differences.length;
    } catch (error) {
      this.logger.error('Error calculating improvement trend:', { error: error.message });
      return 0;
    }
  }

  /**
   * Get user sensory preferences
   * @returns {Object}
   */
  getUserSensoryPreferences() {
    try {
      return {
        visual: this.config.autism?.sensoryOptimization ? 50 : 0,
        audio: this.config.autism?.sensoryOptimization ? 50 : 0,
        tactile: this.config.autism?.sensoryOptimization ? 50 : 0
      };
    } catch (error) {
      this.logger.error('Error getting user sensory preferences:', { error: error.message });
      return { visual: 0, audio: 0, tactile: 0 };
    }
  }

  /**
   * Get adaptation metrics
   * @returns {Object}
   */
  getAdaptationMetrics() {
    try {
      return {
        effectiveness: this.calculateAdaptationEffectiveness(Array.from(this.sessionData.values())),
        recommendations: this.generateAdaptationRecommendations(this.getCurrentProgressMetrics())
      };
    } catch (error) {
      this.logger.error('Error getting adaptation metrics:', { error: error.message });
      return { effectiveness: 0, recommendations: [] };
    }
  }

  /**
   * Calculate adaptation effectiveness
   * @param {Array} adaptationHistory
   * @returns {number}
   */
  calculateAdaptationEffectiveness(adaptationHistory) {
    try {
      const successfulAdaptations = adaptationHistory.filter(a => a.progress > 0).length;
      return (successfulAdaptations / (adaptationHistory.length || 1)) * 100;
    } catch (error) {
      this.logger.error('Error calculating adaptation effectiveness:', { error: error.message });
      return 0;
    }
  }

  /**
   * Generate adaptation recommendations
   * @param {Object} multisensoryData
   * @returns {Array}
   */
  generateAdaptationRecommendations(multisensoryData) {
    try {
      const recommendations = [];
      if (multisensoryData.visual < 30) {
        recommendations.push('Increase visual stimulation');
      }
      if (multisensoryData.audio < 30) {
        recommendations.push('Increase audio stimulation');
      }
      if (multisensoryData.tactile < 30) {
        recommendations.push('Increase tactile stimulation');
      }
      return recommendations;
    } catch (error) {
      this.logger.error('Error generating adaptation recommendations:', { error: error.message });
      return [];
    }
  }

  /**
   * Update statistics
   */
  updateStatistics() {
    try {
      this.statistics.uptime = (Date.now() - this.statistics.startTime) / 1000;
      this.statistics.averageSessionDuration = Array.from(this.sessionData.values())
        .reduce((sum, session) => sum + (session.duration || 0), 0) /
        (this.sessionData.size || 1);
      this.statistics.averageEngagementRate = Array.from(this.sessionData.values())
        .reduce((sum, session) => sum + (session.engagement || 0), 0) /
        (this.sessionData.size || 1);
      this.statistics.therapeuticSuccessRate = this.calculateImprovementTrend(
        Array.from(this.sessionData.values())
      );
      this.logger.info('Statistics updated', this.statistics);
    } catch (error) {
      this.logger.error('Error updating statistics:', { error: error.message });
    }
  }

  /**
   * Collect therapeutic system metrics
   */
  collectTherapeuticSystemMetrics() {
    try {
      const metrics = {
        memory: this.performanceManager.getMemoryUsage(),
        activeSessions: typeof this.sessionManager?.getActiveSessionsCount === 'function' ? 
          this.sessionManager.getActiveSessionsCount() : 
          (this.sessionManager?.activeSessions?.size || 0),
        componentStatus: this.componentManager.getComponentsStatus(),
        cache: {
          hitRate: typeof this.cache?.getHitRate === 'function' ? 
            this.cache.getHitRate() : 0,
          size: typeof this.cache?.size === 'function' ? 
            this.cache.size() : 
            (this.cache?.cache?.size || 0)
        }
      };
      this.logger.info('Therapeutic system metrics collected', metrics);
      return metrics;
    } catch (error) {
      this.logger.error('Error collecting therapeutic system metrics:', { error: error.message });
      return {};
    }
  }

  /**
   * Analyze therapeutic progress
   */
  analyzeTherapeuticProgress() {
    try {
      const progress = this.calculateImprovementTrend(Array.from(this.sessionData.values()));
      this.logger.info('Therapeutic progress analyzed', { progress });
      return progress;
    } catch (error) {
      this.logger.error('Error analyzing therapeutic progress:', { error: error.message });
      return 0;
    }
  }

  /**
   * Check therapeutic goals
   */
  checkTherapeuticGoals() {
    try {
      const activeSessionsCount = typeof this.sessionManager?.getActiveSessionsCount === 'function' ? 
        this.sessionManager.getActiveSessionsCount() : 
        (this.sessionManager?.activeSessions?.size || 0);
        
      const goals = {
        engagement: this.calculateCurrentEngagementLevels().average >=
          this.therapeuticAlerts.engagementThreshold,
        completion: activeSessionsCount /
          (this.statistics.totalSessions || 1) >=
          this.therapeuticAlerts.completionThreshold,
        achievement: this.analyzeTherapeuticProgress() >=
          this.therapeuticAlerts.goalAchievementThreshold
      };
      this.logger.info('Therapeutic goals checked', goals);
      return goals;
    } catch (error) {
      this.logger.error('Error checking therapeutic goals:', { error: error.message });
      return { engagement: false, completion: false, achievement: false };
    }
  }

  /**
   * Get current progress metrics
   * @returns {Object}
   */
  getCurrentProgressMetrics() {
    try {
      const sessions = Array.from(this.sessionData.values());
      return {
        visual: sessions.reduce((sum, s) => sum + (s.sensoryData?.visual || 0), 0) / (sessions.length || 1),
        audio: sessions.reduce((sum, s) => sum + (s.sensoryData?.audio || 0), 0) / (sessions.length || 1),
        tactile: sessions.reduce((sum, s) => sum + (s.sensoryData?.tactile || 0), 0) / (sessions.length || 1)
      };
    } catch (error) {
      this.logger.error('Error getting current progress metrics:', { error: error.message });
      return { visual: 0, audio: 0, tactile: 0 };
    }
  }

  /**
   * Calculate current engagement levels
   * @returns {Object}
   */
  calculateCurrentEngagementLevels() {
    try {
      const sessions = Array.from(this.sessionData.values());
      const engagement = sessions.reduce((sum, session) => sum + (session.engagement || 0), 0) /
        (sessions.length || 1);
      return { average: engagement, sessions: sessions.length };
    } catch (error) {
      this.logger.error('Error calculating engagement levels:', { error: error.message });
      return { average: 0, sessions: 0 };
    }
  }

  /**
   * Get active therapeutic sessions
   * @returns {Array}
   */
  getActiveTherapeuticSessions() {
    try {
      return Array.from(this.sessionData.values()).filter(
        session => session.status === 'active' &&
        Date.now() - session.lastActivity < this.config.retentionPeriod
      );
    } catch (error) {
      this.logger.error('Error getting active therapeutic sessions:', { error: error.message });
      return [];
    }
  }

  /**
   * Get health status
   * @returns {Object}
   */
  getHealthStatus() {
    try {
      const status = {
        system: this.getSystemHealth(),
        components: this.componentManager.getComponentsStatus(),
        database: this.db?.isConnected ? this.db.isConnected() : false,
        memory: this.performanceManager.getMemoryUsage(),
        cache: {
          hitRate: typeof this.cache?.getHitRate === 'function' ? 
            this.cache.getHitRate() : 0,
          size: typeof this.cache?.size === 'function' ? 
            this.cache.size() : 
            (this.cache?.cache?.size || 0)
        }
      };
      this.logger.info('Health status retrieved', status);
      return status;
    } catch (error) {
      this.logger.error('Error getting health status:', { error: error.message });
      return {};
    }
  }

  /**
   * Get system health
   * @returns {Object}
   */
  getSystemHealth() {
    try {
      return {
        state: this.state,
        uptime: this.getUptime(),
        componentsInitialized: this.componentManager.getComponentsStatus(),
        databaseConnected: this.db?.isConnected ? this.db.isConnected() : false
      };
    } catch (error) {
      this.logger.error('Error getting system health:', { error: error.message });
      return { state: SYSTEM_STATES.ERROR, uptime: 0 };
    }
  }

  /**
   * Perform health check
   * @returns {Promise<Object>}
   */
  async healthCheck() {
    try {
      const health = await this.healthCheck.check({
        components: this.componentManager.getComponentsStatus(),
        memory: this.performanceManager.getMemoryUsage(),
        database: this.db?.isConnected ? this.db.isConnected() : false,
        cache: {
          hitRate: typeof this.cache?.getHitRate === 'function' ? 
            this.cache.getHitRate() : 0,
          size: typeof this.cache?.size === 'function' ? 
            this.cache.size() : 
            (this.cache?.cache?.size || 0)
        },
        activeSessions: typeof this.sessionManager?.getActiveSessionsCount === 'function' ? 
          this.sessionManager.getActiveSessionsCount() : 
          (this.sessionManager?.activeSessions?.size || 0)
      });
      this.logger.info('Health check completed', health);
      return health;
    } catch (error) {
      this.logger.error('Error performing health check:', { error: error.message });
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Get metrics
   * @returns {Object}
   */
  getMetrics() {
    try {
      return {
        system: {
          uptime: this.getUptime(),
          state: this.state,
          mode: this.mode
        },
        sessions: this.statistics,
        cache: {
          hitRate: typeof this.cache?.getHitRate === 'function' ? 
            this.cache.getHitRate() : 0,
          size: typeof this.cache?.size === 'function' ? 
            this.cache.size() : 
            (this.cache?.cache?.size || 0)
        },
        components: this.componentManager.getComponentsStatus(),
        therapeuticMetrics: {
          total: this.therapeuticMetrics.size,
          engagement: this.calculateCurrentEngagementLevels().average,
          progress: this.analyzeTherapeuticProgress()
        }
      };
    } catch (error) {
      this.logger.error('Error getting metrics:', { error: error.message });
      return {};
    }
  }

  /**
   * Get uptime
   * @returns {number}
   */
  getUptime() {
    try {
      return (Date.now() - this.statistics.startTime) / 1000;
    } catch (error) {
      this.logger.error('Error getting uptime:', { error: error.message });
      return 0;
    }
  }

  /**
   * Process event
   * @param {string} eventType
   * @param {Object} eventData
   * @returns {Promise<Object>}
   */
  async processEvent(eventType, eventData) {
    try {
      const validation = InputValidator.validateEventData(eventData);
      if (!validation.valid) {
        throw new Error(`Invalid event data: ${validation.errors.join(', ')}`);
      }
      this.notifyEvent(eventType, eventData);
      return { success: true, eventType, eventData };
    } catch (error) {
      this.logger.error('Error processing event:', { error: error.message });
      return { success: false, error: error.message, eventType };
    }
  }

  /**
   * Notify event
   * @param {string} eventType
   * @param {Object} eventData
   */
  notifyEvent(eventType, eventData) {
    try {
      switch (eventType) {
        case 'METRICS_VALIDATION_STARTED':
          this.handleMetricsValidationStarted(eventData);
          break;
        case 'METRICS_VALIDATION_COMPLETED':
          this.handleMetricsValidationCompleted(eventData);
          break;
        case 'METRICS_VALIDATION_ERROR':
          this.handleMetricsValidationError(eventData);
          break;
        default:
          this.logger.info('Event notified', { eventType, eventData });
      }
    } catch (error) {
      this.logger.error('Error notifying event:', { error: error.message });
    }
  }

  /**
   * Handle metrics validation started
   * @param {Object} eventData
   */
  handleMetricsValidationStarted(eventData) {
    try {
      this.logger.info('Metrics validation started', eventData);
    } catch (error) {
      this.logger.error('Error handling metrics validation started:', { error: error.message });
    }
  }

  /**
   * Handle metrics validation completed
   * @param {Object} eventData
   */
  handleMetricsValidationCompleted(eventData) {
    try {
      this.logger.info('Metrics validation completed', eventData);
    } catch (error) {
      this.logger.error('Error handling metrics validation completed:', { error: error.message });
    }
  }

  /**
   * Handle metrics validation error
   * @param {Object} eventData
   */
  handleMetricsValidationError(eventData) {
    try {
      this.logger.error('Metrics validation error', eventData);
    } catch (error) {
      this.logger.error('Error handling metrics validation error:', { error: error.message });
    }
  }

  /**
   * Update session data
   * @param {Object} data
   */
  updateSessionData(data) {
    try {
      if (!data.sessionId) {
        throw new Error('Session ID is required');
      }
      this.sessionManager.updateSession(data.sessionId, data);
      this.logger.info('Session data updated', { sessionId: data.sessionId });
    } catch (error) {
      this.logger.error('Error updating session data:', { error: error.message });
    }
  }

  /**
   * Update session metrics
   * @param {string} sessionId
   * @param {number} metricsCount
   */
  updateSessionMetrics(sessionId, metricsCount) {
    try {
      this.sessionManager.updateSession(sessionId, { metricsCount });
      this.logger.info('Session metrics updated', { sessionId, metricsCount });
    } catch (error) {
      this.logger.error('Error updating session metrics:', { error: error.message });
    }
  }

  /**
   * Count generated metrics
   * @param {Object} processingResult
   * @returns {number}
   */
  countGeneratedMetrics(processingResult) {
    try {
      return Object.keys(processingResult?.metrics || {}).length +
        Object.keys(processingResult?.therapeuticMetrics || {}).length;
    } catch (error) {
      this.logger.error('Error counting generated metrics:', { error: error.message });
      return 0;
    }
  }

  /**
   * Update game metrics baseline
   * @param {string} gameId
   * @param {Object} processedData
   */
  updateGameMetricsBaseline(gameId, processedData) {
    try {
      const baseline = GAME_METRICS_BASELINE[gameId] || {};
      baseline.avgMetrics = ((baseline.avgMetrics || 0) + this.countGeneratedMetrics(processedData)) / 2;
      baseline.avgAccuracy = ((baseline.avgAccuracy || 0) + (processedData.accuracy || 0)) / 2;
      baseline.avgScore = ((baseline.avgScore || 0) + (processedData.score || 0)) / 2;
      baseline.avgResponseTime = ((baseline.avgResponseTime || 0) + (processedData.responseTime || 0)) / 2;
      GAME_METRICS_BASELINE[gameId] = baseline;
      this.logger.info('Game metrics baseline updated', { gameId, baseline });
    } catch (error) {
      this.logger.error('Error updating game metrics baseline:', { error: error.message });
    }
  }

  /**
   * Register health check components
   */
  registerHealthCheckComponents() {
    try {
      // Database health check
      this.healthCheck.registerComponent('database', () => {
        try {
          return this.db?.isConnected ? this.db.isConnected() : false;
        } catch (error) {
          this.logger.error('Health check failed for database', { type: 'health_check_error', component: 'database', error: error.message });
          return false;
        }
      });

      // Cache health check
      this.healthCheck.registerComponent('cache', () => {
        try {
          return typeof this.cache?.size === 'function' ? 
            this.cache.size() < this.config.maxSessionData : 
            (this.cache?.cache?.size || 0) < this.config.maxSessionData;
        } catch (error) {
          this.logger.error('Health check failed for cache', { type: 'health_check_error', component: 'cache', error: error.message });
          return false;
        }
      });

      // Session Manager health check
      this.healthCheck.registerComponent('sessionManager', () => {
        try {
          return typeof this.sessionManager?.getActiveSessionsCount === 'function' ? 
            this.sessionManager.getActiveSessionsCount() < this.config.maxSessionData :
            (this.sessionManager?.activeSessions?.size || 0) < this.config.maxSessionData;
        } catch (error) {
          this.logger.error('Health check failed for sessionManager', { type: 'health_check_error', component: 'sessionManager', error: error.message });
          return false;
        }
      });

      this.logger.info('Health check components registered');
    } catch (error) {
      this.logger.error('Error registering health check components:', { error: error.message });
    }
  }

  /**
   * Register analyzers for health checks
   */
  registerAnalyzersHealthCheck() {
    try {
      const analyzers = [
        'behavioralAnalyzer',
        'cognitiveAnalyzer',
        'therapeuticAnalyzer',
        'progressAnalyzer',
        'sessionAnalyzer'
      ];
      analyzers.forEach(analyzer => {
        if (this.therapeuticSystems[analyzer]) {
          this.healthCheck.registerComponent(analyzer, () => this.componentManager.isComponentAvailable(analyzer));
        }
      });
      this.logger.info('Analyzers registered for health checks');
    } catch (error) {
      this.logger.error('Error registering analyzers for health checks:', { error: error.message });
    }
  }

  /**
   * Auto initialize system
   */
  async autoInitialize() {
    try {
      if (this.state === SYSTEM_STATES.READY || this.state === SYSTEM_STATES.RUNNING) {
        this.logger.info('System already initialized');
        return;
      }
      await this.initialize();
      this.logger.info('System auto-initialized');
    } catch (error) {
      this.logger.error('Error auto-initializing system:', { error: error.message });
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      this.intervals.forEach(interval => clearInterval(interval));
      this.intervals.clear();
      await this.componentManager.stopAllComponents();
      this.performanceManager.cleanup();
      this.cache.clear();
      this.sessionData.clear();
      this.therapeuticMetrics.clear();
      this.sessionManager = new SessionManager(this.logger);
      this.state = SYSTEM_STATES.INITIALIZING;
      this.logger.info('System cleaned up');
    } catch (error) {
      this.logger.error('Error during cleanup:', { error: error.message });
    }
  }

  /**
   * 🔑 Gerar chave de idempotência para evitar requisições duplicadas
   * @param {string} method - Nome do método
   * @param {Object} data - Dados para gerar a chave
   * @returns {string} Chave de idempotência única
   */
  generateIdempotencyKey(method, data) {
    try {
      // Criar hash dos dados relevantes
      const crypto = require('crypto');
      const relevantData = {
        method,
        sessionId: data.sessionId || data.session_id,
        userId: data.userId || data.childId || data.user_id,
        gameId: data.gameId || data.gameName || data.game_id,
        timestamp: Math.floor((data.timestamp || Date.now()) / 60000) // Agrupar por minuto
      };
      
      const dataString = JSON.stringify(relevantData, Object.keys(relevantData).sort());
      const hash = crypto.createHash('md5').update(dataString).digest('hex');
      
      return `${method}_${hash}`;
    } catch (error) {
      // Fallback para chave simples se crypto não estiver disponível
      const simpleKey = `${method}_${data.sessionId || data.userId || Date.now()}`;
      return simpleKey;
    }
  }

  /**
   * 🚫 Verificar se requisição é duplicada
   * @param {string} idempotencyKey - Chave de idempotência
   * @returns {boolean} True se for duplicada
   */
  isDuplicateRequest(idempotencyKey) {
    if (!this.idempotencyCache) {
      this.idempotencyCache = new Map();
    }
    
    const now = Date.now();
    const existing = this.idempotencyCache.get(idempotencyKey);
    
    if (existing) {
      // Verificar se ainda está dentro do TTL (5 minutos)
      if (now - existing.timestamp < 300000) {
        this.logger.warn('🚫 Requisição duplicada detectada', {
          idempotencyKey,
          originalTimestamp: existing.timestamp,
          currentTimestamp: now,
          method: existing.method
        });
        return true;
      } else {
        // Limpar entrada expirada
        this.idempotencyCache.delete(idempotencyKey);
      }
    }
    
    return false;
  }

  /**
   * 📝 Registrar requisição processada
   * @param {string} idempotencyKey - Chave de idempotência
   * @param {string} method - Método que foi executado
   * @param {Object} result - Resultado do processamento
   */
  registerProcessedRequest(idempotencyKey, method, result) {
    if (!this.idempotencyCache) {
      this.idempotencyCache = new Map();
    }
    
    this.idempotencyCache.set(idempotencyKey, {
      timestamp: Date.now(),
      method,
      result,
      processed: true
    });
    
    // Limpar cache periodicamente (manter apenas últimas 1000 entradas)
    if (this.idempotencyCache.size > 1000) {
      const entries = Array.from(this.idempotencyCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      // Remover 20% das entradas mais antigas
      const toRemove = entries.slice(0, Math.floor(entries.length * 0.2));
      toRemove.forEach(([key]) => this.idempotencyCache.delete(key));
      
      this.logger.debug('🧹 Cache de idempotência limpo', {
        removedEntries: toRemove.length,
        remainingEntries: this.idempotencyCache.size
      });
    }
  }

  /**
   * 🔄 Obter resultado de requisição já processada
   * @param {string} idempotencyKey - Chave de idempotência
   * @returns {Object|null} Resultado anterior ou null
   */
  getCachedRequestResult(idempotencyKey) {
    if (!this.idempotencyCache) {
      return null;
    }
    
    const cached = this.idempotencyCache.get(idempotencyKey);
    if (cached && cached.processed) {
      this.logger.info('🔄 Retornando resultado de requisição já processada', {
        idempotencyKey,
        method: cached.method,
        cachedTimestamp: cached.timestamp
      });
      return cached.result;
    }
    
    return null;
  }

  /**
   * 🧹 Limpar cache de idempotência
   */
  cleanupIdempotencyCache() {
    if (!this.idempotencyCache) {
      return;
    }
    
    const now = Date.now();
    const expired = [];
    
    for (const [key, value] of this.idempotencyCache.entries()) {
      if (now - value.timestamp > 300000) { // 5 minutos
        expired.push(key);
      }
    }
    
    expired.forEach(key => this.idempotencyCache.delete(key));
    
    if (expired.length > 0) {
      this.logger.debug('🧹 Entradas expiradas removidas do cache de idempotência', {
        removedCount: expired.length,
        remainingCount: this.idempotencyCache.size
      });
    }
  }
}

/**
 * 🔧 Função para obter instância do SystemOrchestrator
 * @param {Object} databaseService - Serviço de banco de dados
 * @param {Object} config - Configurações do sistema
 * @param {Object} gameSpecificProcessors - Processadores específicos de jogos
 * @returns {Promise<SystemOrchestrator>} Instância do SystemOrchestrator
 */
export async function getSystemOrchestrator(databaseService, config = {}, gameSpecificProcessors = null) {
  return await SystemOrchestrator.getInstance(databaseService, config, gameSpecificProcessors);
}

/**
 * 🔧 Função para obter instância simplificada do SystemOrchestrator
 * @param {Object} basicConfig - Configuração básica
 * @returns {Promise<SystemOrchestrator>} Instância simplificada do SystemOrchestrator
 */
export async function getSimpleSystemOrchestrator(basicConfig = {}) {
  // Mock básico do DatabaseService para uso simplificado
  const mockDb = {
    save: async (data) => ({ id: Date.now(), ...data }),
    find: async (query) => [],
    findOne: async (query) => null,
    update: async (id, data) => ({ id, ...data }),
    delete: async (id) => true,
    isConnected: () => true,
    query: async (sql, params) => []
  };

  const simpleConfig = {
    enableMetricsService: true,
    enableGameSpecificProcessors: true,
    enableMultisensoryIntegration: false, // Simplificado
    enableAdvancedMetricsEngine: false,   // Simplificado
    enablePredictiveAnalysis: false,      // Simplificado
    logLevel: 'warn', // Menos verbose
    ...basicConfig
  };

  return await SystemOrchestrator.getInstance(mockDb, simpleConfig);
}

// Export functions
export default SystemOrchestrator;