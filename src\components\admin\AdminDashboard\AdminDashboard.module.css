/* AdminDashboard.module.css */

/* Login Container */
.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.loginBox {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.loginBox h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.loginBox p {
  color: #666;
  margin-bottom: 30px;
}

.loginForm {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.inputGroup {
  text-align: left;
}

.inputGroup label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.passwordInput {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.passwordInput:focus {
  outline: none;
  border-color: #667eea;
}

.passwordInput:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.buttonGroup {
  display: flex;
  gap: 12px;
}

.loginButton {
  flex: 1;
  padding: 12px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.loginButton:hover:not(:disabled) {
  background: #5a6fd8;
}

.loginButton:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.backButton {
  flex: 1;
  padding: 12px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.backButton:hover {
  background: #5a6268;
}

.error {
  color: #F44336;
  background: #ffebee;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
}

.loginHint {
  margin-top: 20px;
  padding: 10px;
  background: #e3f2fd;
  border-radius: 6px;
  color: #1976d2;
  font-size: 14px;
}

/* Admin Container */
.adminContainer {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* Header */
.adminHeader {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e0e6ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.headerLeft h1 {
  margin: 0;
  color: #2c3e50;
  font-size: 24px;
}

.version {
  color: #7f8c8d;
  font-size: 14px;
  margin-left: 10px;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 20px;
}

.adminUser {
  color: #34495e;
  font-weight: 500;
}

.logoutButton {
  padding: 8px 16px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.logoutButton:hover {
  background: #c0392b;
}

/* Tab Navigation */
.tabNavigation {
  background: white;
  padding: 0 30px;
  border-bottom: 1px solid #e0e6ed;
  display: flex;
  gap: 0;
  overflow-x: auto;
}

.tabButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  color: #7f8c8d;
}

.tabButton:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.tabButton.active {
  color: #3498db;
  border-bottom-color: #3498db;
  background: #f8f9fa;
}

.tabIcon {
  font-size: 18px;
}

.tabLabel {
  font-weight: 500;
}

/* Content */
.adminContent {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.tabContent {
  max-width: 1400px;
  margin: 0 auto;
}

.tabContent h2 {
  margin: 0 0 30px 0;
  color: #2c3e50;
  font-size: 28px;
}

/* Footer */
.adminFooter {
  background: white;
  padding: 20px 30px;
  border-top: 1px solid #e0e6ed;
  margin-top: auto;
}

.footerInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #7f8c8d;
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .adminHeader {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .headerLeft h1 {
    font-size: 20px;
  }

  .tabNavigation {
    padding: 0 20px;
  }

  .tabButton {
    padding: 12px 16px;
  }

  .tabLabel {
    display: none;
  }

  .adminContent {
    padding: 20px;
  }

  .tabContent h2 {
    font-size: 24px;
  }

  .footerInfo {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .loginBox {
    padding: 30px 20px;
  }

  .buttonGroup {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .loginContainer {
    padding: 10px;
  }

  .loginBox {
    padding: 20px;
  }

  .adminContent {
    padding: 15px;
  }

  .tabNavigation {
    padding: 0 15px;
  }
}
