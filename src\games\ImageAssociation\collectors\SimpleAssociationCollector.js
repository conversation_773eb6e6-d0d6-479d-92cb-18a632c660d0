// ============================================================================
// SIMPLE ASSOCIATION COLLECTOR - ATIVIDADE 1
// Coleta e análise de dados para associações simples e diretas
// ============================================================================

import { BaseCollector } from '../../../utils/BaseCollector.js';

export class SimpleAssociationCollector extends BaseCollector {
  constructor() {
    super('SimpleAssociation');
    
    this.cognitiveMetrics = {
      // Métricas específicas de associação simples
      associativeSpeed: [],
      relationRecognition: [],
      visualPatternMatching: [],
      semanticProcessing: [],
      errorPatterns: [],
      
      // Padrões de resposta
      responseConsistency: [],
      hintDependency: [],
      difficultyProgression: [],
      
      // Análise temporal
      reactionTimes: [],
      learningCurve: [],
      fatigueIndicators: []
    };

    this.associationTypes = {
      'animal-food': { weight: 1, complexity: 'low' },
      'tool-object': { weight: 1, complexity: 'low' },
      'cause-effect': { weight: 2, complexity: 'medium' },
      'problem-solution': { weight: 2, complexity: 'medium' },
      'device-accessory': { weight: 3, complexity: 'high' },
      'organ-aid': { weight: 3, complexity: 'high' },
      'location-access': { weight: 2, complexity: 'medium' },
      'vehicle-fuel': { weight: 2, complexity: 'medium' }
    };

    this.sessionData = {
      startTime: null,
      endTime: null,
      totalAssociations: 0,
      correctAssociations: 0,
      incorrectAssociations: 0,
      averageResponseTime: 0,
      associationsByType: {},
      errorsByType: {},
      hintUsage: 0,
      difficultyLevel: 'beginner'
    };
  }

  // ========================================================================
  // COLETA DE DADOS DE INTERAÇÃO
  // ========================================================================

  collectInteraction(interactionData) {
    const {
      selectedItems,
      validation,
      responseTime,
      timestamp,
      hintUsed = false,
      visualCuesEnabled = true
    } = interactionData;

    const associationType = validation.relationship;
    const isCorrect = validation.isCorrect;
    
    // Dados básicos da interação
    const interaction = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedItems: selectedItems.map(item => ({
        id: item.id,
        image: item.image,
        pairId: item.pairId,
        type: item.type
      })),
      validation: {
        isCorrect,
        relationship: associationType,
        reason: validation.reason
      },
      context: {
        hintUsed,
        visualCuesEnabled,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };

    this.interactions.push(interaction);

    // Análise cognitiva específica
    this.analyzeAssociativeProcessing(interaction);
    this.analyzeResponsePatterns(interaction);
    this.analyzeErrorPatterns(interaction);
    
    // Atualizar métricas de sessão
    this.updateSessionMetrics(interaction);

    return interaction;
  }

  // ========================================================================
  // ANÁLISE COGNITIVA ESPECIALIZADA
  // ========================================================================

  analyzeAssociativeProcessing(interaction) {
    const { responseTime, validation, context } = interaction;
    const associationType = validation.relationship;
    const complexity = this.associationTypes[associationType]?.complexity || 'medium';

    // Análise da velocidade associativa
    const speedMetric = {
      timestamp: interaction.timestamp,
      responseTime,
      complexity,
      associationType,
      isCorrect: validation.isCorrect,
      adjustedSpeed: this.calculateAdjustedSpeed(responseTime, complexity),
      efficiency: this.calculateEfficiency(responseTime, validation.isCorrect, complexity)
    };

    this.cognitiveMetrics.associativeSpeed.push(speedMetric);

    // Reconhecimento de relações
    const relationMetric = {
      timestamp: interaction.timestamp,
      relationType: associationType,
      recognized: validation.isCorrect,
      responseTime,
      complexity,
      confidence: this.calculateConfidence(responseTime, validation.isCorrect)
    };

    this.cognitiveMetrics.relationRecognition.push(relationMetric);

    // Processamento semântico
    const semanticMetric = {
      timestamp: interaction.timestamp,
      semanticDistance: this.calculateSemanticDistance(interaction.selectedItems),
      processingTime: responseTime,
      accurateProcessing: validation.isCorrect,
      semanticCategory: this.categorizeSemanticRelation(associationType)
    };

    this.cognitiveMetrics.semanticProcessing.push(semanticMetric);
  }

  analyzeResponsePatterns(interaction) {
    const { responseTime, validation, context } = interaction;

    // Consistência de resposta
    const recentInteractions = this.interactions.slice(-5);
    const consistencyScore = this.calculateResponseConsistency(recentInteractions);
    
    this.cognitiveMetrics.responseConsistency.push({
      timestamp: interaction.timestamp,
      consistencyScore,
      windowSize: recentInteractions.length
    });

    // Dependência de dicas
    if (context.hintUsed) {
      this.cognitiveMetrics.hintDependency.push({
        timestamp: interaction.timestamp,
        responseTime,
        wasCorrect: validation.isCorrect,
        associationType: validation.relationship
      });
    }

    // Progressão de dificuldade
    const difficultyMetric = {
      timestamp: interaction.timestamp,
      currentDifficulty: context.difficultyLevel,
      performance: validation.isCorrect ? 1 : 0,
      responseTime,
      shouldIncreaseComplexity: this.shouldIncreaseComplexity(),
      shouldDecreaseComplexity: this.shouldDecreaseComplexity()
    };

    this.cognitiveMetrics.difficultyProgression.push(difficultyMetric);
  }

  analyzeErrorPatterns(interaction) {
    if (!interaction.validation.isCorrect) {
      const errorType = this.classifyError(interaction);
      
      const errorAnalysis = {
        timestamp: interaction.timestamp,
        errorType,
        selectedItems: interaction.selectedItems,
        correctRelationship: interaction.validation.relationship,
        responseTime: interaction.responseTime,
        context: interaction.context,
        cognitiveLoad: this.estimateCognitiveLoad(interaction),
        interferenceType: this.detectInterference(interaction)
      };

      this.cognitiveMetrics.errorPatterns.push(errorAnalysis);
    }
  }

  // ========================================================================
  // CÁLCULOS E MÉTRICAS ESPECIALIZADAS
  // ========================================================================

  calculateAdjustedSpeed(responseTime, complexity) {
    const baselinesByComplexity = {
      'low': 3000,    // 3 segundos para associações simples
      'medium': 5000, // 5 segundos para associações médias
      'high': 8000    // 8 segundos para associações complexas
    };

    const baseline = baselinesByComplexity[complexity] || 5000;
    return Math.max(0, (baseline - responseTime) / baseline);
  }

  calculateEfficiency(responseTime, isCorrect, complexity) {
    if (!isCorrect) return 0;
    
    const maxTimeByComplexity = {
      'low': 5000,
      'medium': 8000,
      'high': 12000
    };

    const maxTime = maxTimeByComplexity[complexity] || 8000;
    return Math.max(0, (maxTime - responseTime) / maxTime);
  }

  calculateConfidence(responseTime, isCorrect) {
    // Confiança baseada na velocidade e precisão
    if (!isCorrect) return 0;
    
    // Tempo ideal para máxima confiança (2-4 segundos)
    const idealTime = 3000;
    const timeFactor = Math.exp(-Math.abs(responseTime - idealTime) / 2000);
    
    return timeFactor;
  }

  calculateSemanticDistance(selectedItems) {
    if (selectedItems.length !== 2) return 0;
    
    // Matriz de distâncias semânticas simplificada
    const semanticCategories = {
      'animals': ['🐕', '🦴', '🐱', '🐸', '🦋'],
      'objects': ['🔑', '🔒', '📱', '🔌', '👓'],
      'nature': ['☀️', '🌡️', '🌧️', '☂️', '🌱'],
      'body': ['👁️', '👂', '👶', '💪'],
      'vehicles': ['🚗', '⛽', '✈️', '🚲'],
      'places': ['🏠', '🏥', '🏛️', '🎪']
    };

    const [item1, item2] = selectedItems;
    let category1 = null, category2 = null;

    for (const [category, items] of Object.entries(semanticCategories)) {
      if (items.includes(item1.image)) category1 = category;
      if (items.includes(item2.image)) category2 = category;
    }

    if (category1 === category2) return 0.1; // Mesma categoria
    if (category1 && category2) return 0.5;  // Categorias diferentes
    return 1.0; // Sem categoria definida
  }

  categorizeSemanticRelation(associationType) {
    const categories = {
      'animal-food': 'biological',
      'tool-object': 'functional',
      'cause-effect': 'causal',
      'problem-solution': 'logical',
      'device-accessory': 'technological',
      'organ-aid': 'medical',
      'location-access': 'spatial',
      'vehicle-fuel': 'mechanical'
    };

    return categories[associationType] || 'unknown';
  }

  calculateResponseConsistency(recentInteractions) {
    if (recentInteractions.length < 2) return 1.0;

    const responseTimes = recentInteractions.map(i => i.responseTime);
    const mean = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const variance = responseTimes.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Consistência inversa ao desvio padrão normalizado
    const consistencyScore = Math.max(0, 1 - (standardDeviation / mean));
    
    return consistencyScore;
  }

  shouldIncreaseComplexity() {
    const recentInteractions = this.interactions.slice(-10);
    if (recentInteractions.length < 5) return false;

    const accuracy = recentInteractions.filter(i => i.validation.isCorrect).length / recentInteractions.length;
    const averageTime = recentInteractions.reduce((sum, i) => sum + i.responseTime, 0) / recentInteractions.length;

    return accuracy >= 0.8 && averageTime < 4000;
  }

  shouldDecreaseComplexity() {
    const recentInteractions = this.interactions.slice(-10);
    if (recentInteractions.length < 5) return false;

    const accuracy = recentInteractions.filter(i => i.validation.isCorrect).length / recentInteractions.length;
    
    return accuracy < 0.5;
  }

  classifyError(interaction) {
    const { selectedItems, validation } = interaction;
    
    if (selectedItems.length !== 2) return 'incomplete_selection';
    
    const [item1, item2] = selectedItems;
    
    // Verificar se são do mesmo par mas ordem errada
    if (item1.pairId === item2.pairId) return 'impossible_error'; // Não deveria acontecer
    
    // Verificar se há similaridade visual
    if (this.hasVisualSimilarity(item1.image, item2.image)) {
      return 'visual_similarity_confusion';
    }
    
    // Verificar se há categoria semântica similar
    if (this.hasSimilarSemanticCategory(item1.image, item2.image)) {
      return 'semantic_category_confusion';
    }
    
    // Verificar se é associação por proximidade
    if (this.hasTemporalProximity(item1, item2)) {
      return 'temporal_proximity_error';
    }
    
    return 'random_association';
  }

  hasVisualSimilarity(image1, image2) {
    // Grupos de similaridade visual
    const visualGroups = [
      ['🔑', '🔒', '🔓'], // Objetos de fechadura
      ['☀️', '🌡️', '🔥'], // Objetos relacionados ao calor
      ['👁️', '👓', '👀'], // Objetos relacionados à visão
      ['🚗', '🚲', '✈️'], // Veículos
    ];

    return visualGroups.some(group => 
      group.includes(image1) && group.includes(image2)
    );
  }

  hasSimilarSemanticCategory(image1, image2) {
    const semanticDistance = this.calculateSemanticDistance([
      { image: image1 }, 
      { image: image2 }
    ]);
    
    return semanticDistance < 0.3;
  }

  hasTemporalProximity(item1, item2) {
    // Se os itens foram apresentados próximos temporalmente
    return Math.abs(item1.id.split('_')[0] - item2.id.split('_')[0]) <= 1;
  }

  estimateCognitiveLoad(interaction) {
    const { responseTime, context } = interaction;
    
    // Fatores que aumentam carga cognitiva
    let loadFactors = 0;
    
    if (responseTime > 8000) loadFactors += 0.3; // Tempo excessivo
    if (context.hintUsed) loadFactors += 0.2;    // Precisou de dica
    if (!context.visualCuesEnabled) loadFactors += 0.2; // Sem pistas visuais
    
    const difficultyMultiplier = {
      'beginner': 1.0,
      'intermediate': 1.3,
      'advanced': 1.6
    }[context.difficultyLevel] || 1.0;
    
    return Math.min(1.0, loadFactors * difficultyMultiplier);
  }

  detectInterference(interaction) {
    const recentErrors = this.cognitiveMetrics.errorPatterns.slice(-3);
    
    if (recentErrors.length < 2) return 'none';
    
    const errorTypes = recentErrors.map(e => e.errorType);
    
    if (errorTypes.every(type => type === 'visual_similarity_confusion')) {
      return 'visual_interference';
    }
    
    if (errorTypes.every(type => type === 'semantic_category_confusion')) {
      return 'semantic_interference';
    }
    
    if (errorTypes.some(type => type === 'temporal_proximity_error')) {
      return 'temporal_interference';
    }
    
    return 'mixed_interference';
  }

  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================

  updateSessionMetrics(interaction) {
    const { validation, responseTime, context } = interaction;
    const associationType = validation.relationship;

    this.sessionData.totalAssociations++;
    
    if (validation.isCorrect) {
      this.sessionData.correctAssociations++;
    } else {
      this.sessionData.incorrectAssociations++;
    }

    // Atualizar tempo médio de resposta
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageResponseTime = totalTime / this.interactions.length;

    // Atualizar associações por tipo
    if (!this.sessionData.associationsByType[associationType]) {
      this.sessionData.associationsByType[associationType] = { total: 0, correct: 0 };
    }
    
    this.sessionData.associationsByType[associationType].total++;
    if (validation.isCorrect) {
      this.sessionData.associationsByType[associationType].correct++;
    }

    // Atualizar erros por tipo se aplicável
    if (!validation.isCorrect) {
      const errorType = this.classifyError(interaction);
      
      if (!this.sessionData.errorsByType[errorType]) {
        this.sessionData.errorsByType[errorType] = 0;
      }
      
      this.sessionData.errorsByType[errorType]++;
    }

    // Atualizar uso de dicas
    if (context.hintUsed) {
      this.sessionData.hintUsage++;
    }
  }

  // ========================================================================
  // RELATÓRIOS E ANÁLISES
  // ========================================================================

  generateCognitiveReport() {
    return {
      associativeProcessing: this.analyzeAssociativeProcessing(),
      responsePatterns: this.analyzeResponsePatterns(),
      errorAnalysis: this.analyzeErrorPatterns(),
      learningProgression: this.analyzeLearningProgression(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }

  analyzeAssociativeProcessing() {
    const speedMetrics = this.cognitiveMetrics.associativeSpeed;
    const relationMetrics = this.cognitiveMetrics.relationRecognition;
    const semanticMetrics = this.cognitiveMetrics.semanticProcessing;

    return {
      averageSpeed: this.calculateAverageMetric(speedMetrics, 'adjustedSpeed'),
      averageEfficiency: this.calculateAverageMetric(speedMetrics, 'efficiency'),
      relationRecognitionRate: this.calculateSuccessRate(relationMetrics, 'recognized'),
      semanticProcessingQuality: this.calculateAverageMetric(semanticMetrics, 'accurateProcessing'),
      complexity: {
        low: this.getPerformanceByComplexity('low'),
        medium: this.getPerformanceByComplexity('medium'),
        high: this.getPerformanceByComplexity('high')
      }
    };
  }

  generateAdaptiveRecommendations() {
    const recommendations = [];
    
    // Análise de velocidade
    const averageSpeed = this.calculateAverageMetric(this.cognitiveMetrics.associativeSpeed, 'adjustedSpeed');
    if (averageSpeed < 0.3) {
      recommendations.push({
        type: 'difficulty_adjustment',
        recommendation: 'Reduzir complexidade das associações',
        confidence: 0.8
      });
    } else if (averageSpeed > 0.8) {
      recommendations.push({
        type: 'difficulty_adjustment',
        recommendation: 'Aumentar complexidade das associações',
        confidence: 0.9
      });
    }

    // Análise de dependência de dicas
    const hintDependency = this.cognitiveMetrics.hintDependency.length / this.interactions.length;
    if (hintDependency > 0.5) {
      recommendations.push({
        type: 'support_adjustment',
        recommendation: 'Manter suporte visual e reduzir gradualmente',
        confidence: 0.7
      });
    }

    // Análise de padrões de erro
    const errorPatterns = this.getErrorPatternDistribution();
    const dominantError = Object.keys(errorPatterns).reduce((a, b) => 
      errorPatterns[a] > errorPatterns[b] ? a : b
    );

    if (errorPatterns[dominantError] > 0.4) {
      recommendations.push({
        type: 'intervention',
        recommendation: `Foco em correção de ${dominantError}`,
        confidence: 0.8
      });
    }

    return recommendations;
  }

  getPerformanceByComplexity(complexity) {
    const speedMetrics = this.cognitiveMetrics.associativeSpeed.filter(m => m.complexity === complexity);
    
    return {
      count: speedMetrics.length,
      averageSpeed: this.calculateAverageMetric(speedMetrics, 'adjustedSpeed'),
      averageEfficiency: this.calculateAverageMetric(speedMetrics, 'efficiency'),
      successRate: speedMetrics.filter(m => m.isCorrect).length / speedMetrics.length || 0
    };
  }

  getErrorPatternDistribution() {
    const errorCounts = {};
    
    this.cognitiveMetrics.errorPatterns.forEach(error => {
      errorCounts[error.errorType] = (errorCounts[error.errorType] || 0) + 1;
    });

    const total = Object.values(errorCounts).reduce((sum, count) => sum + count, 0);
    
    Object.keys(errorCounts).forEach(errorType => {
      errorCounts[errorType] = errorCounts[errorType] / total;
    });

    return errorCounts;
  }

  getActivityScore() {
    if (this.sessionData.totalAssociations === 0) return 0;
    
    const accuracy = this.sessionData.correctAssociations / this.sessionData.totalAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageResponseTime - 3000) / 10000);
    const hintPenalty = Math.max(0, 1 - (this.sessionData.hintUsage / this.sessionData.totalAssociations));
    
    return Math.round(accuracy * speedFactor * hintPenalty * 1000);
  }
}

export default SimpleAssociationCollector;
