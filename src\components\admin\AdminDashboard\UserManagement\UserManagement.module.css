/**
 * @file UserManagement.module.css
 * @description Estilos para o componente de Gerenciamento de Usuários
 * @version 1.0.0
 */

.userManagement {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.searchInput {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-width: 200px;
}

.filterSelect {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  background-color: white;
}

.statsCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.statLabel {
  font-size: 0.9rem;
  opacity: 0.9;
}

.usersTable {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tableHeader {
  background: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 1rem;
  align-items: center;
}

.userRow {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 1rem;
  align-items: center;
  transition: background-color 0.2s;
}

.userRow:hover {
  background: #f8f9fa;
}

.userRow:last-child {
  border-bottom: none;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.2rem;
}

.userName {
  font-weight: 500;
  color: #333;
}

.userEmail {
  font-size: 0.9rem;
  color: #666;
}

.statusBadge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.statusActive {
  background: #d4edda;
  color: #155724;
}

.statusInactive {
  background: #f8d7da;
  color: #721c24;
}

.actionButtons {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  padding: 0.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.viewButton {
  background: #007bff;
  color: white;
}

.viewButton:hover {
  background: #0056b3;
}

.editButton {
  background: #28a745;
  color: white;
}

.editButton:hover {
  background: #1e7e34;
}

.deleteButton {
  background: #dc3545;
  color: white;
}

.deleteButton:hover {
  background: #c82333;
}

.noUsers {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .controls {
    justify-content: stretch;
  }

  .searchInput,
  .filterSelect {
    flex: 1;
  }

  .tableHeader,
  .userRow {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    text-align: left;
  }

  .userInfo {
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
  }

  .actionButtons {
    justify-content: flex-start;
    padding-top: 0.5rem;
  }
}
