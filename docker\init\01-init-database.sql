-- =====================================================
-- 🚀 PORTAL BETINA V3 - INICIALIZAÇÃO COMPLETA DO BANCO
-- =====================================================
-- Este script cria todas as tabelas e dados necessários
-- para o funcionamento completo do sistema
-- =====================================================

-- Configurações iniciais
SET timezone = 'America/Sao_Paulo';
SET client_encoding = 'UTF8';

-- =====================================================
-- 📋 1. TABELAS PRINCIPAIS DO SISTEMA
-- =====================================================

-- Tabela de sessões de jogos
CREATE TABLE IF NOT EXISTS game_sessions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    session_data JSONB NOT NULL DEFAULT '{}',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP,
    duration INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de métricas de jogos
CREATE TABLE IF NOT EXISTS game_metrics (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    metrics_data JSONB NOT NULL DEFAULT '{}',
    analysis_data JSONB DEFAULT '{}',
    accuracy DECIMAL(5,2) DEFAULT 0,
    response_time INTEGER DEFAULT 0,
    engagement_score DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de análises terapêuticas
CREATE TABLE IF NOT EXISTS therapeutic_analysis (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    analysis_type VARCHAR(100) NOT NULL,
    analysis_data JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(3,2) DEFAULT 0,
    recommendations JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de progresso do usuário
CREATE TABLE IF NOT EXISTS user_progress (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    progress_data JSONB NOT NULL DEFAULT '{}',
    milestone_data JSONB DEFAULT '{}',
    last_session TIMESTAMP,
    total_sessions INTEGER DEFAULT 0,
    average_accuracy DECIMAL(5,2) DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de logs do sistema
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    component VARCHAR(100),
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de sessões de usuário
CREATE TABLE IF NOT EXISTS user_sessions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 🔬 2. TABELAS MULTISSENSORIAIS
-- =====================================================

-- Tabela principal de dados multissensoriais
CREATE TABLE IF NOT EXISTS multisensory_data (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    sensor_type VARCHAR(100) NOT NULL,
    sensor_data JSONB NOT NULL DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    calibration_data JSONB DEFAULT '{}',
    processing_metadata JSONB DEFAULT '{}'
);

-- Tabela de calibração de sensores
CREATE TABLE IF NOT EXISTS sensor_calibration (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    device_id VARCHAR(255),
    sensor_type VARCHAR(100) NOT NULL,
    calibration_data JSONB NOT NULL DEFAULT '{}',
    accuracy_score DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de leituras de sensores
CREATE TABLE IF NOT EXISTS sensor_readings (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    sensor_type VARCHAR(50) NOT NULL,
    reading_data JSONB NOT NULL DEFAULT '{}',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de interações touch
CREATE TABLE IF NOT EXISTS touch_interactions (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    x_coordinate INTEGER,
    y_coordinate INTEGER,
    pressure DECIMAL(3,2),
    duration INTEGER,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de dados do acelerômetro
CREATE TABLE IF NOT EXISTS accelerometer_data (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    x_axis DECIMAL(10,6),
    y_axis DECIMAL(10,6),
    z_axis DECIMAL(10,6),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de dados do giroscópio
CREATE TABLE IF NOT EXISTS gyroscope_data (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    alpha DECIMAL(10,6),
    beta DECIMAL(10,6),
    gamma DECIMAL(10,6),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 🎮 3. TABELAS ESPECÍFICAS DOS JOGOS
-- =====================================================

-- Função para criar tabelas de jogos dinamicamente
DO $$
DECLARE
    game_table TEXT;
    game_tables TEXT[] := ARRAY[
        'colormatch_metrics',
        'contagemnumeros_metrics', 
        'imageassociation_metrics',
        'memorygame_metrics',
        'musicalsequence_metrics',
        'padroesvisuais_metrics',
        'quebracabeca_metrics',
        'creativepainting_metrics',
        'letterrecognition_metrics'
    ];
BEGIN
    FOREACH game_table IN ARRAY game_tables
    LOOP
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I (
                id SERIAL PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL DEFAULT ''default_session'',
                user_id VARCHAR(255) NOT NULL DEFAULT ''default_user'',
                game_specific_data JSONB DEFAULT ''{}''::jsonb,
                collectors_data JSONB DEFAULT ''{}''::jsonb,
                performance_metrics JSONB DEFAULT ''{}''::jsonb,
                therapeutic_indicators JSONB DEFAULT ''{}''::jsonb,
                multisensory_data JSONB DEFAULT ''{}''::jsonb,
                accuracy DECIMAL(5,2) DEFAULT 0,
                response_time INTEGER DEFAULT 0,
                engagement_score DECIMAL(5,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )', game_table);
        
        RAISE NOTICE 'Tabela % criada com sucesso', game_table;
    END LOOP;
END $$;

-- =====================================================
-- 📊 4. TABELAS AUXILIARES PARA SISTEMA
-- =====================================================

-- Tabela para análises específicas de jogos
CREATE TABLE IF NOT EXISTS game_specific_analysis (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    game_id VARCHAR(255) NOT NULL,
    analysis_data JSONB NOT NULL DEFAULT '{}',
    collectors_results JSONB DEFAULT '{}',
    therapeutic_insights JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de perfis cognitivos
CREATE TABLE IF NOT EXISTS cognitive_profiles (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    profile_data JSONB NOT NULL DEFAULT '{}',
    cognitive_strengths JSONB DEFAULT '{}',
    areas_for_improvement JSONB DEFAULT '{}',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de métricas gerais
CREATE TABLE IF NOT EXISTS metrics (
    id SERIAL PRIMARY KEY,
    metric_type VARCHAR(100) NOT NULL,
    metric_data JSONB NOT NULL DEFAULT '{}',
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 🔍 5. ÍNDICES PARA PERFORMANCE
-- =====================================================

-- Índices para game_sessions
CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON game_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_game_id ON game_sessions(game_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_started_at ON game_sessions(started_at);

-- Índices para game_metrics
CREATE INDEX IF NOT EXISTS idx_game_metrics_user_id ON game_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_game_metrics_game_id ON game_metrics(game_id);
CREATE INDEX IF NOT EXISTS idx_game_metrics_created_at ON game_metrics(created_at);
CREATE INDEX IF NOT EXISTS idx_game_metrics_accuracy ON game_metrics(accuracy);

-- Índices para multisensory_data
CREATE INDEX IF NOT EXISTS idx_multisensory_session ON multisensory_data(session_id);
CREATE INDEX IF NOT EXISTS idx_multisensory_sensor_type ON multisensory_data(sensor_type);
CREATE INDEX IF NOT EXISTS idx_multisensory_timestamp ON multisensory_data(timestamp);

-- Índices para therapeutic_analysis
CREATE INDEX IF NOT EXISTS idx_therapeutic_analysis_user_id ON therapeutic_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_analysis_type ON therapeutic_analysis(analysis_type);

-- Índices para user_progress
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_progress_unique ON user_progress(user_id, game_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_updated_at ON user_progress(updated_at);

-- =====================================================
-- 📈 6. VIEW PARA DASHBOARD
-- =====================================================

-- View consolidada para dashboard
CREATE OR REPLACE VIEW dashboard_metrics AS
SELECT 
    gs.game_id,
    gs.user_id,
    gs.session_id,
    gs.started_at,
    gs.duration,
    gm.accuracy,
    gm.response_time,
    gm.engagement_score,
    COUNT(md.id) as multisensory_readings,
    ta.analysis_type,
    ta.confidence_score,
    up.total_sessions,
    up.average_accuracy as user_avg_accuracy
FROM game_sessions gs
LEFT JOIN game_metrics gm ON gs.session_id = gm.session_id
LEFT JOIN multisensory_data md ON gs.session_id = md.session_id
LEFT JOIN therapeutic_analysis ta ON gs.session_id = ta.session_id
LEFT JOIN user_progress up ON gs.user_id = up.user_id AND gs.game_id = up.game_id;

-- =====================================================
-- 🎯 7. DADOS INICIAIS DE TESTE
-- =====================================================

-- Inserir dados de exemplo para demonstração
INSERT INTO game_sessions (session_id, user_id, game_id, session_data, duration) VALUES
('demo_session_001', 'demo_user', 'ColorMatch', '{"demo": true, "level": 1}', 180000),
('demo_session_002', 'demo_user', 'MemoryGame', '{"demo": true, "level": 2}', 240000),
('demo_session_003', 'demo_user', 'QuebraCabeca', '{"demo": true, "level": 1}', 300000)
ON CONFLICT (session_id) DO NOTHING;

-- Inserir métricas de exemplo
INSERT INTO game_metrics (session_id, user_id, game_id, metrics_data, accuracy, response_time, engagement_score) VALUES
('demo_session_001', 'demo_user', 'ColorMatch', '{"colors_matched": 8, "total_colors": 10}', 80.0, 1500, 85.5),
('demo_session_002', 'demo_user', 'MemoryGame', '{"pairs_found": 6, "total_pairs": 8}', 75.0, 2200, 78.3),
('demo_session_003', 'demo_user', 'QuebraCabeca', '{"pieces_placed": 12, "total_pieces": 16}', 75.0, 3000, 82.1);

-- Inserir dados multissensoriais de exemplo
INSERT INTO multisensory_data (session_id, user_id, game_id, sensor_type, sensor_data) VALUES
('demo_session_001', 'demo_user', 'ColorMatch', 'touch', '{"x": 150, "y": 200, "pressure": 0.7}'),
('demo_session_001', 'demo_user', 'ColorMatch', 'accelerometer', '{"x": 0.1, "y": 0.2, "z": 9.8}'),
('demo_session_002', 'demo_user', 'MemoryGame', 'touch', '{"x": 300, "y": 400, "pressure": 0.8}'),
('demo_session_003', 'demo_user', 'QuebraCabeca', 'gyroscope', '{"alpha": 10.5, "beta": 5.2, "gamma": 2.1}');

-- Inserir análises terapêuticas de exemplo
INSERT INTO therapeutic_analysis (session_id, user_id, analysis_type, analysis_data, confidence_score) VALUES
('demo_session_001', 'demo_user', 'cognitive', '{"attention": 0.8, "memory": 0.7, "processing_speed": 0.75}', 0.85),
('demo_session_002', 'demo_user', 'behavioral', '{"engagement": 0.78, "persistence": 0.82, "frustration_tolerance": 0.65}', 0.75),
('demo_session_003', 'demo_user', 'motor', '{"fine_motor": 0.82, "coordination": 0.78, "spatial_awareness": 0.85}', 0.82);

-- Inserir progresso do usuário
INSERT INTO user_progress (user_id, game_id, progress_data, total_sessions, average_accuracy) VALUES
('demo_user', 'ColorMatch', '{"level": 2, "achievements": ["first_game", "color_master"]}', 5, 78.5),
('demo_user', 'MemoryGame', '{"level": 3, "achievements": ["memory_champion"]}', 8, 82.3),
('demo_user', 'QuebraCabeca', '{"level": 1, "achievements": ["puzzle_solver"]}', 3, 75.8)
ON CONFLICT (user_id, game_id) DO UPDATE SET
    total_sessions = EXCLUDED.total_sessions,
    average_accuracy = EXCLUDED.average_accuracy,
    updated_at = CURRENT_TIMESTAMP;

-- =====================================================
-- 🎮 9. DADOS ESPECÍFICOS DOS JOGOS
-- =====================================================

-- Inserir dados específicos para ColorMatch
INSERT INTO colormatch_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_001', 'demo_user', '{"selectedItems": [{"id": 1, "color": "red", "correct": true}], "targetColors": ["red", "blue"], "difficulty": "easy"}', 80.0, 1500, 85.5);

-- Inserir dados específicos para MemoryGame
INSERT INTO memorygame_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_002', 'demo_user', '{"pairs_found": 6, "total_pairs": 8, "difficulty": "medium", "memory_strategy": "visual"}', 75.0, 2200, 78.3);

-- Inserir dados específicos para QuebraCabeca
INSERT INTO quebracabeca_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_003', 'demo_user', '{"pieces_placed": 12, "total_pieces": 16, "difficulty": "easy", "assembly_strategy": "edge_first"}', 75.0, 3000, 82.1);

-- Inserir dados para outros jogos
INSERT INTO contagemnumeros_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_004', 'demo_user', '{"numbers_counted": 15, "total_numbers": 20, "difficulty": "medium"}', 75.0, 1800, 80.2);

INSERT INTO imageassociation_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_005', 'demo_user', '{"associations_made": 7, "total_associations": 10, "difficulty": "easy"}', 70.0, 2500, 75.8);

INSERT INTO musicalsequence_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_006', 'demo_user', '{"sequences_completed": 4, "total_sequences": 6, "difficulty": "medium"}', 66.7, 3200, 72.5);

INSERT INTO padroesvisuais_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_007', 'demo_user', '{"patterns_identified": 8, "total_patterns": 12, "difficulty": "hard"}', 66.7, 2800, 78.9);

INSERT INTO creativepainting_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_008', 'demo_user', '{"strokes_made": 45, "colors_used": 8, "creativity_score": 85}', 85.0, 4500, 92.3);

INSERT INTO letterrecognition_metrics (session_id, user_id, game_specific_data, accuracy, response_time, engagement_score) VALUES
('demo_session_009', 'demo_user', '{"letters_recognized": 18, "total_letters": 24, "difficulty": "easy"}', 75.0, 1200, 83.7);

-- =====================================================
-- ✅ 8. VERIFICAÇÃO FINAL
-- =====================================================

-- Função para verificar se tudo foi criado corretamente
DO $$
DECLARE
    table_count INTEGER;
    data_count INTEGER;
BEGIN
    -- Contar tabelas criadas
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name LIKE '%game%' OR table_name LIKE '%multisensory%' OR table_name LIKE '%sensor%';
    
    -- Contar dados inseridos
    SELECT COUNT(*) INTO data_count FROM game_sessions;
    
    RAISE NOTICE '✅ Inicialização concluída: % tabelas criadas, % sessões de exemplo', table_count, data_count;
END $$;

-- Mensagem final
SELECT 
    '🎉 PORTAL BETINA V3 - BANCO INICIALIZADO COM SUCESSO!' as status,
    COUNT(*) as total_tables
FROM information_schema.tables 
WHERE table_schema = 'public';
