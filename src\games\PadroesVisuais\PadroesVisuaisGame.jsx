import React, { useState, useEffect, useContext, useCallback, useMemo } from 'react';
import { PadroesVisuaisConfig, ACTIVITY_CONFIG, ACTIVITY_DIFFICULTY_CONFIG } from './PadroesVisuaisConfig';
import { PadroesVisuaisMetrics } from './PadroesVisuaisMetrics';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { AdvancedMetricsEngine } from '../../api/services/algorithms/AdvancedMetricsEngine.js';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { PadroesVisuaisCollectorsHub } from './collectors/index.js';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';
import styles from './PadroesVisuais.module.css';

function PadroesVisuaisGame() {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Inicializar collectorsHub
  const collectorsHub = useMemo(() => new PadroesVisuaisCollectorsHub(), []);

  // Estados do sistema de atividades V3
  const [currentActivity, setCurrentActivity] = useState(0);
  const [activityProgress, setActivityProgress] = useState({
    reproducao_sequencias: { completed: 0, level: 1, totalScore: 0 },
    completar_padroes: { completed: 0, level: 1, totalScore: 0 },
    construcao_padroes: { completed: 0, level: 1, totalScore: 0 },
    classificacao_visual: { completed: 0, level: 1, totalScore: 0 },
    transformacao_padroes: { completed: 0, level: 1, totalScore: 0 },
    deteccao_anomalias: { completed: 0, level: 1, totalScore: 0 },
  });

  // Estados específicos das atividades V3
  const [sequenceToReproduce, setSequenceToReproduce] = useState([]);
  const [patternToComplete, setPatternToComplete] = useState(null);
  const [constructionRules, setConstructionRules] = useState(null);
  const [classificationCriteria, setClassificationCriteria] = useState(null);
  const [transformationTarget, setTransformationTarget] = useState(null);
  const [anomalyPattern, setAnomalyPattern] = useState(null);
  const [playerConstruction, setPlayerConstruction] = useState([]);
  const [selectedTransformation, setSelectedTransformation] = useState(null);
  const [classificationGroups, setClassificationGroups] = useState([]);
  
  // Estados adicionais para novas funcionalidades
  const [transformationResult, setTransformationResult] = useState([]);
  const [classificationResults, setClassificationResults] = useState({});
  const [selectedForClassification, setSelectedForClassification] = useState(null);
  const [detectedAnomalies, setDetectedAnomalies] = useState([]);

  // Estados do jogo original
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameSequence, setGameSequence] = useState([]);
  const [playerSequence, setPlayerSequence] = useState([]);
  const [isShowingSequence, setIsShowingSequence] = useState(false);
  const [isPlayerTurn, setIsPlayerTurn] = useState(false);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [difficulty, setDifficulty] = useState('easy');
  const [feedback, setFeedback] = useState(null);
  const [playingShape, setPlayingShape] = useState(null);
  const [consecutiveSuccesses, setConsecutiveSuccesses] = useState(0);
  const [startTime, setStartTime] = useState(null);
  const [draggedShape, setDraggedShape] = useState(null);
  const [countdown, setCountdown] = useState(null);
  const [gameStats, setGameStats] = useState({
    score: 0,
    correctSequences: 0,
    totalAttempts: 0,
    level: 1,
    streak: 0,
    stars: 0,
  });
  const [analysisResults, setAnalysisResults] = useState(null);
  const [attemptCount, setAttemptCount] = useState(0);
  const [advancedMetricsEngine] = useState(() => new AdvancedMetricsEngine());
  const [sessionInteractions, setSessionInteractions] = useState([]);
  const [sessionSequences, setSessionSequences] = useState([]);
  const [sessionId] = useState(() => `pattern-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const [ttsActive, setTtsActive] = useState(ttsEnabled);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentSpeechButton, setCurrentSpeechButton] = useState(null);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false);

  // Hooks unificados
  const unifiedLogic = useUnifiedGameLogic('visual-patterns');
  const multisensoryIntegration = useMultisensoryIntegration('visual-patterns', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
  });
  const therapeuticOrchestrator = useTherapeuticOrchestrator({
    gameType: 'visual-patterns',
    collectorsHub,
    multisensoryIntegration,
    autoUpdate: true,
    logLevel: 'info',
  });

  // Função para TTS
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) return;

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = options.rate || 1.0;
    utterance.pitch = options.pitch || 1.0;
    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
      if (options.onEnd) options.onEnd();
    };

    setIsSpeaking(true);
    setCurrentSpeechButton(options.buttonId || null);
    window.speechSynthesis.cancel();
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Função helper para obter emoji da forma
  const getShapeEmoji = useCallback((shapeId) => {
    const shape = PadroesVisuaisConfig.shapes.find(s => s.id === shapeId);
    return shape ? shape.emoji : shapeId;
  }, []);

  // Funções de acessibilidade TTS
  const explainGame = useCallback(() => {
    const explanation = `
      Bem-vindo ao Padrões Visuais! Este jogo ajuda a desenvolver sua memória visual, sequenciação, concentração e reconhecimento de padrões lógicos.
      Você observará sequências de formas coloridas e deverá reproduzi-las na ordem correta.
      Use as formas disponíveis para arrastar e soltar nos slots corretos.
      Você pode escolher entre três níveis de dificuldade: fácil, médio e avançado.
      Boa sorte e divirta-se!
    `;
    speak(explanation, {
      rate: 0.9,
      buttonId: 'explain',
      onEnd: () => console.log('Explicação do jogo anunciada'),
    });
  }, [speak]);

  const repeatInstruction = useCallback(() => {
    let instruction = '';
    if (isShowingSequence) {
      instruction = `Observe e memorize esta sequência de ${gameSequence.length} formas coloridas. ${countdown ? `Você tem ${countdown} segundos restantes para memorizar.` : ''}`;
    } else if (isPlayerTurn) {
      instruction = `Agora é sua vez! Recrie a sequência arrastando as formas para os slots corretos na ordem que você memorizou. São ${gameSequence.length} formas no total.`;
    } else {
      instruction = 'Preparando a próxima sequência. Aguarde um momento.';
    }
    speak(instruction, {
      rate: 0.9,
      buttonId: 'repeat',
      onEnd: () => console.log('Instrução repetida'),
    });
  }, [isShowingSequence, isPlayerTurn, gameSequence, countdown, speak]);

  const playFeedback = useCallback((message, type = 'neutral') => {
    const prefix = type === 'success' ? 'Parabéns! ' : type === 'error' ? 'Ops! ' : '';
    speak(prefix + message, {
      rate: type === 'success' ? 1.1 : 0.9,
      pitch: type === 'success' ? 1.2 : type === 'error' ? 0.8 : 1,
      onEnd: () => console.log(`Feedback ${type} anunciado`),
    });
  }, [speak]);

  const announceStats = useCallback(() => {
    const statsText = `
      Suas estatísticas atuais:
      Nível ${currentLevel}.
      Pontuação: ${gameStats.score} pontos.
      Sequência atual de acertos: ${gameStats.streak}.
      Precisão: ${getAccuracy()} por cento.
      Total de sequências corretas: ${gameStats.correctSequences} de ${gameStats.totalAttempts} tentativas.
    `;
    speak(statsText, {
      rate: 0.8,
      buttonId: 'stats',
      onEnd: () => console.log('Estatísticas anunciadas'),
    });
  }, [currentLevel, gameStats, speak]);

  const announceSequence = useCallback(() => {
    if (gameSequence.length === 0) {
      speak('Nenhuma sequência disponível no momento.');
      return;
    }
    const shapes = gameSequence.map(shapeId => {
      const shape = PadroesVisuaisConfig.shapes.find(s => s.id === shapeId);
      return shape ? shape.name : 'forma desconhecida';
    });
    const sequenceText = `
      A sequência atual tem ${gameSequence.length} formas:
      ${shapes.join(', ')}.
      ${isShowingSequence ? 'Continue observando para memorizar.' : 'Agora recrie esta sequência na ordem correta.'}
    `;
    speak(sequenceText, {
      rate: 0.8,
      buttonId: 'sequence',
      onEnd: () => console.log('Sequência anunciada'),
    });
  }, [gameSequence, isShowingSequence, speak]);

  const giveHint = useCallback(() => {
    let hint = '';
    if (isShowingSequence) {
      hint = `Dica: Tente criar uma história ou padrão mental com as ${gameSequence.length} formas para facilitar a memorização. Observe as cores e posições com atenção.`;
    } else if (isPlayerTurn) {
      const filledSlots = playerSequence.filter(shape => shape !== undefined).length;
      const remainingSlots = gameSequence.length - filledSlots;
      hint = remainingSlots > 0
        ? `Dica: Você já colocou ${filledSlots} formas. Ainda faltam ${remainingSlots} formas para completar a sequência. Lembre-se da ordem que você memorizou.`
        : 'Dica: Você preencheu todos os slots! Verifique se a ordem está correta antes de confirmar.';
    } else {
      hint = 'Dica: Use o tempo de preparação para se concentrar e se preparar para a próxima sequência.';
    }
    speak(hint, {
      rate: 0.8,
      pitch: 1.1,
      buttonId: 'hint',
      onEnd: () => console.log('Dica fornecida'),
    });
  }, [isShowingSequence, isPlayerTurn, gameSequence, playerSequence, speak]);

  // Funções de métricas e análise
  const processFinalMetrics = useCallback(async () => {
    try {
      console.log('🏁 Processando métricas finais da sessão de Padrões Visuais...');
      const finalSessionData = {
        sessionId,
        userId: user?.id || 'anonymous',
        gameType: 'PadroesVisuais',
        duration: Date.now() - startTime,
        interactions: sessionInteractions,
        sequences: sessionSequences,
        finalStats: gameStats,
        patternData: {
          finalLevel: currentLevel,
          difficulty,
          consecutiveSuccesses,
          totalSequences: sessionSequences.length,
        },
        accuracy: gameStats.correctSequences / Math.max(1, gameStats.totalAttempts),
        averageResponseTime: sessionInteractions.length > 0
          ? sessionInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / sessionInteractions.length
          : 0,
        completeness: 1,
        attentionScore: calculateAttentionScore(),
        sessionComplete: true,
      };
      const finalMetrics = await advancedMetricsEngine.processAdvancedMetrics(
        finalSessionData,
        { id: user?.id || 'anonymous', preferences: {} },
        []
      );
      console.log('📊 Métricas Finais de Padrões Visuais:', finalMetrics);
      if (finalMetrics.metrics.patterns) {
        console.log('🎨 Resumo Final - Análise de Padrões Visuais:', {
          sessionId,
          totalInteractions: sessionInteractions.length,
          totalSequences: sessionSequences.length,
          finalAccuracy: gameStats.correctSequences / Math.max(1, gameStats.totalAttempts),
          patternRecognition: finalMetrics.metrics.patterns.patternRecognition,
          visualMemory: finalMetrics.metrics.patterns.visualMemory,
          spatialProcessing: finalMetrics.metrics.patterns.spatialProcessing,
          insights: finalMetrics.insights,
          recommendations: finalMetrics.recommendations,
        });
      }
    } catch (error) {
      console.warn('Erro ao processar métricas finais:', error);
    }
  }, [sessionId, user, startTime, sessionInteractions, sessionSequences, gameStats, currentLevel, difficulty, consecutiveSuccesses, advancedMetricsEngine]);

  const processAdvancedMetrics = useCallback(async (currentInteraction) => {
    try {
      console.log('🔬 Processando métricas avançadas de padrões visuais...');
      const sessionData = {
        sessionId,
        userId: user?.id || 'anonymous',
        gameType: 'PadroesVisuais',
        duration: Date.now() - startTime,
        interactions: sessionInteractions,
        sequences: sessionSequences,
        patternData: {
          currentLevel,
          difficulty,
          consecutiveSuccesses,
          currentSequence: gameSequence,
          playerResponse: playerSequence,
        },
        accuracy: gameStats.correctSequences / Math.max(1, gameStats.totalAttempts),
        averageResponseTime: sessionInteractions.length > 0
          ? sessionInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / sessionInteractions.length
          : 0,
        completeness: currentInteraction.correct ? 1 : 0,
        attentionScore: calculateAttentionScore(),
      };
      const advancedMetrics = await advancedMetricsEngine.processAdvancedMetrics(
        sessionData,
        { id: user?.id || 'anonymous', preferences: {} },
        []
      );
      console.log('📊 Métricas Avançadas de Padrões Visuais:', advancedMetrics);
      if (advancedMetrics.metrics.patterns) {
        console.log('🎨 Análise de Padrões Visuais:', {
          recognitionAccuracy: advancedMetrics.metrics.patterns.patternRecognition?.overallAccuracy,
          visualMemoryCapacity: advancedMetrics.metrics.patterns.visualMemory?.memoryCapacity,
          spatialProcessing: advancedMetrics.metrics.patterns.spatialProcessing?.spatialOrientation,
          errorPatterns: advancedMetrics.metrics.patterns.errorPatterns,
        });
      }
      if (advancedMetrics.insights?.length > 0) {
        console.log('💡 Insights de Padrões:', advancedMetrics.insights);
      }
      if (advancedMetrics.recommendations?.length > 0) {
        console.log('🎯 Recomendações para Padrões:', advancedMetrics.recommendations);
      }
    } catch (error) {
      console.warn('Erro ao processar métricas avançadas:', error);
    }
  }, [sessionId, user, startTime, sessionInteractions, sessionSequences, gameStats, currentLevel, difficulty, consecutiveSuccesses, gameSequence, playerSequence, advancedMetricsEngine]);

  const calculateAttentionScore = useCallback(() => {
    if (sessionInteractions.length === 0) return 0.5;
    const recentInteractions = sessionInteractions.slice(-5);
    const avgResponseTime = recentInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / recentInteractions.length;
    const accuracy = recentInteractions.filter(i => i.correct).length / recentInteractions.length;
    const timeScore = Math.max(0, Math.min(1, (10000 - avgResponseTime) / 10000));
    return (accuracy + timeScore) / 2;
  }, [sessionInteractions]);

  const recordPatternInteraction = useCallback(async (playerSequence, gameSequence, isCorrect, completionTime) => {
    const interactionData = {
      playerSequence,
      correctSequence: gameSequence,
      isCorrect,
      completionTime,
      timestamp: Date.now(),
      attemptNumber: attemptCount + 1,
      difficulty,
      level: currentLevel,
      sequenceLength: gameSequence.length,
    };
    try {
      await multisensoryIntegration.recordInteraction('pattern_interaction', {
        selectedPattern: playerSequence,
        targetPattern: gameSequence,
        isCorrect,
        responseTime: completionTime,
        difficulty,
        level: currentLevel,
        patternType: 'sequence_reproduction',
      });
      await collectorsHub.collectSequenceData({
        ...interactionData,
        gameState: {
          score: gameStats.score,
          totalAttempts: gameStats.totalAttempts,
          correctSequences: gameStats.correctSequences,
          streak: gameStats.streak,
        },
      });
      const newAttemptCount = attemptCount + 1;
      setAttemptCount(newAttemptCount);
      if (newAttemptCount % 3 === 0) {
        await performCognitiveAnalysis();
      }
    } catch (error) {
      console.error('Erro ao coletar dados do padrão:', error);
    }
  }, [multisensoryIntegration, collectorsHub, attemptCount, difficulty, currentLevel, gameStats]);

  const performCognitiveAnalysis = useCallback(async () => {
    try {
      setCognitiveAnalysisVisible(true);
      const analysisData = await collectorsHub.performCompleteAnalysis();
      setAnalysisResults(analysisData);
      setTimeout(() => setCognitiveAnalysisVisible(false), 3000);
    } catch (error) {
      console.error('Erro na análise cognitiva de padrões:', error);
      setCognitiveAnalysisVisible(false);
    }
  }, [collectorsHub]);

  const getAccuracy = useCallback(() => {
    if (gameStats.totalAttempts === 0) return 100;
    return Math.round((gameStats.correctSequences / gameStats.totalAttempts) * 100);
  }, [gameStats]);

  const generateSequence = useCallback((length) => {
    const sequence = [];
    let lastShapeIndex = -1;
    for (let i = 0; i < length; i++) {
      let randomIndex;
      do {
        randomIndex = Math.floor(Math.random() * PadroesVisuaisConfig.shapes.length);
      } while (randomIndex === lastShapeIndex && PadroesVisuaisConfig.shapes.length > 1);
      lastShapeIndex = randomIndex;
      sequence.push(PadroesVisuaisConfig.shapes[randomIndex].id);
    }
    return sequence;
  }, []);

  const playShape = useCallback(async (shapeId) => {
    setPlayingShape(shapeId);
    const shape = PadroesVisuaisConfig.shapes.find(s => s.id === shapeId);
    if (!shape) {
      console.warn(`Forma não encontrada: ${shapeId}`);
      setPlayingShape(null);
      return;
    }
    console.log(`🎵 Reproduzindo forma: ${shape.name}`);
    setTimeout(() => setPlayingShape(null), 600);
  }, []);

  const showSequence = useCallback(async () => {
    setIsShowingSequence(true);
    setIsPlayerTurn(false);
    setPlayerSequence([]);
    setFeedback(null);
    const difficultyData = PadroesVisuaisConfig.difficulties.find(d => d.id === difficulty);
    const baseTime = difficultyData.showTime || 8000;
    const sequenceBonus = gameSequence.length * 1500;
    const showTime = baseTime + sequenceBonus;
    console.log(`📝 Mostrando sequência de ${gameSequence.length} formas por ${showTime}ms`);
    const formDescription = gameSequence.length === 1 ? 'forma' : 'formas';
    const memorizeMessage = `Atenção! Memorize esta sequência de ${gameSequence.length} ${formDescription} coloridas. Você tem ${Math.ceil(showTime / 1000)} segundos para memorizar.`;
    speak(memorizeMessage, {
      rate: 1.0,
      onEnd: () => console.log('Instrução de memorização anunciada'),
    });
    setCountdown(Math.ceil(showTime / 1000));
    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    setTimeout(() => {
      clearInterval(countdownInterval);
      setCountdown(null);
      setIsShowingSequence(false);
      setIsPlayerTurn(true);
      const turnMessage = `Agora é sua vez! Recrie a sequência arrastando as formas para os slots corretos.`;
      speak(turnMessage, {
        rate: 0.9,
        onEnd: () => console.log('Vez do jogador anunciada'),
      });
    }, showTime);
  }, [gameSequence, difficulty, speak]);

  const handleDragStart = (e, shapeId) => {
    setDraggedShape(shapeId);
    e.dataTransfer.setData('text/plain', shapeId);
  };

  const handleDragEnd = () => {
    setDraggedShape(null);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e, position) => {
    e.preventDefault();
    const shapeId = e.dataTransfer.getData('text/plain');
    if (shapeId && position !== undefined) {
      const newPlayerSequence = [...playerSequence];
      newPlayerSequence[position] = shapeId;
      setPlayerSequence(newPlayerSequence);
      checkSequenceCompletion(newPlayerSequence);
    }
  };

  const checkSequenceCompletion = useCallback((currentSequence) => {
    const filledSlots = currentSequence.filter(shape => shape !== undefined);
    if (filledSlots.length === gameSequence.length) {
      const completionTime = startTime ? Date.now() - startTime : 0;
      const isCorrect = gameSequence.every((shape, index) => currentSequence[index] === shape);
      recordPatternInteraction(currentSequence, gameSequence, isCorrect, completionTime);
      if (isCorrect) {
        handleCorrectSequence();
      } else {
        handleIncorrectSequence();
      }
    }
  }, [gameSequence, startTime, recordPatternInteraction]);

  const clearPlayerSequence = () => {
    setPlayerSequence([]);
  };

  const generateNewLevel = useCallback(() => {
    const difficultyData = PadroesVisuaisConfig.difficulties.find(d => d.id === difficulty);
    const sequenceLength = Math.min(difficultyData.sequenceLength + Math.floor(currentLevel / 3), 8);
    console.log(`🎯 Gerando novo nível - Dificuldade: ${difficulty}, Nível: ${currentLevel}, Tamanho calculado: ${sequenceLength}`);
    const newSequence = generateSequence(sequenceLength);
    console.log(`✨ Nova sequência gerada: ${JSON.stringify(newSequence)}, Comprimento: ${newSequence.length}`);
    setGameSequence(newSequence);
    setPlayerSequence([]);
    setFeedback(null);
    setTimeout(() => showSequence(), 1000);
  }, [difficulty, currentLevel, generateSequence, showSequence]);

  const handleCorrectSequence = useCallback(() => {
    const basePoints = 20;
    const levelBonus = currentLevel * 10;
    const lengthBonus = gameSequence.length * 5;
    const points = basePoints + levelBonus + lengthBonus;
    const responseTime = Date.now() - startTime;
    const interaction = {
      timestamp: Date.now(),
      correct: true,
      sequenceCorrect: true,
      responseTime,
      sequenceLength: gameSequence.length,
      level: currentLevel,
      difficulty,
      gameSequence: [...gameSequence],
      playerSequence: [...playerSequence],
      points,
      consecutiveSuccesses: consecutiveSuccesses + 1,
    };
    setSessionInteractions(prev => [...prev, interaction]);
    setSessionSequences(prev => [...prev, gameSequence]);
    setGameStats(prev => ({
      ...prev,
      score: prev.score + points,
      correctSequences: prev.correctSequences + 1,
      totalAttempts: prev.totalAttempts + 1,
      level: prev.level + 1,
      streak: prev.streak + 1,
    }));
    setCurrentLevel(prev => prev + 1);
    setConsecutiveSuccesses(prev => prev + 1);
    if ((consecutiveSuccesses + 1) % 3 === 0) {
      processAdvancedMetrics(interaction);
    }
    const message = PadroesVisuaisConfig.encouragingMessages[
      Math.floor(Math.random() * PadroesVisuaisConfig.encouragingMessages.length)
    ];
    playFeedback(`${message} Você ganhou ${points} pontos!`, 'success');
    setTimeout(() => {
      setFeedback(null);
      generateNewLevel();
    }, 3000);
  }, [gameSequence, playerSequence, currentLevel, difficulty, consecutiveSuccesses, startTime, generateNewLevel, playFeedback, processAdvancedMetrics]);

  const handleIncorrectSequence = useCallback(() => {
    const responseTime = Date.now() - startTime;
    const interaction = {
      timestamp: Date.now(),
      correct: false,
      sequenceCorrect: false,
      responseTime,
      sequenceLength: gameSequence.length,
      level: currentLevel,
      difficulty,
      gameSequence: [...gameSequence],
      playerSequence: [...playerSequence],
      points: 0,
      consecutiveSuccesses: 0,
      errorDetails: analyzeSequenceError(gameSequence, playerSequence),
    };
    setSessionInteractions(prev => [...prev, interaction]);
    setSessionSequences(prev => [...prev, gameSequence]);
    setGameStats(prev => ({
      ...prev,
      totalAttempts: prev.totalAttempts + 1,
      streak: 0,
    }));
    setConsecutiveSuccesses(0);
    processAdvancedMetrics(interaction);
    playFeedback('Sequência incorreta. Observe novamente a sequência correta!', 'error');
    setTimeout(() => {
      setIsShowingSequence(true);
      setIsPlayerTurn(false);
      setTimeout(() => {
        setPlayerSequence([]);
        setFeedback(null);
        setIsShowingSequence(false);
        setIsPlayerTurn(true);
      }, 5000);
    }, 1000);
  }, [gameSequence, playerSequence, currentLevel, difficulty, startTime, playFeedback, processAdvancedMetrics]);

  const analyzeSequenceError = (correctSequence, playerSequence) => {
    const errors = {
      positionErrors: [],
      shapeErrors: [],
      orderErrors: [],
      totalErrors: 0,
    };
    correctSequence.forEach((correctShape, index) => {
      const playerShape = playerSequence[index];
      if (playerShape !== correctShape) {
        errors.totalErrors++;
        errors.positionErrors.push(index);
        if (playerShape && correctSequence.includes(playerShape)) {
          errors.orderErrors.push({
            position: index,
            expected: correctShape,
            actual: playerShape,
            type: 'order',
          });
        } else if (playerShape) {
          errors.shapeErrors.push({
            position: index,
            expected: correctShape,
            actual: playerShape,
            type: 'shape',
          });
        } else {
          errors.shapeErrors.push({
            position: index,
            expected: correctShape,
            actual: null,
            type: 'missing',
          });
        }
      }
    });
    return errors;
  };

  const startGame = useCallback((selectedDifficulty) => {
    setDifficulty(selectedDifficulty);
    setCurrentLevel(1);
    setGameStats({
      score: 0,
      correctSequences: 0,
      totalAttempts: 0,
      level: 1,
      streak: 0,
      stars: 0,
    });
    setSessionInteractions([]);
    setSessionSequences([]);
    setGameStarted(true);
    setShowStartScreen(false);
    setStartTime(Date.now());
    const initializeMultisensory = async () => {
      try {
        await multisensoryIntegration.initializeSession(`session_${Date.now()}`, {
          difficulty: selectedDifficulty,
          gameMode: 'visual_patterns',
          userId: user?.id || 'anonymous',
        });
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
      }
    };
    initializeMultisensory();
    console.log('🎮 Iniciando sessão de Padrões Visuais com métricas avançadas:', {
      sessionId,
      difficulty: selectedDifficulty,
      userId: user?.id || 'anonymous',
      timestamp: new Date().toISOString(),
    });
    setTimeout(() => {
      generateNewLevel();
      const welcomeMessage = `Jogo iniciado! Dificuldade ${selectedDifficulty}. Prepare-se para a primeira sequência.`;
      speak(welcomeMessage, {
        rate: 0.9,
        onEnd: () => console.log('Boas-vindas anunciadas'),
      });
    }, 100);
  }, [multisensoryIntegration, user, generateNewLevel, speak, sessionId]);

  const initializeActivity = useCallback((activityIndex) => {
    setCurrentActivity(activityIndex);
    setSequenceToReproduce([]);
    setPatternToComplete(null);
    setConstructionRules(null);
    setClassificationCriteria(null);
    setTransformationTarget(null);
    setAnomalyPattern(null);
    setPlayerConstruction([]);
    setSelectedTransformation(null);
    setClassificationGroups([]);
    setFeedback(null);
    const activityKeys = Object.keys(ACTIVITY_CONFIG);
    const activityKey = activityKeys[activityIndex];
    if (activityKey) {
      console.log(`🎯 Inicializando atividade: ${activityKey}`);
    }
  }, []);

  useEffect(() => {
    if (gameStarted && !showStartScreen) {
      initializeActivity(0);
    }
  }, [gameStarted, showStartScreen, initializeActivity]);

  // Auto-start desabilitado para mostrar tela de dificuldade
  /*
  useEffect(() => {
    const autoStart = () => {
      setShowStartScreen(false);
      setGameStarted(true);
      setDifficulty('easy');
      setStartTime(Date.now());
    };
    const timer = setTimeout(autoStart, 1000);
    return () => clearTimeout(timer);
  }, []);
  */

  useEffect(() => {
    if (currentLevel > 10 && gameStarted) {
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await multisensoryIntegration.finalizeSession({
            finalScore: gameStats.score,
            finalAccuracy: gameStats.correctSequences / gameStats.totalAttempts,
            totalInteractions: gameStats.totalAttempts,
            sessionDuration: Date.now() - startTime,
            difficulty,
          });
          console.log('🔄 PadroesVisuais: Relatório multissensorial final:', multisensoryReport);
        } catch (error) {
          console.warn('⚠️ PadroesVisuais: Erro ao finalizar sessão multissensorial:', error);
        }
      };
      finalizeMultisensorySession();
    }
  }, [currentLevel, gameStarted, multisensoryIntegration, gameStats, startTime, difficulty]);

  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      console.log(`🔊 TTS ${newState ? 'ativado' : 'desativado'}`);
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
      localStorage.setItem('padroesVisuaisTTS', newState.toString());
      return newState;
    });
  }, []);

  useEffect(() => {
    const savedTTSState = localStorage.getItem('padroesVisuaisTTS');
    if (savedTTSState !== null) {
      setTtsActive(savedTTSState === 'true');
    }
  }, []);

  useEffect(() => {
    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      console.log('🔇 TTS parado ao sair do jogo Padrões Visuais');
    };
  }, []);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
    };
    const handlePageHide = () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('pagehide', handlePageHide);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('pagehide', handlePageHide);
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // =====================================================
  // 🎨 INTERFACE MODERNA DAS ATIVIDADES
  // =====================================================
  
  const renderEnhancedActivityInterface = () => {
    const activityKeys = Object.keys(ACTIVITY_CONFIG);
    const activityKey = activityKeys[currentActivity];
    
    switch (activityKey) {
      case 'reproducao_sequencias':
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>🔄 Reproduza a Sequência</h3>
              <p>Observe atentamente e reproduza na ordem correta</p>
            </div>
            
            {/* Sequência para memorizar - só aparece durante memorização */}
            {isShowingSequence && gameSequence.length > 0 && (
              <div className={styles.sequenceDisplay}>
                <h4>📝 Memorize esta sequência:</h4>
                <div className={styles.patternGrid}>
                  {gameSequence.map((shape, index) => (
                    <div 
                      key={index} 
                      className={`${styles.patternElement} ${styles.memorizing} ${playingShape === shape ? styles.active : ''}`}
                      style={{ 
                        animationDelay: `${index * 0.2}s`,
                        transform: playingShape === shape ? 'scale(1.2)' : 'scale(1)'
                      }}
                    >
                      {getShapeEmoji(shape)}
                    </div>
                  ))}
                </div>
                {countdown && (
                  <div className={styles.memorizeTimer}>
                    ⏱️ {countdown} segundos restantes para memorizar
                  </div>
                )}
              </div>
            )}
            
            {/* Área de reprodução - só aparece quando é a vez do jogador */}
            {isPlayerTurn && (
              <div className={styles.reproductionArea}>
                <h4>🎯 Sua vez! Recrie a sequência:</h4>
                <div className={styles.reproductionGrid}>
                  {Array.from({ length: gameSequence.length }, (_, index) => (
                    <div 
                      key={index} 
                      className={`${styles.reproductionSlot} ${playerSequence[index] ? styles.filled : styles.empty}`}
                      onDrop={(e) => handleDrop(e, index)}
                      onDragOver={handleDragOver}
                      data-slot={index}
                    >
                      <div className={styles.slotNumber}>{index + 1}</div>
                      <div className={styles.slotContent}>
                        {playerSequence[index] ? getShapeEmoji(playerSequence[index]) : '?'}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.progressIndicator}>
                  <span>Progresso: {playerSequence.filter(slot => slot).length} / {gameSequence.length}</span>
                </div>
              </div>
            )}
            
            {/* Opções de formas disponíveis - só aparece quando é a vez do jogador */}
            {isPlayerTurn && (
              <div className={styles.shapeOptions}>
                <h4>🎨 Arraste as formas ou clique para colocar:</h4>
                <div className={styles.shapePalette}>
                  {PadroesVisuaisConfig.shapes.map((shape) => (
                    <div
                      key={shape.id}
                      className={`${styles.shapeOption} ${draggedShape === shape.id ? styles.dragging : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, shape.id)}
                      onDragEnd={handleDragEnd}
                      onClick={() => {
                        const emptySlot = playerSequence.findIndex(slot => !slot);
                        if (emptySlot !== -1) {
                          addShapeToPattern(shape.id, emptySlot);
                        }
                      }}
                      title={`Forma: ${shape.name}`}
                    >
                      <div className={styles.shapeEmoji}>{shape.emoji}</div>
                      <div className={styles.shapeName}>{shape.name}</div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.gameHints}>
                  <p>💡 Dica: Você pode arrastar as formas ou clicar nelas para colocar no próximo slot vazio</p>
                </div>
              </div>
            )}
          </div>
        );
        
      case 'completar_padroes':
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>🧩 Complete o Padrão</h3>
              <p>Identifique o elemento que completa a sequência</p>
              
              <div className={styles.patternPuzzle}>
                <div className={styles.patternSequence}>
                  <div className={styles.patternElement}>🔴</div>
                  <div className={styles.patternElement}>🔵</div>
                  <div className={styles.patternElement}>🔴</div>
                  <div className={styles.patternElement}>🔵</div>
                  <div className={`${styles.patternElement} ${styles.empty}`}>?</div>
                </div>
                
                <div className={styles.completionOptions}>
                  <h4>Escolha a resposta:</h4>
                  <div className={styles.optionsGrid}>
                    <button 
                      className={styles.optionButton}
                      onClick={() => selectPattern('🔴')}
                    >
                      🔴
                    </button>
                    <button 
                      className={styles.optionButton}
                      onClick={() => selectPattern('🔵')}
                    >
                      🔵
                    </button>
                    <button 
                      className={styles.optionButton}
                      onClick={() => selectPattern('🟢')}
                    >
                      🟢
                    </button>
                    <button 
                      className={styles.optionButton}
                      onClick={() => selectPattern('🟡')}
                    >
                      🟡
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'construcao_padroes':
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>🏗️ Construa o Padrão</h3>
              <p>Use as formas para recriar o padrão objetivo</p>
            </div>
            
            <div className={styles.constructionWorkspace}>
              <div className={styles.targetPattern}>
                <h4>🎯 Padrão objetivo:</h4>
                <div className={styles.patternGrid}>
                  <div className={styles.patternElement}>{getShapeEmoji('circle')}</div>
                  <div className={styles.patternElement}>{getShapeEmoji('square')}</div>
                  <div className={styles.patternElement}>{getShapeEmoji('triangle')}</div>
                </div>
              </div>
              
              <div className={styles.constructionArea}>
                <h4>🔨 Sua construção:</h4>
                <div className={styles.reproductionGrid}>
                  {Array.from({ length: 3 }, (_, index) => (
                    <div 
                      key={index}
                      className={`${styles.reproductionSlot} ${playerConstruction[index] ? styles.filled : styles.empty}`}
                      onDrop={(e) => handleDrop(e, index)}
                      onDragOver={handleDragOver}
                      data-slot={index}
                    >
                      <div className={styles.slotNumber}>{index + 1}</div>
                      <div className={styles.slotContent}>
                        {playerConstruction[index] ? getShapeEmoji(playerConstruction[index]) : '?'}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.progressIndicator}>
                  <span>Progresso: {(playerConstruction || []).filter(slot => slot).length} / 3</span>
                </div>
              </div>
              
              <div className={styles.shapePalette}>
                <h4>🎨 Formas disponíveis:</h4>
                <div className={styles.optionsGrid}>
                  {PadroesVisuaisConfig.shapes.map((shape) => (
                    <div
                      key={shape.id}
                      className={`${styles.shapeOption} ${draggedShape === shape.id ? styles.dragging : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, shape.id)}
                      onDragEnd={handleDragEnd}
                      onClick={() => {
                        const emptySlot = (playerConstruction || []).findIndex(slot => !slot);
                        if (emptySlot !== -1) {
                          const newConstruction = [...(playerConstruction || [])];
                          newConstruction[emptySlot] = shape.id;
                          setPlayerConstruction(newConstruction);
                        }
                      }}
                      title={`Forma: ${shape.name}`}
                    >
                      <div className={styles.shapeEmoji}>{shape.emoji}</div>
                      <div className={styles.shapeName}>{shape.name}</div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.gameHints}>
                  <p>💡 Dica: Arraste as formas para os slots na ordem correta para recriar o padrão</p>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'classificacao_visual':
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>📊 Classificação Visual</h3>
              <p>Arraste os elementos para os grupos corretos</p>
            </div>
            
            <div className={styles.classificationWorkspace}>
              <div className={styles.elementsToClassify}>
                <h4>🎯 Elementos para classificar:</h4>
                <div className={styles.elementGrid}>
                  {['circle', 'square', 'circle', 'triangle', 'square', 'triangle'].map((shape, index) => (
                    <div
                      key={index}
                      className={`${styles.classificationElement} ${selectedForClassification === `${shape}-${index}` ? styles.selected : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, `${shape}-${index}`)}
                      onDragEnd={handleDragEnd}
                      onClick={() => setSelectedForClassification(`${shape}-${index}`)}
                      title={`Elemento: ${shape}`}
                    >
                      <div className={styles.elementEmoji}>{getShapeEmoji(shape)}</div>
                      <div className={styles.elementIndex}>{index + 1}</div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.progressIndicator}>
                  <span>Classificados: {Object.values(classificationResults || {}).filter(v => v).length} / 6</span>
                </div>
              </div>
              
              <div className={styles.classificationGroups}>
                <div className={styles.classGroup}>
                  <h5>🔴 Círculos</h5>
                  <div 
                    className={`${styles.dropZone} ${styles.circleZone}`}
                    onDrop={(e) => handleClassificationDrop(e, 'circle')}
                    onDragOver={handleDragOver}
                    data-group="circle"
                  >
                    <div className={styles.zoneContent}>
                      {Object.entries(classificationResults || {})
                        .filter(([key, value]) => value === 'circle')
                        .map(([key]) => {
                          const shape = key.split('-')[0];
                          return (
                            <div key={key} className={styles.classifiedElement}>
                              {getShapeEmoji(shape)}
                            </div>
                          );
                        })}
                      {Object.keys(classificationResults || {}).filter(key => 
                        classificationResults[key] === 'circle'
                      ).length === 0 && (
                        <div className={styles.zoneHint}>Arraste círculos aqui</div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className={styles.classGroup}>
                  <h5>🔵 Quadrados</h5>
                  <div 
                    className={`${styles.dropZone} ${styles.squareZone}`}
                    onDrop={(e) => handleClassificationDrop(e, 'square')}
                    onDragOver={handleDragOver}
                    data-group="square"
                  >
                    <div className={styles.zoneContent}>
                      {Object.entries(classificationResults || {})
                        .filter(([key, value]) => value === 'square')
                        .map(([key]) => {
                          const shape = key.split('-')[0];
                          return (
                            <div key={key} className={styles.classifiedElement}>
                              {getShapeEmoji(shape)}
                            </div>
                          );
                        })}
                      {Object.keys(classificationResults || {}).filter(key => 
                        classificationResults[key] === 'square'
                      ).length === 0 && (
                        <div className={styles.zoneHint}>Arraste quadrados aqui</div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className={styles.classGroup}>
                  <h5>🟢 Triângulos</h5>
                  <div 
                    className={`${styles.dropZone} ${styles.triangleZone}`}
                    onDrop={(e) => handleClassificationDrop(e, 'triangle')}
                    onDragOver={handleDragOver}
                    data-group="triangle"
                  >
                    <div className={styles.zoneContent}>
                      {Object.entries(classificationResults || {})
                        .filter(([key, value]) => value === 'triangle')
                        .map(([key]) => {
                          const shape = key.split('-')[0];
                          return (
                            <div key={key} className={styles.classifiedElement}>
                              {getShapeEmoji(shape)}
                            </div>
                          );
                        })}
                      {Object.keys(classificationResults || {}).filter(key => 
                        classificationResults[key] === 'triangle'
                      ).length === 0 && (
                        <div className={styles.zoneHint}>Arraste triângulos aqui</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className={styles.gameHints}>
                <p>💡 Dica: Arraste cada elemento para o grupo da forma correspondente</p>
              </div>
            </div>
          </div>
        );
        
      case 'transformacao_padroes':
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>🔄 Transformação de Padrões</h3>
              <p>Aplique a transformação indicada ao padrão</p>
            </div>
            
            <div className={styles.transformationWorkspace}>
              <div className={styles.transformationRule}>
                <h4>🎯 Regra: <span className={styles.rule}>ROTAÇÃO 90°</span></h4>
                <p>Gire o padrão 90 graus no sentido horário</p>
              </div>
              
              <div className={styles.transformationDemo}>
                <div className={styles.originalPattern}>
                  <h5>📋 Padrão Original:</h5>
                  <div className={styles.patternGrid}>
                    <div className={styles.patternElement}>{getShapeEmoji('triangle')}</div>
                    <div className={styles.patternElement}>{getShapeEmoji('circle')}</div>
                    <div className={styles.patternElement}>{getShapeEmoji('square')}</div>
                  </div>
                </div>
                
                <div className={styles.transformationArrow}>
                  <span className={styles.arrowIcon}>↻</span>
                  <span className={styles.arrowText}>90°</span>
                </div>
                
                <div className={styles.resultPattern}>
                  <h5>🎯 Resultado esperado:</h5>
                  <div className={styles.reproductionGrid}>
                    {Array.from({ length: 3 }, (_, index) => (
                      <div 
                        key={index}
                        className={`${styles.reproductionSlot} ${transformationResult[index] ? styles.filled : styles.empty}`}
                        onDrop={(e) => handleDrop(e, index)}
                        onDragOver={handleDragOver}
                        data-slot={index}
                      >
                        <div className={styles.slotNumber}>{index + 1}</div>
                        <div className={styles.slotContent}>
                          {transformationResult[index] ? getShapeEmoji(transformationResult[index]) : '?'}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className={styles.progressIndicator}>
                    <span>Progresso: {(transformationResult || []).filter(slot => slot).length} / 3</span>
                  </div>
                </div>
              </div>
              
              <div className={styles.shapePalette}>
                <h4>🎨 Formas disponíveis:</h4>
                <div className={styles.optionsGrid}>
                  {PadroesVisuaisConfig.shapes.map((shape) => (
                    <div
                      key={shape.id}
                      className={`${styles.shapeOption} ${draggedShape === shape.id ? styles.dragging : ''}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, shape.id)}
                      onDragEnd={handleDragEnd}
                      onClick={() => {
                        const emptySlot = (transformationResult || []).findIndex(slot => !slot);
                        if (emptySlot !== -1) {
                          const newResult = [...(transformationResult || [])];
                          newResult[emptySlot] = shape.id;
                          setTransformationResult(newResult);
                        }
                      }}
                      title={`Forma: ${shape.name}`}
                    >
                      <div className={styles.shapeEmoji}>{shape.emoji}</div>
                      <div className={styles.shapeName}>{shape.name}</div>
                    </div>
                  ))}
                </div>
                
                <div className={styles.gameHints}>
                  <p>� Dica: Visualize como o padrão ficaria após a rotação e monte o resultado</p>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'deteccao_anomalias':
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>🔍 Detecção de Anomalias</h3>
              <p>Encontre o elemento que não pertence ao padrão</p>
            </div>
            
            <div className={styles.anomalyWorkspace}>
              <div className={styles.patternToAnalyze}>
                <h4>🎯 Encontre o diferente:</h4>
                <div className={styles.anomalyGrid}>
                  {['circle', 'circle', 'square', 'circle', 'circle', 'circle'].map((shape, index) => (
                    <div
                      key={index}
                      className={`${styles.anomalyElement} ${
                        detectedAnomalies && detectedAnomalies.includes(index) ? 
                        (shape === 'square' ? styles.correctAnomaly : styles.wrongAnomaly) : 
                        styles.unselected
                      }`}
                      onClick={() => handleAnomalyDetection(index, shape)}
                      title={`Elemento ${index + 1}: ${shape}`}
                    >
                      <div className={styles.elementNumber}>{index + 1}</div>
                      <div className={styles.elementEmoji}>{getShapeEmoji(shape)}</div>
                      {detectedAnomalies && detectedAnomalies.includes(index) && (
                        <div className={styles.selectionIndicator}>
                          {shape === 'square' ? '✅' : '❌'}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                
                <div className={styles.anomalyStats}>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Detectadas:</span>
                    <span className={styles.statValue}>
                      {detectedAnomalies ? detectedAnomalies.length : 0} / 1
                    </span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Precisão:</span>
                    <span className={styles.statValue}>
                      {detectedAnomalies && detectedAnomalies.length > 0 ? 
                        Math.round((detectedAnomalies.filter(index => 
                          ['circle', 'circle', 'square', 'circle', 'circle', 'circle'][index] === 'square'
                        ).length / detectedAnomalies.length) * 100) : 0}%
                    </span>
                  </div>
                </div>
              </div>
              
              <div className={styles.anomalyAnalysis}>
                <h4>📊 Análise do Padrão:</h4>
                <div className={styles.patternBreakdown}>
                  <div className={styles.breakdownItem}>
                    <span className={styles.shapeIcon}>{getShapeEmoji('circle')}</span>
                    <span className={styles.shapeCount}>5 círculos</span>
                    <span className={styles.shapeStatus}>Padrão normal</span>
                  </div>
                  <div className={styles.breakdownItem}>
                    <span className={styles.shapeIcon}>{getShapeEmoji('square')}</span>
                    <span className={styles.shapeCount}>1 quadrado</span>
                    <span className={styles.shapeStatus}>Anomalia!</span>
                  </div>
                </div>
                
                <div className={styles.gameHints}>
                  <p>💡 Dica: O elemento diferente interrompe o padrão repetitivo</p>
                  <p>🎯 Meta: Encontre todas as anomalias com 100% de precisão</p>
                </div>
                
                {detectedAnomalies && detectedAnomalies.length > 0 && (
                  <div className={styles.detectionFeedback}>
                    {detectedAnomalies.filter(index => 
                      ['circle', 'circle', 'square', 'circle', 'circle', 'circle'][index] === 'square'
                    ).length === 1 && detectedAnomalies.length === 1 ? (
                      <div className={styles.successMessage}>
                        🎉 Parabéns! Você encontrou a anomalia corretamente!
                      </div>
                    ) : (
                      <div className={styles.tryAgainMessage}>
                        🤔 Continue procurando! Há elementos que não foram detectados corretamente.
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        );
        
      default:
        return (
          <div className={styles.activityInterface}>
            <div className={styles.instructionPanel}>
              <h3>🎯 Selecione uma Atividade</h3>
              <p>Escolha uma das atividades disponíveis na barra lateral</p>
            </div>
          </div>
        );
    }
  };

  // =====================================================
  // 🎮 FUNÇÕES DE INTERAÇÃO DAS ATIVIDADES
  // =====================================================
  
  // Adicionar forma ao padrão
  const addShapeToPattern = (shape, index) => {
    console.log('Forma adicionada:', shape, 'posição:', index);
    
    const newPlayerSequence = [...playerSequence];
    newPlayerSequence[index] = shape;
    setPlayerSequence(newPlayerSequence);
    
    // Verificar se completou a sequência
    checkSequenceCompletion(newPlayerSequence);
  };

  // Toggle classificação
  const toggleClassification = (element, type) => {
    console.log('Classificação:', element, type);
    
    // Atualizar grupos de classificação
    const newGroups = [...classificationGroups];
    const elementIndex = newGroups.findIndex(g => g.element === element);
    
    if (elementIndex >= 0) {
      newGroups[elementIndex].type = type;
    } else {
      newGroups.push({ element, type });
    }
    
    setClassificationGroups(newGroups);
    
    // Feedback de classificação
    speak(`${element} classificado como ${type}`);
  };

  // Selecionar transformação
  const selectTransformation = (option) => {
    console.log('Transformação selecionada:', option);
    
    setSelectedTransformation(option);
    
    // Verificar se a transformação está correta
    const isCorrect = option === transformationTarget;
    
    if (isCorrect) {
      setFeedback({
        type: 'success',
        message: 'Transformação correta! 🔄',
        score: 15
      });
      playFeedback('Transformação correta!', 'success');
    } else {
      setFeedback({
        type: 'error',
        message: 'Transformação incorreta. Tente novamente!'
      });
      playFeedback('Transformação incorreta.', 'error');
    }
  };

  // Toggle anomalia
  const toggleAnomaly = (element, type) => {
    console.log('Verificando anomalia:', element, type);
    
    // Verificar se é realmente uma anomalia
    const isAnomaly = type === 'anomaly';
    
    if (isAnomaly) {
      setFeedback({
        type: 'success',
        message: 'Anomalia encontrada! 🔍',
        score: 20
      });
      
      setGameStats(prev => ({
        ...prev,
        score: prev.score + 20,
        correctSequences: prev.correctSequences + 1
      }));
      
      playFeedback('Anomalia encontrada corretamente!', 'success');
    } else {
      setFeedback({
        type: 'error',
        message: 'Este elemento não é uma anomalia.'
      });
      playFeedback('Este elemento não é uma anomalia.', 'error');
    }
  };

  // Funções para Classificação Visual
  const handleClassificationDrop = (e, groupType) => {
    e.preventDefault();
    const elementKey = e.dataTransfer.getData('text/plain');
    if (elementKey) {
      setClassificationResults(prev => ({
        ...prev,
        [elementKey]: groupType
      }));
      setSelectedForClassification(null);
    }
  };

  // Função para Detecção de Anomalias
  const handleAnomalyDetection = (index, shape) => {
    setDetectedAnomalies(prev => {
      if (prev.includes(index)) {
        return prev.filter(i => i !== index);
      } else {
        return [...prev, index];
      }
    });
  };

  // =====================================================
  // 🎮 RENDERIZAÇÃO PRINCIPAL
  // =====================================================

  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Padrões Visuais"
        gameSubtitle="Desenvolva sua memória visual e sequencial"
        gameInstruction="Observe a sequência de formas coloridas e reproduza-a na ordem correta. Teste sua memória visual!"
        gameIcon="🎯"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: 'Sequências de 3 formas - Ideal para iniciantes',
            icon: '😊',
            preview: (
              <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
                <div style={{ fontSize: '20px' }}>🔴</div>
                <div style={{ fontSize: '20px' }}>🔵</div>
                <div style={{ fontSize: '20px' }}>🟢</div>
              </div>
            ),
          },
          {
            id: 'medium',
            name: 'Médio',
            description: 'Sequências de 4 formas - Desafio equilibrado',
            icon: '🎯',
            preview: (
              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center' }}>
                <div style={{ fontSize: '16px' }}>🔴</div>
                <div style={{ fontSize: '16px' }}>🔵</div>
                <div style={{ fontSize: '16px' }}>🟢</div>
                <div style={{ fontSize: '16px' }}>🟡</div>
              </div>
            ),
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: 'Sequências de 5 formas - Para especialistas',
            icon: '🚀',
            preview: (
              <div style={{ display: 'flex', gap: '2px', justifyContent: 'center' }}>
                <div style={{ fontSize: '14px' }}>🔴</div>
                <div style={{ fontSize: '14px' }}>🔵</div>
                <div style={{ fontSize: '14px' }}>🟢</div>
                <div style={{ fontSize: '14px' }}>🟡</div>
                <div style={{ fontSize: '14px' }}>🟣</div>
              </div>
            ),
          },
        ]}
        customContent={
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '1.5rem',
            marginBottom: '1rem',
          }}>
            <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
              🧠 Este jogo desenvolve:
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
              gap: '1rem',
            }}>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>👁️</div>
                <div>Memória Visual</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔄</div>
                <div>Sequenciação</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎯</div>
                <div>Concentração</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧩</div>
                <div>Padrões Lógicos</div>
              </div>
            </div>
          </div>
        }
        onStart={(difficulty) => startGame(difficulty)}
      />
    );
  }

  // Se ainda não iniciou, mostra a tela de início
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Padrões Visuais"
        gameSubtitle="Desenvolva sua memória visual e sequencial"
        gameInstruction="Observe a sequência de formas coloridas e reproduza-a na ordem correta. Teste sua memória visual!"
        gameIcon="🎯"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: 'Sequências de 3 formas - Ideal para iniciantes',
            icon: '😊',
            preview: (
              <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
                <div style={{ fontSize: '20px' }}>🔴</div>
                <div style={{ fontSize: '20px' }}>🔵</div>
                <div style={{ fontSize: '20px' }}>🟢</div>
              </div>
            )
          },
          {
            id: 'medium',
            name: 'Médio',
            description: 'Sequências de 4 formas - Desafio equilibrado',
            icon: '🎯',
            preview: (
              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center' }}>
                <div style={{ fontSize: '16px' }}>🔴</div>
                <div style={{ fontSize: '16px' }}>🔵</div>
                <div style={{ fontSize: '16px' }}>🟢</div>
                <div style={{ fontSize: '16px' }}>🟡</div>
              </div>
            )
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: 'Sequências de 5 formas - Para especialistas',
            icon: '🚀',
            preview: (
              <div style={{ display: 'flex', gap: '2px', justifyContent: 'center' }}>
                <div style={{ fontSize: '14px' }}>🔴</div>
                <div style={{ fontSize: '14px' }}>🔵</div>
                <div style={{ fontSize: '14px' }}>🟢</div>
                <div style={{ fontSize: '14px' }}>🟡</div>
                <div style={{ fontSize: '14px' }}>🟣</div>
              </div>
            )
          }
        ]}
        customContent={
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '1.5rem',
            marginBottom: '1rem'
          }}>
            <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
              🧠 Este jogo desenvolve:
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>👁️</div>
                <div>Memória Visual</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔄</div>
                <div>Sequenciação</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎯</div>
                <div>Concentração</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧩</div>
                <div>Padrões Lógicos</div>
              </div>
            </div>
          </div>
        }
        onStart={(difficulty) => startGame(difficulty)}
      />
    );
  }

  // Layout igual ao ContagemNumeros (harmonioso e limpo)
  return (
    <div className={styles.padroesVisuaisGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🎯 Padrões Visuais
            <div className={styles.activitySubtitle}>
              Reconheça e complete padrões visuais
            </div>
          </h1>          
          <button 
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            aria-label={ttsActive ? "Desativar TTS" : "Ativar TTS"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

      {/* Stats igual ao ContagemNumeros */}
      <div className={styles.stats}>
        <div className={styles.statItem}>
          <div className={styles.statValue}>{currentLevel}</div>
          <div className={styles.statLabel}>Nível</div>
        </div>
        <div className={styles.statItem}>
          <div className={styles.statValue}>{gameStats.score}</div>
          <div className={styles.statLabel}>Pontos</div>
        </div>
        <div className={styles.statItem}>
          <div className={styles.statValue}>{gameStats.streak}</div>
          <div className={styles.statLabel}>Sequência</div>
        </div>
        <div className={styles.statItem}>
          <div className={styles.statValue}>{getAccuracy()}%</div>
          <div className={styles.statLabel}>Precisão</div>
        </div>
      </div>

      {/* Activity selector igual ao ContagemNumeros */}
      <div className={styles.activitySelector}>
        <h3>🎮 Escolha a Atividade:</h3>
        <div className={styles.activityButtons}>
          {Object.entries(ACTIVITY_CONFIG).map(([key, config], index) => (
            <button
              key={key}
              className={`${styles.activityBtn} ${currentActivity === index ? styles.active : ''}`}
              onClick={() => initializeActivity(index)}
            >
              {config.icon} {config.name}
            </button>
          ))}
        </div>
      </div>

      {/* Game area igual ao ContagemNumeros */}
      <div className={styles.gameArea}>
        {renderEnhancedActivityInterface()}
      </div>

      {/* Game controls igual ao ContagemNumeros */}
      <div className={styles.gameControls}>
        <button 
          className={`${styles.controlButton} ${styles.secondary}`}
          onClick={() => clearPlayerSequence()}
          disabled={isShowingSequence}
        >
          🔄 Limpar
        </button>
        
        <button 
          className={`${styles.controlButton} ${styles.primary}`}
          onClick={() => {
            if (gameSequence.length > 0) {
              showSequence();
            } else {
              generateNewLevel();
            }
          }}
          disabled={isShowingSequence}
        >
          {gameSequence.length > 0 ? '👁️ Ver Sequência' : '🎯 Nova Sequência'}
        </button>
        
        <button 
          className={`${styles.controlButton} ${styles.hint}`}
          onClick={giveHint}
          disabled={isShowingSequence}
        >
          💡 Dica
        </button>
      </div>

      {/* TTS controls igual ao ContagemNumeros */}
      <div className={styles.accessibilityControls}>
        <button 
          className={styles.accessibilityButton}
          onClick={explainGame}
        >
          📖 Explicar Jogo
        </button>
        <button 
          className={styles.accessibilityButton}
          onClick={repeatInstruction}
        >
          🔄 Repetir Instrução
        </button>
        <button 
          className={styles.accessibilityButton}
          onClick={announceStats}
        >
          📊 Ler Estatísticas
        </button>
        <button 
          className={styles.accessibilityButton}
          onClick={announceSequence}
        >
          🎵 Descrever Sequência
        </button>
      </div>

      {/* Feedback igual ao ContagemNumeros */}
      {feedback && (
        <div className={`${styles.feedback} ${feedback.type === 'success' ? styles.correct : styles.incorrect}`}>
          <div className={styles.feedbackContent}>
            <div className={styles.feedbackMessage}>{feedback.message}</div>
            {feedback.score && (
              <div className={styles.feedbackScore}>+{feedback.score} pontos!</div>
            )}
          </div>
        </div>
      )}

      {/* Status display igual ao ContagemNumeros */}
      {isShowingSequence && (
        <div className={styles.gameStatus}>
          <div className={styles.statusIcon}>👁️</div>
          <div className={styles.statusText}>
            Memorize a sequência de {gameSequence.length} formas
          </div>
          {countdown && <div className={styles.countdown}>{countdown}s restantes</div>}
        </div>
      )}

      {isPlayerTurn && (
        <div className={styles.gameStatus}>
          <div className={styles.statusIcon}>🎯</div>
          <div className={styles.statusText}>
            Recrie a sequência na ordem correta
          </div>
        </div>
      )}
      </div>
    </div>
  );
}

export default PadroesVisuaisGame;
