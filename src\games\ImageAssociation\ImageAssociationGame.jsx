/**
 * 🖼️ IMAGE ASSOCIATION V3 - JOGO DE ASSOCIAÇÃO COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { ImageAssociationConfig } from './ImageAssociationConfig';
import { ImageAssociationMetrics } from './ImageAssociationMetrics';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
// Hook específico para análise terapêutica
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🖼️ Importar coletores avançados de associação
import { ImageAssociationCollectorsHub } from './collectors/index.js';
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Importa estilos modulares
import styles from './ImageAssociation.module.css';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - IMAGE ASSOCIATION
const ACTIVITY_TYPES = {
  BASIC_ASSOCIATION: {
    id: 'basic_association',
    name: 'Associação Básica',
    icon: '🔗',
    description: 'Associe imagens relacionadas',
    component: 'BasicAssociationActivity'
  },
  CATEGORY_MATCHING: {
    id: 'category_matching',
    name: 'Categorização',
    icon: '📂',
    description: 'Agrupe imagens por categoria',
    component: 'CategoryMatchingActivity'
  },
  SEQUENCE_ASSOCIATION: {
    id: 'sequence_association',
    name: 'Sequência Lógica',
    icon: '📝',
    description: 'Complete sequências de imagens',
    component: 'SequenceAssociationActivity'
  },
  EMOTION_RECOGNITION: {
    id: 'emotion_recognition',
    name: 'Reconhecimento de Emoções',
    icon: '😊',
    description: 'Identifique emoções em faces',
    component: 'EmotionRecognitionActivity'
  },
  CONTEXT_ASSOCIATION: {
    id: 'context_association',
    name: 'Associação Contextual',
    icon: '🏠',
    description: 'Relacione objetos aos seus contextos',
    component: 'ContextAssociationActivity'
  },
  MEMORY_ASSOCIATION: {
    id: 'memory_association',
    name: 'Memória Associativa',
    icon: '🧠',
    description: 'Lembre-se de pares de imagens',
    component: 'MemoryAssociationActivity'
  }
};

const ImageAssociationGame = ({ onBack }) => {
  // 🏠 Contexto do sistema
  const { user } = useContext(SystemContext);

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.BASIC_ASSOCIATION.id,
    activityCycle: [
      ACTIVITY_TYPES.BASIC_ASSOCIATION.id,
      ACTIVITY_TYPES.CATEGORY_MATCHING.id,
      ACTIVITY_TYPES.SEQUENCE_ASSOCIATION.id,
      ACTIVITY_TYPES.EMOTION_RECOGNITION.id,
      ACTIVITY_TYPES.CONTEXT_ASSOCIATION.id,
      ACTIVITY_TYPES.MEMORY_ASSOCIATION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      basicAssociation: {
        currentPair: null,
        options: [],
        selectedOption: null
      },
      categoryMatching: {
        categories: [],
        currentCategory: null,
        placedItems: []
      },
      sequenceAssociation: {
        sequence: [],
        userProgress: [],
        missingIndex: null
      },
      emotionRecognition: {
        currentEmotion: null,
        faceOptions: [],
        selectedFace: null
      },
      contextAssociation: {
        context: null,
        objects: [],
        correctObjects: []
      },
      memoryAssociation: {
        pairs: [],
        flippedCards: [],
        matchedPairs: []
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null, // 'success', 'error', 'hint'
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  //  Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration('image_association', collectorsHub);

  // 🎯 Hook orquestrador terapêutico
  const {
    initializeSession: initTherapeutic,
    recordActivity: recordTherapeuticActivity,
    generateReport: generateTherapeuticReport,
    getRecommendations: getTherapeuticRecommendations,
    finalizeSession: finalizeTherapeutic
  } = useTherapeuticOrchestrator();

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('image_association');

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => !prev);
    if (!ttsActive) {
      speak('TTS ativado');
    }
  }, [ttsActive, speak]);

  // Função para falar texto usando TTS (compatibilidade)
  const speakOld = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo no jogo
    if (!ttsActive) {
      console.log('🔇 TTS desativado - não reproduzindo áudio');
      return;
    }
    
    if (!ttsEnabled || !('speechSynthesis' in window)) {
      console.warn('Text-to-Speech não está habilitado ou não é suportado neste navegador');
      return;
    }
    
    // Cancelar qualquer fala em andamento
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Configurações padrão com opções personalizáveis
    utterance.lang = options.lang || 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    // Marcar que está falando
    setIsSpeaking(true);
    if (options.buttonId) {
      setCurrentSpeechButton(options.buttonId);
    }
    
    // Callback quando iniciar
    utterance.onstart = () => {
      setIsSpeaking(true);
    };
    
    // Callback para quando terminar de falar
    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
      if (options.onEnd) {
        options.onEnd();
      }
    };
    
    // Callback para erros
    utterance.onerror = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    };
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive, ttsEnabled]);
  
  // Função para explicar o jogo
  const explainGame = useCallback(() => {
    const explanation = `
      Bem-vindo ao jogo Associação de Imagens! 
      Este é um jogo onde você precisa conectar imagens que fazem sentido juntas.
      
      Como jogar:
      Observe atentamente a imagem da esquerda.
      Depois, escolha qual das opções à direita combina melhor com ela.
      Use sua lógica e conhecimento para fazer as associações corretas.
      
      Quanto mais associações corretas você fizer, mais pontos ganhará!
      
      Boa sorte e divirta-se desenvolvendo sua capacidade de associação!
    `;
    
    speak(explanation, {
      rate: 0.8,
      buttonId: 'explain',
      onEnd: () => console.log('Explicação do jogo concluída')
    });
  }, [speak]);

  // 🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS

  // Inicializar métricas
  useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = new ImageAssociationMetrics();
    }
  }, []);

  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'image_association',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // Inicializar sessões dos hooks
      await initMultisensory();
      await initTherapeutic();

      // Gerar primeira atividade
      generateNewRound();

      speak('Jogo iniciado! Vamos começar com associação de imagens.');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, initMultisensory, initTherapeutic, user, speak]);

  // Função para trocar atividade
  const changeActivity = useCallback((activityId) => {
    if (gameState.activityRoundCount < 4 && gameState.currentActivity !== activityId) {
      speak(`Complete mais ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro.`);
      return;
    }

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      activityRoundCount: 0,
      activityIndex: prev.activityCycle.indexOf(activityId)
    }));

    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);

    generateNewRound();
  }, [gameState.activityRoundCount, gameState.currentActivity, speak]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback(() => {
    const currentActivity = gameState.currentActivity;

    setGameState(prev => ({
      ...prev,
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ''
    }));

    // Gerar dados específicos da atividade
    switch (currentActivity) {
      case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
        generateBasicAssociation();
        break;
      case ACTIVITY_TYPES.CATEGORY_MATCHING.id:
        generateCategoryMatching();
        break;
      case ACTIVITY_TYPES.SEQUENCE_ASSOCIATION.id:
        generateSequenceAssociation();
        break;
      case ACTIVITY_TYPES.EMOTION_RECOGNITION.id:
        generateEmotionRecognition();
        break;
      case ACTIVITY_TYPES.CONTEXT_ASSOCIATION.id:
        generateContextAssociation();
        break;
      case ACTIVITY_TYPES.MEMORY_ASSOCIATION.id:
        generateMemoryAssociation();
        break;
      default:
        generateBasicAssociation();
    }
  }, [gameState.currentActivity]);

  // 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES

  // Gerar associação básica
  const generateBasicAssociation = useCallback(() => {
    const associations = [
      { main: '🐶', options: ['🦴', '🐱', '🚗'], correct: 0, explanation: 'Cachorro come osso' },
      { main: '🌧️', options: ['☀️', '☂️', '🏠'], correct: 1, explanation: 'Chuva precisa de guarda-chuva' },
      { main: '🔑', options: ['🚪', '🍎', '📱'], correct: 0, explanation: 'Chave abre porta' },
      { main: '🍯', options: ['🐝', '🐟', '🐸'], correct: 0, explanation: 'Abelha faz mel' }
    ];

    const randomAssociation = associations[Math.floor(Math.random() * associations.length)];

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        basicAssociation: {
          currentPair: randomAssociation,
          options: randomAssociation.options,
          selectedOption: null
        }
      }
    }));
  }, []);

  // Gerar categorização
  const generateCategoryMatching = useCallback(() => {
    const categories = [
      { name: 'Animais', items: ['🐶', '🐱', '🐸', '🦋'], color: '#FF6B6B' },
      { name: 'Frutas', items: ['🍎', '🍌', '🍊', '🍇'], color: '#4ECDC4' },
      { name: 'Veículos', items: ['🚗', '🚲', '✈️', '🚢'], color: '#45B7D1' }
    ];

    const selectedCategory = categories[Math.floor(Math.random() * categories.length)];
    const mixedItems = [...selectedCategory.items, '🏠', '📱', '⚽'].sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        categoryMatching: {
          categories: [selectedCategory],
          currentCategory: selectedCategory,
          placedItems: [],
          availableItems: mixedItems
        }
      }
    }));
  }, []);

  // Gerar sequência de associação
  const generateSequenceAssociation = useCallback(() => {
    const sequences = [
      { sequence: ['🌱', '🌿', '🌳'], missing: 1, explanation: 'Crescimento da planta' },
      { sequence: ['🥚', '🐣', '🐤'], missing: 2, explanation: 'Nascimento do pintinho' },
      { sequence: ['☁️', '🌧️', '🌈'], missing: 1, explanation: 'Depois da chuva vem o arco-íris' }
    ];

    const selectedSequence = sequences[Math.floor(Math.random() * sequences.length)];

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        sequenceAssociation: {
          sequence: selectedSequence.sequence,
          userProgress: selectedSequence.sequence.map((item, index) =>
            index === selectedSequence.missing ? null : item
          ),
          missingIndex: selectedSequence.missing
        }
      }
    }));
  }, []);

  // Gerar reconhecimento de emoções
  const generateEmotionRecognition = useCallback(() => {
    const emotions = [
      { emotion: 'feliz', face: '😊', options: ['😊', '😢', '😠'], correct: 0 },
      { emotion: 'triste', face: '😢', options: ['😊', '😢', '😠'], correct: 1 },
      { emotion: 'bravo', face: '😠', options: ['😊', '😢', '😠'], correct: 2 }
    ];

    const selectedEmotion = emotions[Math.floor(Math.random() * emotions.length)];

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        emotionRecognition: {
          currentEmotion: selectedEmotion.emotion,
          faceOptions: selectedEmotion.options,
          selectedFace: null,
          correctIndex: selectedEmotion.correct
        }
      }
    }));
  }, []);

  // Gerar associação contextual
  const generateContextAssociation = useCallback(() => {
    const contexts = [
      { context: '🏠 Casa', objects: ['🛏️', '🚗', '📺', '🌳'], correct: [0, 2] },
      { context: '🏫 Escola', objects: ['📚', '🍎', '⚽', '🛏️'], correct: [0, 2] },
      { context: '🏥 Hospital', objects: ['💊', '🩺', '🚗', '📱'], correct: [0, 1] }
    ];

    const selectedContext = contexts[Math.floor(Math.random() * contexts.length)];

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        contextAssociation: {
          context: selectedContext.context,
          objects: selectedContext.objects,
          correctObjects: selectedContext.correct,
          selectedObjects: []
        }
      }
    }));
  }, []);

  // Gerar memória associativa
  const generateMemoryAssociation = useCallback(() => {
    const pairs = [
      ['🐶', '🦴'], ['🐱', '🐟'], ['🐝', '🍯'], ['🔑', '🚪']
    ];

    const shuffledPairs = pairs.sort(() => Math.random() - 0.5).slice(0, 3);
    const cards = shuffledPairs.flat().sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        memoryAssociation: {
          pairs: shuffledPairs,
          cards: cards.map((card, index) => ({ id: index, content: card, flipped: false, matched: false })),
          flippedCards: [],
          matchedPairs: []
        }
      }
    }));
  }, []);

  // 🖼️ Inicializar coletores avançados de associação
  // 🖼️ Buscar instância dos coletores do sistema (evita múltiplas instanciações)
  const [collectorsHub] = useState(() => {
    // Tentar obter instância do sistema global primeiro
    if (window.globalSystemInstance?.gameSpecificProcessors?.gameCollectors?.ImageAssociation?.hub) {
      console.log('🖼️ Reutilizando instância existente do ImageAssociation CollectorsHub');
      return window.globalSystemInstance.gameSpecificProcessors.gameCollectors.ImageAssociation.hub;
    }
    // Fallback: criar nova instância apenas se necessário
    console.log('🖼️ Criando nova instância do ImageAssociation CollectorsHub');
    return new ImageAssociationCollectorsHub();
  });
  
  // 🔄 Hook multissensorial integrado
  const multisensoryIntegration = useMultisensoryIntegration('image-association', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info'
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    gameType: 'image-association',
    collectorsHub,
    multisensoryIntegration,
    autoUpdate: true,
    logLevel: 'info'
  });

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // Calcular precisão
  const getAccuracy = useCallback(() => {
    return attempts > 0 ? Math.round((successes / attempts) * 100) : 0
  }, [attempts, successes])

  // 🖼️ Inicializar sessão com coletores
  const initializeSession = useCallback(async () => {
    try {
      if (collectorsHub && !collectorsHub.initialized) {
        await collectorsHub.initialize()
        console.log('🎯 ImageAssociation: Coletores inicializados')
      }
    } catch (error) {
      console.warn('⚠️ ImageAssociation: Erro ao inicializar coletores:', error)
    }
  }, [collectorsHub])

  // 🖼️ Registrar interação de associação
  const recordAssociationInteraction = useCallback(async (selectedOption, isCorrect, reactionTime) => {
    try {
      if (!collectorsHub || !currentAssociation) return

      const gameData = {
        sessionId: `imageassoc_${Date.now()}`,
        phase: currentPhase,
        category: currentAssociation.category || 'association',
        mainItem: currentAssociation.main,
        correctAnswer: currentAssociation.correct,
        userAnswer: selectedOption,
        isCorrect,
        responseTime: reactionTime,
        difficulty: difficulty.toLowerCase(),
        options: currentAssociation.options,
        explanation: currentAssociation.explanation,
        timestamp: Date.now()
      }

      await collectorsHub.collectGameEvent(gameData)
      // 🔄 Registrar interação multissensorial
      await multisensoryIntegration.recordInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: gameData,
        multisensoryProcessing: {
          visualProcessing: { visualRecognition: 0.7, visualMemory: 0.7, visualAttention: 0.7 },
          cognitiveProcessing: { associativeMemory: 0.7, processingSpeed: 0.7, adaptability: 0.7 },
          behavioralProcessing: { interactionCount: 0.7, averageResponseTime: 0.7, consistency: 0.7 }
        }
      });
      console.log('📊 ImageAssociation: Dados coletados:', gameData)
      
    } catch (error) {
      console.warn('⚠️ ImageAssociation: Erro ao registrar interação:', error)
    }
  }, [collectorsHub, currentAssociation, multisensoryIntegration, difficulty])

  // Carregar fase atual
  const loadCurrentPhase = useCallback(() => {
    // Filtrar associações por dificuldade atual
    const availableAssociations = ImageAssociationConfig.associations.filter(
      a => a.difficulty === difficulty
    );
    
    const association = availableAssociations.find(a => a.phase === currentPhase);
    
    if (association) {
      setCurrentAssociation(association);
      setSelectedOption(null);
      setFeedback(null);
    } else {
      // Se não encontrar a fase, mostrar mensagem de conclusão
      setFeedback({
        type: 'success',
        message: `🎉 Parabéns! Você completou todas as fases da dificuldade ${difficulty}! 🏆`
      });
    }
  }, [difficulty, currentPhase]);
  // Iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    setDifficulty(selectedDifficulty.toUpperCase());
    setGameStarted(true);
    setShowStartScreen(false);
    setScore(0);
    setLevel(1);
    setStars(0);
    setSuccesses(0);
    setAttempts(0);
    setCurrentPhase(1); // Sempre começar da fase 1
    setSelectedOption(null);
    setFeedback(null);
    setGameStartTime(Date.now());

    // 🖼️ Inicializar sessão com coletores
    await initializeSession()

    // 🔄 Inicializar integração multissensorial
    try {
      await multisensoryIntegration.initializeSession(`session_${Date.now()}`, {
        difficulty: selectedDifficulty,
        gameMode: 'image_association',
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
    }
    
    // Carregar a primeira associação e anunciar início
    setTimeout(() => {
      loadCurrentPhase();
      
      // Debug: verificar estado do TTS
      console.log(`🔊 Estado TTS no início: ${ttsActive}`);
      
      // Anunciar início do jogo via TTS
      const welcomeMessage = `Jogo iniciado! Dificuldade ${selectedDifficulty}. Prepare-se para encontrar as associações entre imagens.`;
      speak(welcomeMessage, {
        rate: 0.9,
        onEnd: () => console.log('Boas-vindas anunciadas')
      });
    }, 100);
  }, [initializeSession, multisensoryIntegration, user, loadCurrentPhase, speak, ttsActive]);

  // Próxima fase
  const nextPhase = useCallback(() => {
    // Obter associações da dificuldade atual
    const availableAssociations = ImageAssociationConfig.associations.filter(
      a => a.difficulty === difficulty
    );
    
    // Encontrar próxima fase
    const currentIndex = availableAssociations.findIndex(a => a.phase === currentPhase);
    
    if (currentIndex >= 0 && currentIndex < availableAssociations.length - 1) {
      // Há mais fases nesta dificuldade
      const nextAssociation = availableAssociations[currentIndex + 1];
      setCurrentPhase(nextAssociation.phase);
    } else {
      // Completou todas as fases da dificuldade atual
      setFeedback({
        type: 'success',
        message: `🎉 Parabéns! Você completou todas as fases da dificuldade ${difficulty}! 🏆`
      });
      
      // Anunciar conclusão via TTS
      speak(`Parabéns! Você completou todas as fases da dificuldade ${difficulty}! Excelente trabalho!`, {
        rate: 1.0,
        onEnd: () => console.log('Feedback de conclusão anunciado')
      });
      
      // Voltar ao menu após alguns segundos
      setTimeout(() => {
        setShowStartScreen(true);
        setGameStarted(false);
      }, 3000);
      return;
    }
    
    setTimeout(() => {
      loadCurrentPhase();
    }, 100);
  }, [difficulty, currentPhase, speak, loadCurrentPhase]);

  // Lidar com sucesso
  const handleSuccess = useCallback(async () => {
    setSuccesses(prev => prev + 1);
    
    const points = difficulty === 'EASY' ? 10 : difficulty === 'MEDIUM' ? 15 : 20;
    setScore(prev => prev + points);
    
    // Atualizar level e estrelas baseado no score
    const newLevel = Math.floor(score / 100) + 1;
    setLevel(newLevel);
    setStars(Math.min(3, Math.floor(getAccuracy() / 30)));

    // Mensagem de sucesso
    const successMessages = [
      "Muito bem! Excelente associação! 🎉",
      "Perfeito! Você entendeu a relação! ⭐",
      "Fantástico! Continue assim! 🚀",
      "Brilhante! Sua lógica está ótima! 💡"
    ];
    
    const message = successMessages[Math.floor(Math.random() * successMessages.length)];
    
    setFeedback({
      type: 'success',
      message: `${message} +${points} pontos!`
    });

    // Anunciar sucesso via TTS
    speak(`${message} Você ganhou ${points} pontos!`, {
      rate: 1.1,
      onEnd: () => console.log('Feedback de sucesso anunciado')
    });

    // Aguardar um pouco e carregar próxima fase automaticamente
    setTimeout(() => {
      nextPhase();
    }, 2000);
  }, [difficulty, score, getAccuracy, speak, nextPhase])

  // Lidar com erro
  const handleError = useCallback(() => {
    setFeedback({
      type: 'error',
      message: 'Pense na relação entre os elementos! Tente novamente! 🤔',
    })

    // Anunciar erro via TTS
    speak('Ops! Pense na relação entre os elementos. Tente novamente!', {
      rate: 0.9,
      onEnd: () => console.log('Feedback de erro anunciado')
    });

    // Limpar seleção após feedback
    setTimeout(() => {
      setSelectedOption(null);
      setFeedback(null);
    }, 2000);
  }, [speak]);

  const handleOptionSelect = useCallback(async (option) => {
    if (selectedOption) return

    const startTime = Date.now()
    setSelectedOption(option)
    setAttempts(prev => prev + 1)

    const isCorrect = option.emoji === currentAssociation.correct.emoji
    const reactionTime = Date.now() - startTime

    // 🖼️ Registrar com coletores avançados
    await recordAssociationInteraction(option, isCorrect, reactionTime)

    if (isCorrect) {
      setTimeout(() => {
        handleSuccess()
      }, 1000)
    } else {
      handleError()
    }
  }, [selectedOption, currentAssociation, recordAssociationInteraction, handleSuccess, handleError]);

  // Função para reiniciar o jogo
  const restartGame = useCallback(() => {
    setShowStartScreen(true);
    setGameStarted(false);
    setCurrentAssociation(null);
    setSelectedOption(null);
    setFeedback(null);
    setScore(0);
    setLevel(1);
    setStars(0);
    setSuccesses(0);
    setAttempts(0);
    setCurrentPhase(1);
    setAttemptCount(0);
    setCognitiveAnalysisVisible(false);
    setAnalysisResults(null);
  }, []);

  // Efeito para carregar fase quando currentPhase muda
  useEffect(() => {
    if (gameStarted && currentPhase) {
      loadCurrentPhase();
    }
  }, [currentPhase, gameStarted, difficulty, loadCurrentPhase]);

  // Mover finalizeMultisensorySession para useCallback
  const finalizeMultisensorySession = useCallback(async () => {
    try {
      const multisensoryReport = await multisensoryIntegration.finalizeSession({
        finalScore: score,
        finalAccuracy: attempts > 0 ? successes / attempts : 0,
        totalInteractions: attempts,
        sessionDuration: Date.now() - gameStartTime,
        difficulty: difficulty
      });
      console.log('🔄 ImageAssociation: Relatório multissensorial final:', multisensoryReport);
    } catch (error) {
      console.warn('⚠️ ImageAssociation: Erro ao finalizar sessão multissensorial:', error);
    }
  }, [multisensoryIntegration, score, successes, attempts, gameStartTime, difficulty]);

  // 🔄 Detectar quando o jogo é completado e finalizar sessão multissensorial
  useEffect(() => {
    if (gameStarted && level > 10) { // Assumindo que 10 é o nível máximo
      finalizeMultisensorySession();
    }
  }, [gameStarted, level, finalizeMultisensorySession]);

  // Cleanup do TTS quando componente é desmontado
  useEffect(() => {
    return () => {
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);  

  // Função para toggle do TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      console.log(`🔊 TTS ${newState ? 'ativado' : 'desativado'}`);
      
      // Se desativando, parar qualquer fala em andamento
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
      
      // Salvar preferência no localStorage
      localStorage.setItem('imageAssociationTTS', newState.toString());
      
      return newState;
    });
  }, []);

  // Carregar preferência do TTS do localStorage
  useEffect(() => {
    const savedTTSState = localStorage.getItem('imageAssociationTTS');
    if (savedTTSState !== null) {
      setTtsActive(savedTTSState === 'true');
    }
  }, []);

  // Cleanup do TTS quando componente for desmontado
  useEffect(() => {
    return () => {
      // Parar qualquer TTS em andamento ao sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      console.log('🔇 TTS parado ao sair do jogo Associação de Imagens');
    };
  }, []);

  // Cleanup global para evitar TTS vazando entre jogos
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
    };

    const handlePageHide = () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('pagehide', handlePageHide);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('pagehide', handlePageHide);
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // Se ainda não iniciou, mostra a tela de início
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Associação de Imagens"
        gameSubtitle="Conecte ideias e descubra relações"
        gameInstruction="Encontre a relação correta entre imagens e conceitos. Observe bem as pistas e escolha a opção que melhor se associa!"
        gameIcon="🔗"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: 'Associações básicas - Ideal para iniciantes',
            icon: '😊',
            preview: (
              <div style={{ display: 'flex', gap: '4px', justifyContent: 'center', alignItems: 'center' }}>
                <span style={{ fontSize: '16px' }}>�</span>
                <span style={{ fontSize: '12px' }}>↔️</span>
                <span style={{ fontSize: '16px' }}>🥛</span>
              </div>
            )
          },
          {
            id: 'medium',
            name: 'Médio',
            description: 'Relações funcionais - Desafio equilibrado',
            icon: '🎯',
            preview: (
              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center', alignItems: 'center' }}>
                <span style={{ fontSize: '14px' }}>⚡</span>
                <span style={{ fontSize: '10px' }}>↔️</span>
                <span style={{ fontSize: '14px' }}>💡</span>
                <span style={{ fontSize: '10px' }}>↔️</span>
                <span style={ { fontSize: '14px' }}>🏠</span>
              </div>
            )
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: 'Associações abstratas - Para especialistas',
            icon: '🚀',
            preview: (
              <div style={{ display: 'flex', gap: '2px', justifyContent: 'center', alignItems: 'center', flexWrap: 'wrap' }}>
                <span style={{ fontSize: '12px' }}>🎭</span>
                <span style={{ fontSize: '8px' }}>↔️</span>
                <span style={{ fontSize: '12px' }}>💭</span>
                <span style={{ fontSize: '8px' }}>↔️</span>
                <span style={{ fontSize: '12px' }}>🌟</span>
              </div>
            )
          }
        ]}
        onStart={(difficulty) => startGame(difficulty)}
        customContent={
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '1.5rem',
            marginBottom: '1rem'
          }}>
            <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
              🧠 Este jogo desenvolve:
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔗</div>
                <div>Associação de Ideias</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧩</div>
                <div>Raciocínio Lógico</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎯</div>
                <div>Concentração</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>💡</div>
                <div>Pensamento Criativo</div>
              </div>
            </div>
          </div>
        }
      />
    );
  }  return (
    <div 
      className={`${styles.imageAssociationGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      <div className={styles.gameContent}>
        {/* Header do jogo */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🔗 Associação de Imagens
            <div className={styles.activitySubtitle}>
              Conecte imagens relacionadas e desenvolva o raciocínio
            </div>
          </h1>
          <button 
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            aria-label={ttsActive ? "Desativar Text-to-Speech" : "Ativar Text-to-Speech"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* 🎯 SISTEMA DE ATIVIDADES - PADRÃO MEMORYGAME EXATO */}
        {gameState.status === 'playing' && (
          <div className={styles.activityMenu}>
            {Object.values(ACTIVITY_TYPES).map((activity) => {
              const isCurrentActivity = gameState.currentActivity === activity.id;
              const canSwitch = gameState.activityRoundCount >= 4 || isCurrentActivity;

              return (
                <button
                  key={activity.id}
                  className={`${styles.activityButton} ${
                    isCurrentActivity ? styles.active : ''
                  }`}
                  onClick={() => changeActivity(activity.id)}
                  title={
                    !canSwitch
                      ? `Complete ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro`
                      : activity.description
                  }
                  disabled={!canSwitch}
                >
                  <span className={styles.activityIcon}>{activity.icon}</span>
                  <span className={styles.activityName}>{activity.name}</span>
                  {isCurrentActivity && (
                    <span className={styles.activeIndicator}>●</span>
                  )}
                </button>
              );
            })}
          </div>
        )}

        {/* Estatísticas padronizadas */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.accuracy}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.activityRoundCount}/4+</div>
            <div className={styles.statLabel}>Progresso</div>
          </div>
        </div>

        {/* Área da pergunta */}
        {currentAssociation && (
          <div className={styles.questionArea}>
            <div className={styles.questionHeader}>
              <h2 className={styles.questionTitle}>
                O que se relaciona com {currentAssociation.main.label}?
              </h2>
            </div>

            {/* Item principal */}
            <div className={styles.mainItem}>
              <div className={styles.mainEmoji}>{currentAssociation.main.emoji}</div>
              <div className={styles.mainLabel}>{currentAssociation.main.label}</div>
            </div>

            {/* Seta de associação */}
            <div className={styles.associationArrow}>
              <span>🔽</span>
              <div className={styles.arrowText}>se relaciona com</div>
            </div>

            {/* Grid de opções */}
            <div className={styles.optionsGrid}>
              {currentAssociation.options.map((option, index) => (
                <button
                  key={index}
                  className={`${styles.optionButton} ${
                    selectedOption === option
                      ? option.emoji === currentAssociation.correct.emoji
                        ? styles.correct
                        : styles.incorrect
                      : ''
                  }`}
                  onClick={() => handleOptionSelect(option)}
                  disabled={selectedOption !== null}
                >
                  <div className={styles.optionEmoji}>{option.emoji}</div>
                  <div className={styles.optionLabel}>{option.label}</div>
                  
                  {/* Ícone de feedback */}
                  {selectedOption === option && (
                    <div className={`${styles.feedbackIcon} ${
                      option.emoji === currentAssociation.correct.emoji ? styles.correct : styles.incorrect
                    }`}>
                      {option.emoji === currentAssociation.correct.emoji ? '✓' : '✗'}
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Mensagem de feedback */}
        {feedback && (
          <div className={`${styles.feedbackMessage} ${styles[feedback.type]}`}>
            {feedback.message}
          </div>
        )}

        {/* Indicador de Status do TTS */}
        {!ttsActive && (
          <div className={styles.ttsStatusIndicator}>
            🔇 Áudio desativado - Clique no botão TTS para ativar
          </div>
        )}

        {/* Botões TTS para instruções */}
        <div className={styles.instructionsTTS}>
          <button 
            className={`${styles.ttsButton} ${styles.ttsExplain} ${currentSpeechButton === 'explain' ? styles.speaking : ''}`}
            onClick={explainGame}
            aria-label="Ouvir explicação do jogo usando áudio"
            title="Explicar jogo"
            disabled={!ttsActive || (isSpeaking && currentSpeechButton !== 'explain')}
          >
            <span className={styles.ttsIcon}>❓</span>
            <span className={styles.ttsLabel}>Explicar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageAssociationGame
