/**
 * @file AdminPanel.jsx
 * @description Painel administrativo do Portal Betina - Wrapper para AdminDashboard
 * @version 3.0.0
 */

import React from 'react'
import AdminDashboard from '../../admin/AdminDashboard/AdminDashboard'

function AdminPanel({ onBack }) {
  // Usar o novo AdminDashboard que já tem autenticação integrada
  return <AdminDashboard onBack={onBack} />
}

export default AdminPanel