/**
 * @file SystemHealthMonitor.jsx
 * @description Monitor de Saúde do Sistema - Área Administrativa
 * @version 1.0.0
 * @admin true
 */

import React, { useState, useEffect } from 'react'
import styles from './SystemHealthMonitor.module.css'

const SystemHealthMonitor = () => {
  const [healthData, setHealthData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState(new Date())

  // Simular dados de health check
  const loadHealthData = async () => {
    try {
      // Em produção, fazer fetch para /api/health
      const mockHealthData = {
        overall: 'healthy',
        components: {
          system_orchestrator: {
            status: 'healthy',
            metrics: {
              state: 'running',
              activeSessions: Math.floor(Math.random() * 10) + 1,
              totalProcessed: Math.floor(Math.random() * 1000) + 500,
              uptime: Date.now() - (Math.random() * 3600000)
            }
          },
          ai_brain: {
            status: 'healthy',
            metrics: {
              mode: 'supremo',
              processorsActive: 5,
              lastAnalysis: Date.now() - (Math.random() * 300000)
            }
          },
          intelligent_cache: {
            status: 'healthy',
            metrics: {
              hitRate: (0.8 + Math.random() * 0.15).toFixed(3),
              size: Math.floor(Math.random() * 500) + 100,
              maxSize: 1000,
              hits: Math.floor(Math.random() * 1000) + 500,
              misses: Math.floor(Math.random() * 200) + 50
            }
          },
          database: {
            status: 'healthy',
            metrics: {
              connectionActive: true,
              lastQuery: Date.now(),
              queryCount: Math.floor(Math.random() * 5000) + 1000
            }
          },
          behavioral_analyzer: {
            status: 'healthy',
            metrics: {
              analysesPerformed: Math.floor(Math.random() * 100) + 50,
              patternsDetected: Math.floor(Math.random() * 20) + 10,
              lastAnalysis: Date.now() - (Math.random() * 600000)
            }
          },
          cognitive_analyzer: {
            status: 'healthy',
            metrics: {
              cognitiveAssessments: Math.floor(Math.random() * 80) + 30,
              domainsAnalyzed: 4,
              lastAssessment: Date.now() - (Math.random() * 400000)
            }
          }
        }
      }

      setHealthData(mockHealthData)
      setLastUpdate(new Date())
    } catch (error) {
      console.error('Erro ao carregar dados de saúde:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadHealthData()
    const interval = setInterval(loadHealthData, 30000) // Atualizar a cada 30s
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return '#4CAF50'
      case 'warning': return '#FF9800'
      case 'unhealthy': return '#F44336'
      default: return '#9E9E9E'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return '✅'
      case 'warning': return '⚠️'
      case 'unhealthy': return '❌'
      default: return '❓'
    }
  }

  const formatUptime = (uptime) => {
    const hours = Math.floor(uptime / 3600000)
    const minutes = Math.floor((uptime % 3600000) / 60000)
    return `${hours}h ${minutes}m`
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando dados de saúde do sistema...</p>
      </div>
    )
  }

  return (
    <div className={styles.healthMonitor}>
      {/* Components Grid */}
      <div className={styles.componentsGrid}>
        {Object.entries(healthData.components).map(([name, component]) => (
          <div key={name} className={styles.componentCard}>
            <div className={styles.componentHeader}>
              <span className={styles.componentIcon}>
                {getStatusIcon(component.status)}
              </span>
              <h3 className={styles.componentName}>
                {name.replace(/_/g, ' ').toUpperCase()}
              </h3>
              <span 
                className={styles.componentStatus}
                style={{ color: getStatusColor(component.status) }}
              >
                {component.status}
              </span>
            </div>

            <div className={styles.componentMetrics}>
              {Object.entries(component.metrics).map(([key, value]) => (
                <div key={key} className={styles.metric}>
                  <span className={styles.metricLabel}>
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                  </span>
                  <span className={styles.metricValue}>
                    {typeof value === 'number' && key.includes('Time') 
                      ? formatUptime(Date.now() - value)
                      : typeof value === 'boolean'
                      ? value ? '✅' : '❌'
                      : value
                    }
                  </span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* System Metrics Summary */}
      <div className={styles.summarySection}>
        <h3>📊 Resumo do Sistema</h3>
        <div className={styles.summaryGrid}>
          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>🖥️</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                {Object.keys(healthData.components).length}
              </div>
              <div className={styles.summaryLabel}>Componentes</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>✅</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                {Object.values(healthData.components).filter(c => c.status === 'healthy').length}
              </div>
              <div className={styles.summaryLabel}>Saudáveis</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>⚠️</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                {Object.values(healthData.components).filter(c => c.status === 'warning').length}
              </div>
              <div className={styles.summaryLabel}>Avisos</div>
            </div>
          </div>

          <div className={styles.summaryCard}>
            <div className={styles.summaryIcon}>❌</div>
            <div className={styles.summaryContent}>
              <div className={styles.summaryValue}>
                {Object.values(healthData.components).filter(c => c.status === 'unhealthy').length}
              </div>
              <div className={styles.summaryLabel}>Problemas</div>
            </div>
          </div>
        </div>
      </div>

      {/* Cache Performance */}
      <div className={styles.cacheSection}>
        <h3>💾 Performance do Cache</h3>
        <div className={styles.cacheMetrics}>
          {healthData.components.intelligent_cache && (
            <div className={styles.cacheCard}>
              <div className={styles.cacheHeader}>
                <span>Cache Inteligente</span>
                <span className={styles.hitRate}>
                  Hit Rate: {healthData.components.intelligent_cache.metrics.hitRate}
                </span>
              </div>
              <div className={styles.cacheBar}>
                <div 
                  className={styles.cacheProgress}
                  style={{ 
                    width: `${parseFloat(healthData.components.intelligent_cache.metrics.hitRate) * 100}%` 
                  }}
                ></div>
              </div>
              <div className={styles.cacheStats}>
                <span>Hits: {healthData.components.intelligent_cache.metrics.hits}</span>
                <span>Misses: {healthData.components.intelligent_cache.metrics.misses}</span>
                <span>Size: {healthData.components.intelligent_cache.metrics.size}/{healthData.components.intelligent_cache.metrics.maxSize}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export { SystemHealthMonitor }
export default SystemHealthMonitor
