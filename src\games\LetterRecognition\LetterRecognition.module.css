/**
 * @file LetterRecognition.module.css
 * @description Estilos modulares para o Jogo de Reconhecimento de Letras
 * @version 3.0.0 - Mobile-First & MemoryGame UI Patterns
 */

/* CSS Variables para consistência */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-blur: blur(10px);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
}

/* Container principal do jogo */
.letterRecognitionGame {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo - PADRÃO MEMORYGAME */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botão TTS no header - PADRÃO MEMORYGAME */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

/* Estados do toggle TTS - PADRÃO MEMORYGAME */
.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Header de estatísticas - PADRÃO MEMORYGAME */
.statsHeader {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.statItem {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  color: white;
}

.statValue {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* Seletor de atividades - PADRÃO MEMORYGAME Mobile-First */
.activityMenu {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.activityButton.active {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

/* Elementos de atividade - seguindo padrão MemoryGame */
.activityIcon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.activityName {
  flex: 1;
  text-align: left;
}

.activeIndicator {
  color: #00ff00;
  font-size: 0.8rem;
  margin-left: auto;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}
  margin-left: auto;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.7; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* Área do jogo principal */
.gameArea {
  flex: 1;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 1rem;
}

/* 🔊 Botão para repetir instrução */
.repeatButton {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.repeatButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.repeatButton:active {
  transform: scale(0.95);
}

/* 🔊 Botão TTS pequeno em cada opção de letra */
.letterTtsButton {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.7rem;
  z-index: 5;
}

.letterTtsButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

.letterTtsButton:active {
  transform: scale(0.9);
}

/* Header do jogo (versão anterior) */
.gameHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.backButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.gameTitle {
  font-size: 2rem;
  font-weight: 800;
  text-align: center;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Área da letra */
.letterArea {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
}

.letterDisplay {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.8);
  border: 3px solid #82B1FF;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  min-height: 120px;
  justify-content: center;
  animation: gentle-pulse 3s ease-in-out infinite;
}

@keyframes gentle-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% { 
    transform: scale(1.02);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  }
}

.letterTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
}

.targetLetter {
  font-size: 8rem;
  font-weight: 900;
  color: #FECA57;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.4);
  margin-bottom: 1rem;
  animation: letterPulse 2s ease-in-out infinite;
}

.letterMain {
  font-size: 3.5rem;
  font-weight: 800;
  color: #82B1FF;
  line-height: 1;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.letterSound {
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  opacity: 0.8;
}

.letterExample {
  font-size: 1.125rem;
  font-weight: 500;
  color: #333;
  text-align: center;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes letterPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Grid de opções - MOBILE-FIRST MELHORADO */
.optionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 1rem;
}

/* Opções de letras - MOBILE-FIRST MELHORADO */
.letterOption {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 1.5rem 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  color: #333;
  position: relative;
  touch-action: manipulation; /* Melhora touch em mobile */
}

.letterOption:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.letterOption.selected {
  background: rgba(255, 255, 255, 1);
}

.letterOption.correct {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  animation: correctPulse 0.6s ease-in-out;
}

.letterOption.incorrect {
  border-color: #f44336;
  background: rgba(244, 67, 54, 0.1);
  animation: incorrectShake 0.6s ease-in-out;
}

.optionLetter {
  font-size: 2rem;
  font-weight: 900;
  color: #333;
  margin-bottom: 0.25rem;
  text-shadow: none;
}

.optionName {
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  opacity: 0.8;
}

@keyframes correctPulse {
  0% { transform: translateY(-8px) scale(1.05); }
  50% { transform: translateY(-8px) scale(1.2); }
  100% { transform: translateY(-8px) scale(1.05); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateY(-8px) translateX(0); }
  25% { transform: translateY(-8px) translateX(-10px); }
  75% { transform: translateY(-8px) translateX(10px); }
}

/* Estatísticas no header - COMO NO PREVIEW */
.statsHeader {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 80px;
}

.statValue {
  font-size: 1.25rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.75rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

/* Seção da letra alvo - COMO NO PREVIEW */
.letterTargetSection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  margin-bottom: 2rem;
}

.letterInstruction {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
}

.letterInstruction p {
  font-size: 1.125rem;
  font-weight: 600;
  color: #666;
  margin: 0;
  opacity: 0.8;
}

/* Alfabeto de referência */
.alphabetReference {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.alphabetTitle {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
}

.alphabetGrid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.alphabetLetter {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 700;
  transition: all 0.3s ease;
}

.alphabetLetter.current {
  background: rgba(255, 206, 84, 0.3);
  border-color: #FECA57;
  color: #FECA57;
}

.alphabetLetter.completed {
  background: rgba(150, 206, 180, 0.3);
  border-color: #96CEB4;
  color: #96CEB4;
}

/* Estatísticas do jogo */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
}

/* Controles do jogo */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.nextButton {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.4);
}

.nextButton:hover {
  background: rgba(76, 175, 80, 0.3);
}

.changeDifficultyBtn {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.4);
}

.changeDifficultyBtn:hover {
  background: rgba(255, 193, 7, 0.3);
}

/* Mensagens de feedback */
.feedbackMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: messageSlide 3s ease-in-out;
}

.feedbackMessage.success {
  background: rgba(76, 175, 80, 0.95);
  color: white;
}

.feedbackMessage.error {
  background: rgba(244, 67, 54, 0.95);
  color: white;
}

@keyframes messageSlide {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Responsividade */
@media (max-width: 768px) {
  .letterRecognitionGame {
    padding: 0.5rem;
  }
  
  .gameTitle {
    font-size: 1.5rem;
  }
  
  .targetLetter {
    font-size: 6rem;
  }
  
  .letterDisplay {
    padding: 2rem;
    min-width: 250px;
  }
  
  .optionsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .optionLetter {
    font-size: 3rem;
  }
  
  .gameControls {
    flex-direction: column;
    align-items: center;
  }
  
  /* 🎯 ESTILOS PARA AS NOVAS ATIVIDADES V3 */
  
  /* Indicador de atividade */
  .activityIndicator {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .activityInfo {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  .activityName {
    color: #4CAF50;
    font-weight: 700;
  }
  
  .progressInfo {
    font-size: 0.9rem;
    opacity: 0.8;
  }
  
  /* Instrução de atividade */
  .activityInstruction {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
  }
  
  .activityInstruction h3 {
    color: white;
    margin-bottom: 0.5rem;
    font-size: 1.4rem;
  }
  
  .activityInstruction p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
  }
  
  /* 🎵 Atividade: Combinação de Sons */
  .soundMatchingActivity .playSoundButton {
    background: rgba(255, 107, 107, 0.9);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 1rem 2rem;
    border-radius: 50px;
    cursor: pointer;
    font-size: 1.2rem;
    margin: 1rem;
    transition: all 0.3s ease;
  }

  .soundMatchingActivity .playSoundButton:hover {
    background: rgba(255, 82, 82, 1);
    transform: scale(1.05);
  }

  .optionExample {
    font-size: 1.2rem;
    margin-top: 0.5rem;
  }

  .phoneticText {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.5rem;
    font-style: italic;
  }

  /* 🔗 Atividade: Formação de Palavras */
  .wordFormationActivity .wordBuilder {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
  }

  .wordSlots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .letterSlot {
    width: 60px;
    height: 60px;
    border: 3px dashed rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transition: all 0.3s ease;
  }

  .letterSlot.filled {
    border: 3px solid #4CAF50;
    background: rgba(76, 175, 80, 0.3);
    color: white;
    animation: slotFill 0.3s ease-in-out;
  }

  @keyframes slotFill {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }

  .availableLetters {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .letterTile {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    transition: all 0.3s ease;
  }

  .letterTile:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  .letterTile:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    transform: none;
    opacity: 0.5;
  }

  /* 📝 Atividade: Reconhecimento de Sequência */
  .sequenceActivity .sequenceDisplay {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    text-align: center;
  }

  .sequenceLetters {
    font-size: 3rem;
    font-weight: bold;
    color: white;
    letter-spacing: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
  }

  .arrow {
    color: rgba(255, 255, 255, 0.7);
    font-size: 2rem;
  }

  .missingLetter {
    color: #ff6b6b;
    animation: missingPulse 1.5s ease-in-out infinite;
  }

  @keyframes missingPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  /* 🎧 Atividade: Discriminação Fonética */
  .phoneticActivity .soundButton {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(74, 144, 226, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 6px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    z-index: 5;
  }

  .phoneticActivity .soundButton:hover {
    background: rgba(74, 144, 226, 1);
    transform: scale(1.1);
  }

  /* 👁️ Atividade: Discriminação Visual */
  .visualActivity .letterGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    max-width: 300px;
    margin: 2rem auto;
  }

  .letterItem {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
  }

  .letterItem:hover {
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-2px);
  }

  .letterItem.target {
    background: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.5);
  }

  .letterItem.selected {
    background: rgba(76, 175, 80, 0.4);
    color: white;
    border-color: rgba(76, 175, 80, 0.7);
    animation: itemSelect 0.3s ease-in-out;
  }

  @keyframes itemSelect {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }

  .foundCount {
    color: #4CAF50;
    font-weight: bold;
  }
}

@media (max-width: 768px) {
  /* Mobile-first adjustments */
  .gameHeader {
    padding: 1rem;
    min-height: 60px;
  }
  
  .gameTitle {
    font-size: 1.5rem;
  }
  
  .activitySubtitle {
    font-size: 0.6rem;
  }
  
  .statsHeader {
    gap: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .statItem {
    padding: 0.75rem 0.5rem;
  }
  
  .statValue {
    font-size: 1.2rem;
  }
  
  .activityButtons {
    gap: 0.3rem;
  }
  
  .activityBtn {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
  
  .optionsGrid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.75rem;
    padding: 0 0.5rem;
  }
  
  .letterOption {
    padding: 1rem 0.75rem;
    min-height: 80px;
  }
}

@media (max-width: 480px) {
  .gameHeader {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
    padding: 0.75rem;
  }
  
  .gameTitle {
    font-size: 1.3rem;
  }
  
  .headerTtsButton {
    position: relative;
    top: auto;
    right: auto;
    margin-top: 0.5rem;
  }
  
  .statsHeader {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .targetLetter {
    font-size: 4rem;
  }
  
  .letterDisplay {
    padding: 1.5rem;
    min-width: 200px;
  }
  
  .optionsGrid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .letterOption {
    min-height: 70px;
    padding: 1rem;
  }
  
  .optionLetter {
    font-size: 2.5rem;
  }
  
  .activityButtons {
    justify-content: center;
    gap: 0.3rem;
  }
  
  .activityBtn {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
    flex: 1;
    min-width: 0;
    justify-content: center;
  }
}

/* Suporte a alto contraste - padrão MemoryGame */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido - padrão MemoryGame */
.reduced-motion {
  .letterOption, .activityBtn, .gameButton, .headerTtsButton {
    animation: none !important;
    transition: none !important;
  }
}
