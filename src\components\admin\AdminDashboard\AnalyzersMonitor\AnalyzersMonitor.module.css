/**
 * @file AnalyzersMonitor.module.css
 * @description Estilos para o Monitor de Analisadores
 * @version 1.0.0
 */

.analyzersMonitor {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.refreshButton {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.refreshButton:hover {
  background: #0056b3;
}

.refreshButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.analyzersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.analyzerCard {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.analyzerCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.analyzerHeader {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.analyzerIcon {
  font-size: 2rem;
}

.analyzerInfo {
  flex: 1;
}

.analyzerName {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.analyzerDescription {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
}

.analyzerStatus {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.statusActive {
  background: #d4edda;
  color: #155724;
}

.statusInactive {
  background: #f8d7da;
  color: #721c24;
}

.statusProcessing {
  background: #fff3cd;
  color: #856404;
}

.analyzerMetrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.metric {
  text-align: center;
}

.metricValue {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}

.metricLabel {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
}

.analyzerActions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.actionButton {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.viewButton {
  background: #007bff;
  color: white;
}

.viewButton:hover {
  background: #0056b3;
}

.configButton {
  background: #28a745;
  color: white;
}

.configButton:hover {
  background: #1e7e34;
}

.restartButton {
  background: #ffc107;
  color: #212529;
}

.restartButton:hover {
  background: #e0a800;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  margin: 1rem;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.modalTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: #333;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.detailCard {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
}

.detailLabel {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 500;
}

.detailValue {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.configSection {
  margin-bottom: 2rem;
}

.configTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.configList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.configItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
}

.configItem:last-child {
  border-bottom: none;
}

.configKey {
  font-weight: 500;
  color: #333;
}

.configValue {
  color: #666;
  font-family: monospace;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.logsSection {
  margin-top: 2rem;
}

.logsTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.logsList {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 0.9rem;
}

.logEntry {
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
}

.logTimestamp {
  color: #666;
  margin-right: 0.5rem;
}

.logLevel {
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

.logInfo {
  background: #d1ecf1;
  color: #0c5460;
}

.logWarning {
  background: #fff3cd;
  color: #856404;
}

.logError {
  background: #f8d7da;
  color: #721c24;
}

.logMessage {
  color: #333;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .analyzersGrid {
    grid-template-columns: 1fr;
  }

  .analyzerMetrics {
    grid-template-columns: 1fr;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }

  .modalContent {
    margin: 0.5rem;
    padding: 1rem;
  }

  .configItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
