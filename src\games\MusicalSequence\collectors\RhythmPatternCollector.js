/**
 * 🎵 RHYTHM PATTERN COLLECTOR
 * Coleta métricas de padrões rítmicos em Musical Sequence
 * Portal Betina V3
 */

export class RhythmPatternCollector {
  constructor() {
    this.collectorId = 'rhythm-pattern';
    this.collectorName = 'Rhythm Pattern Collector';
    this.version = '1.0.0';
    this.isActive = true;
    
    this.metrics = {
      rhythmAccuracy: 0,
      tempoConsistency: 0,
      rhythmComplexityHandling: 0,
      syncronizationSkill: 0
    };
    
    this.collectionHistory = [];
    this.patterns = {
      rhythmPatterns: [],
      tempoVariations: [],
      syncEvents: []
    };
    
    console.log(`🎵 ${this.collectorName} inicializado`);
  }

  /**
   * Coleta dados de padrões rítmicos
   */
  async collectRhythmData(gameData) {
    try {
      const rhythmData = {
        timestamp: Date.now(),
        sessionId: gameData.sessionId,
        rhythmAccuracy: this.analyzeRhythmAccuracy(gameData),
        tempoAnalysis: this.analyzeTempoConsistency(gameData),
        complexityHandling: this.analyzeComplexityHandling(gameData),
        synchronization: this.analyzeSynchronization(gameData)
      };

      this.collectionHistory.push(rhythmData);
      this.updateMetrics(rhythmData);
      
      return rhythmData;
    } catch (error) {
      console.error('Erro ao coletar dados de padrão rítmico:', error);
      return null;
    }
  }

  /**
   * Analisa precisão rítmica
   */
  analyzeRhythmAccuracy(gameData) {
    const attempts = gameData.attempts || [];
    const rhythmAnalysis = [];
    
    attempts.forEach(attempt => {
      const expectedRhythm = attempt.expectedRhythm || [];
      const userRhythm = attempt.userRhythm || [];
      
      const accuracy = this.calculateRhythmAccuracy(expectedRhythm, userRhythm);
      const timing = this.analyzeRhythmTiming(expectedRhythm, userRhythm);
      
      rhythmAnalysis.push({
        attemptId: attempt.id,
        accuracy: accuracy,
        timing: timing,
        complexity: this.calculateRhythmComplexity(expectedRhythm)
      });
    });
    
    return this.summarizeRhythmAccuracy(rhythmAnalysis);
  }

  /**
   * Calcula precisão do ritmo
   */
  calculateRhythmAccuracy(expected, user) {
    if (expected.length === 0 || user.length === 0) return 0;
    
    const minLength = Math.min(expected.length, user.length);
    let correctBeats = 0;
    
    for (let i = 0; i < minLength; i++) {
      const expectedBeat = expected[i];
      const userBeat = user[i];
      
      // Tolerância de timing (±100ms)
      const timingTolerance = 100;
      const timingDiff = Math.abs((expectedBeat.timing || 0) - (userBeat.timing || 0));
      
      if (timingDiff <= timingTolerance) {
        correctBeats++;
      }
    }
    
    return (correctBeats / expected.length) * 100;
  }

  /**
   * Analisa timing do ritmo
   */
  analyzeRhythmTiming(expected, user) {
    if (expected.length === 0 || user.length === 0) return {};
    
    const timingDeviations = [];
    const minLength = Math.min(expected.length, user.length);
    
    for (let i = 0; i < minLength; i++) {
      const expectedTiming = expected[i].timing || 0;
      const userTiming = user[i].timing || 0;
      const deviation = userTiming - expectedTiming;
      
      timingDeviations.push(deviation);
    }
    
    const avgDeviation = timingDeviations.reduce((a, b) => a + b, 0) / timingDeviations.length;
    const absAvgDeviation = timingDeviations.reduce((a, b) => a + Math.abs(b), 0) / timingDeviations.length;
    
    return {
      averageDeviation: avgDeviation,
      absoluteAverageDeviation: absAvgDeviation,
      consistency: Math.max(0, 100 - (absAvgDeviation / 10)),
      tendency: avgDeviation > 0 ? 'late' : avgDeviation < 0 ? 'early' : 'accurate'
    };
  }

  /**
   * Calcula complexidade do ritmo
   */
  calculateRhythmComplexity(rhythm) {
    if (rhythm.length === 0) return 0;
    
    let complexity = 0;
    
    // Analisar variações de duração
    const durations = rhythm.map(beat => beat.duration || 500);
    const uniqueDurations = new Set(durations);
    complexity += uniqueDurations.size;
    
    // Analisar padrões de acentuação
    const accents = rhythm.map(beat => beat.accent || false);
    const accentPattern = accents.filter(Boolean).length;
    complexity += accentPattern;
    
    // Analisar síncopes e variações
    const syncopations = this.detectSyncopations(rhythm);
    complexity += syncopations * 2;
    
    return Math.min(complexity, 10);
  }

  /**
   * Detecta síncopes
   */
  detectSyncopations(rhythm) {
    let syncopations = 0;
    
    for (let i = 1; i < rhythm.length; i++) {
      const current = rhythm[i];
      const previous = rhythm[i - 1];
      
      // Detectar mudanças inesperadas no padrão
      const expectedTiming = (previous.timing || 0) + (previous.duration || 500);
      const actualTiming = current.timing || 0;
      
      const difference = Math.abs(actualTiming - expectedTiming);
      if (difference > 50) { // 50ms de tolerância
        syncopations++;
      }
    }
    
    return syncopations;
  }

  /**
   * Sumariza precisão rítmica
   */
  summarizeRhythmAccuracy(rhythmAnalysis) {
    if (rhythmAnalysis.length === 0) return {};
    
    const avgAccuracy = rhythmAnalysis.reduce((a, b) => a + b.accuracy, 0) / rhythmAnalysis.length;
    const avgTiming = rhythmAnalysis.reduce((a, b) => a + (b.timing.consistency || 0), 0) / rhythmAnalysis.length;
    const avgComplexity = rhythmAnalysis.reduce((a, b) => a + b.complexity, 0) / rhythmAnalysis.length;
    
    return {
      averageAccuracy: avgAccuracy,
      averageTimingConsistency: avgTiming,
      averageComplexity: avgComplexity,
      improvementTrend: this.calculateImprovementTrend(rhythmAnalysis)
    };
  }

  /**
   * Calcula tendência de melhoria
   */
  calculateImprovementTrend(rhythmAnalysis) {
    if (rhythmAnalysis.length < 3) return 0;
    
    const firstThird = rhythmAnalysis.slice(0, Math.floor(rhythmAnalysis.length / 3));
    const lastThird = rhythmAnalysis.slice(-Math.floor(rhythmAnalysis.length / 3));
    
    const firstAvg = firstThird.reduce((a, b) => a + b.accuracy, 0) / firstThird.length;
    const lastAvg = lastThird.reduce((a, b) => a + b.accuracy, 0) / lastThird.length;
    
    return ((lastAvg - firstAvg) / firstAvg) * 100;
  }

  /**
   * Analisa consistência de tempo
   */
  analyzeTempoConsistency(gameData) {
    const attempts = gameData.attempts || [];
    const tempoAnalysis = [];
    
    attempts.forEach(attempt => {
      const userRhythm = attempt.userRhythm || [];
      const tempoConsistency = this.calculateTempoConsistency(userRhythm);
      
      tempoAnalysis.push({
        attemptId: attempt.id,
        consistency: tempoConsistency,
        averageTempo: this.calculateAverageTempo(userRhythm),
        tempoVariations: this.analyzeTempoVariations(userRhythm)
      });
    });
    
    return this.summarizeTempoAnalysis(tempoAnalysis);
  }

  /**
   * Calcula consistência de tempo
   */
  calculateTempoConsistency(rhythm) {
    if (rhythm.length < 2) return 100;
    
    const intervals = [];
    for (let i = 1; i < rhythm.length; i++) {
      const interval = (rhythm[i].timing || 0) - (rhythm[i-1].timing || 0);
      intervals.push(interval);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((a, b) => a + Math.pow(b - avgInterval, 2), 0) / intervals.length;
    const standardDeviation = Math.sqrt(variance);
    
    const consistency = Math.max(0, 100 - (standardDeviation / avgInterval * 100));
    return consistency;
  }

  /**
   * Calcula tempo médio
   */
  calculateAverageTempo(rhythm) {
    if (rhythm.length < 2) return 0;
    
    const intervals = [];
    for (let i = 1; i < rhythm.length; i++) {
      const interval = (rhythm[i].timing || 0) - (rhythm[i-1].timing || 0);
      intervals.push(interval);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    
    // Converter para BPM (batidas por minuto)
    const bpm = avgInterval > 0 ? 60000 / avgInterval : 0;
    return bpm;
  }

  /**
   * Analisa variações de tempo
   */
  analyzeTempoVariations(rhythm) {
    if (rhythm.length < 3) return {};
    
    const intervals = [];
    for (let i = 1; i < rhythm.length; i++) {
      const interval = (rhythm[i].timing || 0) - (rhythm[i-1].timing || 0);
      intervals.push(interval);
    }
    
    const accelerations = [];
    const decelerations = [];
    
    for (let i = 1; i < intervals.length; i++) {
      const change = intervals[i] - intervals[i-1];
      if (change > 0) {
        decelerations.push(change); // intervalo maior = mais lento
      } else if (change < 0) {
        accelerations.push(Math.abs(change)); // intervalo menor = mais rápido
      }
    }
    
    return {
      accelerations: accelerations.length,
      decelerations: decelerations.length,
      avgAcceleration: accelerations.length > 0 ? 
        accelerations.reduce((a, b) => a + b, 0) / accelerations.length : 0,
      avgDeceleration: decelerations.length > 0 ? 
        decelerations.reduce((a, b) => a + b, 0) / decelerations.length : 0
    };
  }

  /**
   * Sumariza análise de tempo
   */
  summarizeTempoAnalysis(tempoAnalysis) {
    if (tempoAnalysis.length === 0) return {};
    
    const avgConsistency = tempoAnalysis.reduce((a, b) => a + b.consistency, 0) / tempoAnalysis.length;
    const avgTempo = tempoAnalysis.reduce((a, b) => a + b.averageTempo, 0) / tempoAnalysis.length;
    
    const totalAccelerations = tempoAnalysis.reduce((a, b) => a + (b.tempoVariations.accelerations || 0), 0);
    const totalDecelerations = tempoAnalysis.reduce((a, b) => a + (b.tempoVariations.decelerations || 0), 0);
    
    return {
      averageConsistency: avgConsistency,
      averageTempo: avgTempo,
      totalVariations: totalAccelerations + totalDecelerations,
      stabilityScore: Math.max(0, 100 - (totalAccelerations + totalDecelerations) * 2)
    };
  }

  /**
   * Analisa tratamento de complexidade
   */
  analyzeComplexityHandling(gameData) {
    const attempts = gameData.attempts || [];
    const complexityLevels = {};
    
    attempts.forEach(attempt => {
      const complexity = this.calculateRhythmComplexity(attempt.expectedRhythm || []);
      const level = Math.floor(complexity);
      
      if (!complexityLevels[level]) {
        complexityLevels[level] = [];
      }
      
      complexityLevels[level].push({
        accuracy: this.calculateRhythmAccuracy(
          attempt.expectedRhythm || [], 
          attempt.userRhythm || []
        ),
        responseTime: attempt.responseTime || 0
      });
    });
    
    return this.analyzeComplexityPerformance(complexityLevels);
  }

  /**
   * Analisa performance por complexidade
   */
  analyzeComplexityPerformance(complexityLevels) {
    const performance = {};
    
    Object.keys(complexityLevels).forEach(level => {
      const attempts = complexityLevels[level];
      const avgAccuracy = attempts.reduce((a, b) => a + b.accuracy, 0) / attempts.length;
      const avgResponseTime = attempts.reduce((a, b) => a + b.responseTime, 0) / attempts.length;
      
      performance[level] = {
        attempts: attempts.length,
        averageAccuracy: avgAccuracy,
        averageResponseTime: avgResponseTime,
        efficiencyScore: avgAccuracy - (avgResponseTime / 1000) // penalizar tempo longo
      };
    });
    
    return performance;
  }

  /**
   * Analisa sincronização
   */
  analyzeSynchronization(gameData) {
    const attempts = gameData.attempts || [];
    const syncAnalysis = [];
    
    attempts.forEach(attempt => {
      const expectedRhythm = attempt.expectedRhythm || [];
      const userRhythm = attempt.userRhythm || [];
      
      const syncScore = this.calculateSynchronizationScore(expectedRhythm, userRhythm);
      const phaseShift = this.calculatePhaseShift(expectedRhythm, userRhythm);
      
      syncAnalysis.push({
        attemptId: attempt.id,
        syncScore: syncScore,
        phaseShift: phaseShift,
        onset: this.analyzeOnsetTiming(expectedRhythm, userRhythm)
      });
    });
    
    return this.summarizeSyncAnalysis(syncAnalysis);
  }

  /**
   * Calcula score de sincronização
   */
  calculateSynchronizationScore(expected, user) {
    if (expected.length === 0 || user.length === 0) return 0;
    
    const maxLength = Math.max(expected.length, user.length);
    const minLength = Math.min(expected.length, user.length);
    
    let synchronizedBeats = 0;
    const syncWindow = 150; // janela de 150ms para sincronização
    
    for (let i = 0; i < minLength; i++) {
      const expectedTiming = expected[i].timing || 0;
      const userTiming = user[i].timing || 0;
      
      if (Math.abs(expectedTiming - userTiming) <= syncWindow) {
        synchronizedBeats++;
      }
    }
    
    return (synchronizedBeats / maxLength) * 100;
  }

  /**
   * Calcula mudança de fase
   */
  calculatePhaseShift(expected, user) {
    if (expected.length === 0 || user.length === 0) return 0;
    
    let totalShift = 0;
    const minLength = Math.min(expected.length, user.length);
    
    for (let i = 0; i < minLength; i++) {
      const expectedTiming = expected[i].timing || 0;
      const userTiming = user[i].timing || 0;
      totalShift += (userTiming - expectedTiming);
    }
    
    return totalShift / minLength;
  }

  /**
   * Analisa timing de início
   */
  analyzeOnsetTiming(expected, user) {
    if (expected.length === 0 || user.length === 0) return {};
    
    const expectedOnset = expected[0].timing || 0;
    const userOnset = user[0].timing || 0;
    const onsetDelay = userOnset - expectedOnset;
    
    return {
      delay: onsetDelay,
      accuracy: Math.max(0, 100 - Math.abs(onsetDelay) / 10)
    };
  }

  /**
   * Sumariza análise de sincronização
   */
  summarizeSyncAnalysis(syncAnalysis) {
    if (syncAnalysis.length === 0) return {};
    
    const avgSyncScore = syncAnalysis.reduce((a, b) => a + b.syncScore, 0) / syncAnalysis.length;
    const avgPhaseShift = syncAnalysis.reduce((a, b) => a + b.phaseShift, 0) / syncAnalysis.length;
    const avgOnsetAccuracy = syncAnalysis.reduce((a, b) => a + (b.onset.accuracy || 0), 0) / syncAnalysis.length;
    
    return {
      averageSyncScore: avgSyncScore,
      averagePhaseShift: avgPhaseShift,
      averageOnsetAccuracy: avgOnsetAccuracy,
      overallSyncQuality: (avgSyncScore + avgOnsetAccuracy) / 2
    };
  }

  /**
   * Atualiza métricas acumuladas
   */
  updateMetrics(rhythmData) {
    const rhythmAccuracy = rhythmData.rhythmAccuracy || {};
    const tempoAnalysis = rhythmData.tempoAnalysis || {};
    const complexityHandling = rhythmData.complexityHandling || {};
    const synchronization = rhythmData.synchronization || {};
    
    this.metrics.rhythmAccuracy = this.calculateRunningAverage(
      this.metrics.rhythmAccuracy,
      rhythmAccuracy.averageAccuracy || 0
    );
    
    this.metrics.tempoConsistency = this.calculateRunningAverage(
      this.metrics.tempoConsistency,
      tempoAnalysis.averageConsistency || 0
    );
    
    // Score de complexidade baseado na performance geral
    const complexityScores = Object.values(complexityHandling).map(level => level.efficiencyScore || 0);
    const avgComplexityScore = complexityScores.length > 0 ? 
      complexityScores.reduce((a, b) => a + b, 0) / complexityScores.length : 0;
    
    this.metrics.rhythmComplexityHandling = this.calculateRunningAverage(
      this.metrics.rhythmComplexityHandling,
      Math.max(0, avgComplexityScore)
    );
    
    this.metrics.syncronizationSkill = this.calculateRunningAverage(
      this.metrics.syncronizationSkill,
      synchronization.overallSyncQuality || 0
    );
  }

  /**
   * Calcula média móvel
   */
  calculateRunningAverage(current, newValue) {
    return current * 0.8 + newValue * 0.2;
  }

  /**
   * Método de coleta padrão
   */
  async collect(gameData) {
    return await this.collectRhythmData(gameData);
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal
   */
  async analyze(gameData) {
    return await this.collectRhythmData(gameData);
  }

  /**
   * Obtém métricas atuais
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Obtém histórico de coleta
   */
  getCollectionHistory() {
    return [...this.collectionHistory];
  }

  /**
   * Reset do coletor
   */
  reset() {
    this.metrics = {
      rhythmAccuracy: 0,
      tempoConsistency: 0,
      rhythmComplexityHandling: 0,
      syncronizationSkill: 0
    };
    this.collectionHistory = [];
    this.patterns = {
      rhythmPatterns: [],
      tempoVariations: [],
      syncEvents: []
    };
  }
}
