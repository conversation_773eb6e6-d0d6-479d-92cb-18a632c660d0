/**
 * @file DashboardContainer.jsx
 * @description Container principal dos dashboards do Portal Betina V3
 * @version 3.0.0
 */

import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import styles from './DashboardContainer.module.css'
import { usePremium } from '../../context/PremiumContext'
import { useAdmin } from '../../context/AdminContext'
import { useAccessibilityContext } from '../context/AccessibilityContext'
import {
  PerformanceDashboard,
  NeuropedagogicalDashboard,
  MultisensoryMetricsDashboard,
  IntegratedSystemDashboard,
  AdvancedAIReport,
  DASHBOARD_CONFIG,
  DASHBOARD_ORDER,
  getDashboardsByAccess,
  isPremiumDashboard,
  isAdminDashboard,
  canAccessDashboard,
} from './index.js'
import AdminGate from '../common/AdminGate/AdminGate'
import TextToSpeech from '../common/TextToSpeech/TextToSpeech'
import But<PERSON> from '../common/Button/Button'

/**
 * Container principal para mostrar diferentes tipos de dashboard
 */
const DashboardContainer = ({ initialTab = 'performance' }) => {
  const [activeTab, setActiveTab] = useState(initialTab)
  const [availableDashboards, setAvailableDashboards] = useState([])
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [loginData, setLoginData] = useState({ email: '', password: '' })
  const [loginError, setLoginError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { isPremium, canAccessDashboard: canAccessPremiumDashboard } = usePremium()
  const { isAdmin, canAccessIntegratedDashboard } = useAdmin()
  const { settings } = useAccessibilityContext()
  
  // Determinar quais dashboards o usuário pode acessar
  useEffect(() => {
    const accessLevel = isPremium ? 'premium' : 'public'
    const dashboards = getDashboardsByAccess(accessLevel, isAdmin)
    setAvailableDashboards(dashboards)
    
    // Se o initialTab não estiver disponível, voltar para performance
    if (initialTab !== 'performance' && !canAccessDashboard(initialTab, accessLevel, isAdmin)) {
      setActiveTab('performance')
    }
  }, [isPremium, isAdmin, initialTab])
  
  // Função de login usando a API do backend
  const handleLogin = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    setLoginError('')

    console.log('🔐 Tentando login:', { 
      email: loginData.email, 
      password: loginData.password ? '***' : 'vazio' 
    })

    try {
      console.log('📡 Fazendo requisição para /api/auth/dashboard/login')
      
      const response = await fetch('/api/auth/dashboard/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginData.email,
          password: loginData.password,
          rememberMe: true
        })
      })

      console.log('📨 Resposta recebida:', { 
        status: response.status, 
        statusText: response.statusText,
        ok: response.ok 
      })

      const data = await response.json()
      console.log('📄 Dados da resposta:', data)

      if (data.success && data.token) {
        console.log('✅ Login bem-sucedido!')
        // Salvar token no localStorage (estrutura correta do backend)
        localStorage.setItem('authToken', data.token)
        localStorage.setItem('userData', JSON.stringify(data.user || { email: loginData.email }))
        
        setIsAuthenticated(true)
        setLoginError('')
        setIsLoading(false)
        
        // Verificar se é usuário premium (sem await para não travar)
        checkPremiumStatus()
      } else {
        console.log('❌ Login falhou:', data.message)
        setLoginError(data.message || 'Erro ao fazer login')
        setIsLoading(false)
      }
    } catch (error) {
      console.error('💥 Erro no login:', error)
      setLoginError('Erro de conexão. Tente novamente.')
      setIsLoading(false)
    }
  }

  // Verificar status premium do usuário
  const checkPremiumStatus = async () => {
    try {
      const token = localStorage.getItem('authToken')
      if (!token) return

      const response = await fetch('/api/premium/auth/status', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      const data = await response.json()
      if (response.ok && data.success) {
        // Atualizar contexto premium se necessário
        console.log('Status premium:', data.data)
      }
    } catch (error) {
      console.error('Erro ao verificar status premium:', error)
    }
  }

  // Verificar se usuário já está logado ao carregar o componente
  useEffect(() => {
    const token = localStorage.getItem('authToken')
    const userData = localStorage.getItem('userData')
    
    console.log('🔍 Verificando autenticação:', { token: !!token, userData: !!userData })
    
    if (token && userData) {
      try {
        const user = JSON.parse(userData)
        console.log('✅ Usuário encontrado no localStorage:', user.email)
        setIsAuthenticated(true)
        // Verificar se token ainda é válido
        checkPremiumStatus()
      } catch (error) {
        console.error('❌ Erro ao verificar dados salvos:', error)
        // Limpar dados inválidos
        localStorage.removeItem('authToken')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('userData')
        setIsAuthenticated(false)
      }
    } else {
      console.log('🚫 Usuário não autenticado - redirecionando para login')
      setIsAuthenticated(false)
    }
  }, [])

  const handleInputChange = (field, value) => {
    setLoginData(prev => ({
      ...prev,
      [field]: value
    }))
    setLoginError('')
  }  // Verificar se precisa mostrar tela de login para dashboards premium/admin
  const needsAuthentication = () => {
    console.log('🔐 Verificando autenticação:', { 
      activeTab, 
      isAuthenticated, 
      isAdmin, 
      isPremium,
      isAdminTab: isAdminDashboard(activeTab),
      isPremiumTab: isPremiumDashboard(activeTab)
    })
    
    // TODOS OS DASHBOARDS REQUEREM LOGIN AGORA
    return !isAuthenticated
  }

  // Renderizar tela de login
  const renderLoginScreen = () => {
    return (      <div style={{
        minHeight: '100vh',
      <div className={styles.loginContainer}>
        <div className={styles.loginBox}>          <div style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
            <h1 style={{ fontSize: '1.8rem', marginBottom: '0.5rem' }}>
              {isAdminDashboard(activeTab) ? '🔐 Admin' : '🔐 Premium'}
            </h1>
            <p style={{ opacity: 0.9, lineHeight: 1.4, fontSize: '0.9rem' }}>
              {isAdminDashboard(activeTab) 
                ? 'Credenciais administrativas' 
                : 'Entre para acessar os dashboards'
              }
            </p>
          </div>

          <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <div>
              <label style={{ display: 'block', marginBottom: '0.3rem', fontWeight: '500', fontSize: '0.9rem' }}>
                Email:
              </label>
              <input
                type="email"
                value={loginData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.8rem',
                  borderRadius: '10px',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '0.9rem',
                  outline: 'none',
                  transition: 'all 0.3s ease'
                }}
                placeholder="Digite seu email"
                required
              />
            </div>

            <div>
              <label style={{ display: 'block', marginBottom: '0.3rem', fontWeight: '500', fontSize: '0.9rem' }}>
                Senha:
              </label>
              <input
                type="password"
                value={loginData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.8rem',
                  borderRadius: '10px',
                  border: '2px solid rgba(255, 255, 255, 0.3)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '0.9rem',
                  outline: 'none',
                  transition: 'all 0.3s ease'
                }}
                placeholder="Digite sua senha"
                required
              />
            </div>

            {loginError && (
              <div style={{
                background: 'rgba(239, 68, 68, 0.2)',
                color: '#fecaca',
                padding: '0.8rem',
                borderRadius: '10px',
                border: '2px solid #ef4444',
                textAlign: 'center',
                fontSize: '0.85rem'
              }}>
                {loginError}
              </div>
            )}

            <button
              type="submit"
              disabled={isLoading}
              style={{
                background: isLoading ? 'rgba(107, 114, 128, 0.8)' : 'linear-gradient(135deg, #22c55e, #16a34a)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '0.8rem 1.5rem',
                fontSize: '0.9rem',
                fontWeight: '600',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease',
                textTransform: 'uppercase',
                letterSpacing: '0.5px'
              }}
            >
              {isLoading ? '🔄 Entrando...' : '🔓 Entrar'}
            </button>
          </form>

          <div style={{
            marginTop: '1.5rem',
            padding: '0.8rem',
            background: 'rgba(59, 130, 246, 0.2)',
            borderRadius: '10px',
            border: '2px solid #3b82f6',
            fontSize: '0.8rem',
            textAlign: 'center'
          }}>
            <strong>💡 Demo:</strong><br />
            <code><EMAIL></code> / <code>admin123</code>
          </div>
        </div>
      </div>    )
  }  // Renderizar o dashboard ativo
  const renderDashboard = () => {
    console.log('🎨 Renderizando dashboard:', { 
      activeTab, 
      needsAuth: needsAuthentication(),
      isAuthenticated 
    })
    
    // Verificar se precisa de autenticação para dashboards premium
    if (needsAuthentication()) {
      console.log('🚫 Exibindo tela de login')
      return renderLoginScreen()
    }
    
    console.log('✅ Usuário autenticado - exibindo dashboard')
    
    // Verificar acesso específico para dashboard administrativo
    if (isAdminDashboard(activeTab) && (!isAdmin || !canAccessIntegratedDashboard())) {
      return (        <AdminGate 
          title="Sistema Integrado - Acesso Restrito"
          message="Este dashboard contém informações administrativas sensíveis e está disponível apenas para administradores autorizados do sistema."
        />
      )
    }
    
    // Renderizar o dashboard apropriado
    switch(activeTab) {
      case 'performance':
        return <PerformanceDashboard />
      case 'neuropedagogical':
        return <NeuropedagogicalDashboard />
      case 'multisensory':
        return <MultisensoryMetricsDashboard />
      case 'integrated':
        return <IntegratedSystemDashboard />
      case 'relatorioA':
        return <AdvancedAIReport />
      default:
        return <PerformanceDashboard />
    }
  }

  // Ordenar dashboards conforme a ordem definida no DASHBOARD_ORDER
  const sortedDashboards = [...availableDashboards].sort((a, b) => {
    const aIndex = DASHBOARD_ORDER.indexOf(a[0])
    const bIndex = DASHBOARD_ORDER.indexOf(b[0])
    return aIndex - bIndex
  })
  
  console.log('🔄 Renderizando componente principal')
  
  return (
    <div className={styles.dashboardContainer}>
      {/* Navegação entre dashboards */}
      <div className={styles.dashboardTabs}>
        {sortedDashboards.map(([key, config]) => (
          <button
            key={key}
            className={`${styles.dashboardTab} ${activeTab === key ? styles.active : ''}`}
            onClick={() => setActiveTab(key)}
            aria-pressed={activeTab === key}
          >
            <span aria-hidden="true">{config.icon}</span> {config.title}
          </button>        ))}
      </div>
      
      {/* Conteúdo do dashboard */}
      <div className={styles.dashboardContent}>
        <div className={styles.dashboardWrapper}>
          {renderDashboard()}
        </div>
      </div>
    </div>
  )
}

DashboardContainer.propTypes = {
  initialTab: PropTypes.string
}

export default DashboardContainer
