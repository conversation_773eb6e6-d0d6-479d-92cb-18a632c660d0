/**
 * 🎯 PAIR MATCHING V3 COLLECTOR
 * Coletor especializado para análise de correspondência de pares (versão melhorada)
 * Localização: src/games/MemoryGame/collectors/PairMatchingV3Collector.js
 */

export class PairMatchingV3Collector {
  constructor() {
    this.id = 'pair_matching_v3';
    this.name = 'Correspondência de Pares V3';
    this.version = '3.0.0';
    this.category = 'visual_matching';
    
    this.data = {
      matches: [],
      pairAccuracy: [],
      visualMemory: [],
      spatialMapping: [],
      searchStrategies: [],
      attentionPatterns: [],
      workingMemoryLoad: [],
      errorPatterns: []
    };
  }

  // Coletar dados de tentativas de correspondência
  collect(data) {
    const {
      pairs,
      userMatches,
      gridLayout,
      responseTime,
      accuracy,
      difficulty,
      cardProperties,
      searchSequence,
      timestamp
    } = data;

    // Análise detalhada de correspondência
    const matchAnalysis = this.analyzePairMatching(pairs, userMatches, gridLayout);
    
    this.data.matches.push({
      pairs: pairs,
      userMatches: userMatches,
      layout: gridLayout,
      properties: cardProperties,
      sequence: searchSequence,
      analysis: matchAnalysis,
      responseTime,
      timestamp
    });

    // Precisão de pares
    this.data.pairAccuracy.push({
      accuracy,
      totalPairs: pairs.length,
      correctMatches: matchAnalysis.correctMatches,
      falseMatches: matchAnalysis.falseMatches,
      gridSize: gridLayout.size,
      timestamp
    });

    // Memória visual
    const visualAnalysis = this.analyzeVisualMemory(pairs, userMatches, cardProperties);
    this.data.visualMemory.push(visualAnalysis);

    // Mapeamento espacial
    const spatialAnalysis = this.analyzeSpatialMapping(pairs, userMatches, gridLayout);
    this.data.spatialMapping.push(spatialAnalysis);

    // Estratégias de busca
    if (searchSequence) {
      const searchAnalysis = this.analyzeSearchStrategy(searchSequence, gridLayout, pairs);
      this.data.searchStrategies.push(searchAnalysis);
    }

    // Padrões de atenção
    const attentionAnalysis = this.analyzeAttentionPatterns(searchSequence, userMatches);
    this.data.attentionPatterns.push(attentionAnalysis);

    // Carga de memória de trabalho
    const memoryLoad = this.calculateWorkingMemoryLoad(pairs.length, accuracy, gridLayout.size);
    this.data.workingMemoryLoad.push({
      load: memoryLoad,
      performance: accuracy,
      complexity: this.calculateGameComplexity(pairs.length, gridLayout.size),
      timestamp
    });

    // Padrões de erro
    const errorAnalysis = this.analyzeErrorPatterns(pairs, userMatches);
    this.data.errorPatterns.push(errorAnalysis);
  }

  analyzePairMatching(pairs, userMatches, gridLayout) {
    const analysis = {
      correctMatches: 0,
      falseMatches: 0,
      missedPairs: 0,
      accuracy: 0,
      efficiency: 0,
      spatialErrors: [],
      visualErrors: []
    };

    // Converter pares para formato comparável
    const originalPairs = this.convertPairsToSet(pairs);
    const userPairSet = this.convertPairsToSet(userMatches);

    // Analisar matches corretos
    originalPairs.forEach(pair => {
      if (this.hasPairMatch(pair, userPairSet)) {
        analysis.correctMatches++;
      } else {
        analysis.missedPairs++;
      }
    });

    // Analisar matches falsos
    userPairSet.forEach(userPair => {
      if (!this.hasPairMatch(userPair, originalPairs)) {
        analysis.falseMatches++;
        
        // Analisar tipo de erro
        const errorType = this.analyzeMatchError(userPair, pairs, gridLayout);
        if (errorType.type === 'spatial') {
          analysis.spatialErrors.push(errorType);
        } else {
          analysis.visualErrors.push(errorType);
        }
      }
    });

    // Calcular métricas
    analysis.accuracy = pairs.length > 0 ? analysis.correctMatches / pairs.length : 0;
    analysis.efficiency = this.calculateMatchingEfficiency(analysis, pairs.length);

    return analysis;
  }

  convertPairsToSet(pairs) {
    return new Set(pairs.map(pair => {
      // Normalizar para que a ordem não importe
      const sorted = [pair.card1, pair.card2].sort();
      return `${sorted[0]}-${sorted[1]}`;
    }));
  }

  hasPairMatch(targetPair, pairSet) {
    return pairSet.has(targetPair);
  }

  analyzeMatchError(userPair, correctPairs, gridLayout) {
    const [card1, card2] = userPair.split('-');
    
    // Verificar se é erro espacial (cartas próximas)
    const spatialProximity = this.calculateSpatialProximity(card1, card2, gridLayout);
    if (spatialProximity < 2) { // Cartas adjacentes
      return {
        type: 'spatial',
        subtype: 'proximity_error',
        cards: [card1, card2],
        distance: spatialProximity
      };
    }

    // Verificar se é erro visual (similaridade visual)
    const visualSimilarity = this.calculateVisualSimilarity(card1, card2, correctPairs);
    if (visualSimilarity > 0.7) {
      return {
        type: 'visual',
        subtype: 'similarity_error',
        cards: [card1, card2],
        similarity: visualSimilarity
      };
    }

    return {
      type: 'random',
      subtype: 'random_error',
      cards: [card1, card2]
    };
  }

  calculateSpatialProximity(card1, card2, gridLayout) {
    const pos1 = this.getCardPosition(card1, gridLayout);
    const pos2 = this.getCardPosition(card2, gridLayout);
    
    if (!pos1 || !pos2) return Infinity;
    
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    
    return Math.sqrt(dx * dx + dy * dy);
  }

  getCardPosition(cardId, gridLayout) {
    // Assumindo que o cardId corresponde a uma posição no grid
    const index = parseInt(cardId);
    const cols = gridLayout.cols || Math.sqrt(gridLayout.size);
    
    return {
      x: index % cols,
      y: Math.floor(index / cols)
    };
  }

  calculateVisualSimilarity(card1, card2, pairs) {
    // Buscar pares corretos que contenham essas cartas
    let card1Pair = null;
    let card2Pair = null;
    
    pairs.forEach(pair => {
      if (pair.card1 === card1 || pair.card2 === card1) {
        card1Pair = pair;
      }
      if (pair.card1 === card2 || pair.card2 === card2) {
        card2Pair = pair;
      }
    });

    if (!card1Pair || !card2Pair) return 0;

    // Comparar propriedades visuais dos pares corretos
    const similarity = this.compareCardPairProperties(card1Pair, card2Pair);
    
    return similarity;
  }

  compareCardPairProperties(pair1, pair2) {
    // Assumindo que os pares têm propriedades como cor, forma, padrão
    let similarity = 0;
    let factors = 0;

    if (pair1.category && pair2.category) {
      similarity += pair1.category === pair2.category ? 1 : 0;
      factors++;
    }

    if (pair1.color && pair2.color) {
      similarity += this.calculateColorSimilarity(pair1.color, pair2.color);
      factors++;
    }

    if (pair1.pattern && pair2.pattern) {
      similarity += pair1.pattern === pair2.pattern ? 1 : 0;
      factors++;
    }

    return factors > 0 ? similarity / factors : 0;
  }

  calculateColorSimilarity(color1, color2) {
    if (color1 === color2) return 1;

    // Definir cores similares
    const similarColors = {
      'red': ['orange', 'pink'],
      'blue': ['purple', 'cyan'],
      'green': ['yellow', 'cyan'],
      'yellow': ['orange', 'green']
    };

    if (similarColors[color1]?.includes(color2) || similarColors[color2]?.includes(color1)) {
      return 0.5;
    }

    return 0;
  }

  calculateMatchingEfficiency(analysis, totalPairs) {
    const totalAttempts = analysis.correctMatches + analysis.falseMatches;
    return totalAttempts > 0 ? analysis.correctMatches / totalAttempts : 0;
  }

  analyzeVisualMemory(pairs, userMatches, cardProperties) {
    const memoryAnalysis = {
      recognitionAccuracy: 0,
      confusionMatrix: {},
      visualCategories: {},
      memoryStrength: 0
    };

    // Analisar reconhecimento por categoria visual
    const categories = this.groupByVisualCategory(pairs, cardProperties);
    
    Object.keys(categories).forEach(category => {
      const categoryPairs = categories[category];
      const categoryMatches = this.countCategoryMatches(categoryPairs, userMatches);
      
      memoryAnalysis.visualCategories[category] = {
        total: categoryPairs.length,
        correct: categoryMatches,
        accuracy: categoryPairs.length > 0 ? categoryMatches / categoryPairs.length : 0
      };
    });

    // Calcular precisão geral de reconhecimento
    const totalCorrect = Object.values(memoryAnalysis.visualCategories)
      .reduce((sum, cat) => sum + cat.correct, 0);
    const totalPairs = pairs.length;
    
    memoryAnalysis.recognitionAccuracy = totalPairs > 0 ? totalCorrect / totalPairs : 0;

    // Analisar força da memória
    memoryAnalysis.memoryStrength = this.calculateMemoryStrength(memoryAnalysis.visualCategories);

    return memoryAnalysis;
  }

  groupByVisualCategory(pairs, cardProperties) {
    const categories = {};
    
    pairs.forEach(pair => {
      const category = cardProperties[pair.card1]?.category || 'unknown';
      if (!categories[category]) categories[category] = [];
      categories[category].push(pair);
    });

    return categories;
  }

  countCategoryMatches(categoryPairs, userMatches) {
    const userPairSet = this.convertPairsToSet(userMatches);
    const categoryPairSet = this.convertPairsToSet(categoryPairs);
    
    let matches = 0;
    categoryPairSet.forEach(pair => {
      if (userPairSet.has(pair)) matches++;
    });

    return matches;
  }

  calculateMemoryStrength(categories) {
    const accuracies = Object.values(categories).map(cat => cat.accuracy);
    return accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
  }

  analyzeSpatialMapping(pairs, userMatches, gridLayout) {
    const spatialAnalysis = {
      locationAccuracy: 0,
      spatialStrategy: 'unknown',
      gridEfficiency: 0,
      spatialBias: {}
    };

    // Analisar precisão de localização
    spatialAnalysis.locationAccuracy = this.calculateLocationAccuracy(pairs, userMatches, gridLayout);

    // Identificar estratégia espacial
    spatialAnalysis.spatialStrategy = this.identifySpatialStrategy(userMatches, gridLayout);

    // Calcular eficiência do grid
    spatialAnalysis.gridEfficiency = this.calculateGridEfficiency(userMatches, gridLayout);

    // Analisar bias espacial
    spatialAnalysis.spatialBias = this.analyzeSpatialBias(userMatches, gridLayout);

    return spatialAnalysis;
  }

  calculateLocationAccuracy(pairs, userMatches, gridLayout) {
    // Verificar quantos matches ocorreram nas posições corretas
    let correctLocations = 0;
    
    pairs.forEach(pair => {
      const userMatch = userMatches.find(match => 
        (match.card1 === pair.card1 && match.card2 === pair.card2) ||
        (match.card1 === pair.card2 && match.card2 === pair.card1)
      );
      
      if (userMatch) {
        const pos1Correct = this.isPositionCorrect(pair.card1, userMatch.card1, gridLayout);
        const pos2Correct = this.isPositionCorrect(pair.card2, userMatch.card2, gridLayout);
        
        if (pos1Correct && pos2Correct) correctLocations++;
      }
    });

    return pairs.length > 0 ? correctLocations / pairs.length : 0;
  }

  isPositionCorrect(originalCard, matchedCard, gridLayout) {
    // Verificar se a carta foi encontrada na posição correta
    return originalCard === matchedCard;
  }

  identifySpatialStrategy(userMatches, gridLayout) {
    if (userMatches.length < 2) return 'insufficient_data';

    // Analisar padrão de busca
    const searchPattern = this.analyzeSearchPattern(userMatches, gridLayout);
    
    if (searchPattern.linearity > 0.8) return 'systematic_linear';
    if (searchPattern.clustering > 0.7) return 'spatial_clustering';
    if (searchPattern.randomness > 0.6) return 'random_search';
    
    return 'mixed_strategy';
  }

  analyzeSearchPattern(userMatches, gridLayout) {
    const positions = userMatches.map(match => [
      this.getCardPosition(match.card1, gridLayout),
      this.getCardPosition(match.card2, gridLayout)
    ]).flat().filter(pos => pos !== null);

    if (positions.length < 3) {
      return { linearity: 0, clustering: 0, randomness: 1 };
    }

    const linearity = this.calculateLinearity(positions);
    const clustering = this.calculateClustering(positions);
    const randomness = 1 - Math.max(linearity, clustering);

    return { linearity, clustering, randomness };
  }

  calculateLinearity(positions) {
    if (positions.length < 3) return 0;

    // Calcular se as posições seguem um padrão linear
    let linearSequences = 0;
    let totalSequences = positions.length - 2;

    for (let i = 0; i < positions.length - 2; i++) {
      const p1 = positions[i];
      const p2 = positions[i + 1];
      const p3 = positions[i + 2];

      // Verificar se três pontos consecutivos estão em linha
      if (this.arePointsLinear(p1, p2, p3)) {
        linearSequences++;
      }
    }

    return totalSequences > 0 ? linearSequences / totalSequences : 0;
  }

  arePointsLinear(p1, p2, p3) {
    // Verificar se três pontos estão aproximadamente em linha
    const dx1 = p2.x - p1.x;
    const dy1 = p2.y - p1.y;
    const dx2 = p3.x - p2.x;
    const dy2 = p3.y - p2.y;

    // Produto cruzado próximo de zero indica linearidade
    const crossProduct = Math.abs(dx1 * dy2 - dy1 * dx2);
    return crossProduct < 0.5; // Tolerância para linearidade
  }

  calculateClustering(positions) {
    if (positions.length < 2) return 0;

    // Calcular proximidade média entre posições consecutivas
    let totalDistance = 0;
    let transitions = 0;

    for (let i = 0; i < positions.length - 1; i++) {
      const distance = this.calculateDistance(positions[i], positions[i + 1]);
      totalDistance += distance;
      transitions++;
    }

    const avgDistance = transitions > 0 ? totalDistance / transitions : 0;
    
    // Clustering alto = distância baixa
    return Math.max(0, 1 - (avgDistance / 5)); // Assumindo grid máximo de 5x5
  }

  calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  calculateGridEfficiency(userMatches, gridLayout) {
    // Calcular quão eficientemente o grid foi navegado
    const totalCards = gridLayout.size;
    const uniqueCards = new Set();
    
    userMatches.forEach(match => {
      uniqueCards.add(match.card1);
      uniqueCards.add(match.card2);
    });

    return totalCards > 0 ? uniqueCards.size / totalCards : 0;
  }

  analyzeSpatialBias(userMatches, gridLayout) {
    const bias = {
      topBias: 0,
      bottomBias: 0,
      leftBias: 0,
      rightBias: 0,
      centerBias: 0
    };

    const positions = userMatches.map(match => [
      this.getCardPosition(match.card1, gridLayout),
      this.getCardPosition(match.card2, gridLayout)
    ]).flat().filter(pos => pos !== null);

    if (positions.length === 0) return bias;

    const cols = gridLayout.cols || Math.sqrt(gridLayout.size);
    const rows = gridLayout.rows || Math.sqrt(gridLayout.size);
    const centerX = cols / 2;
    const centerY = rows / 2;

    positions.forEach(pos => {
      // Bias vertical
      if (pos.y < centerY) bias.topBias++;
      else bias.bottomBias++;

      // Bias horizontal
      if (pos.x < centerX) bias.leftBias++;
      else bias.rightBias++;

      // Bias central
      const distanceFromCenter = this.calculateDistance(pos, { x: centerX, y: centerY });
      if (distanceFromCenter < Math.min(cols, rows) / 3) {
        bias.centerBias++;
      }
    });

    // Normalizar por número de posições
    Object.keys(bias).forEach(key => {
      bias[key] = bias[key] / positions.length;
    });

    return bias;
  }

  analyzeSearchStrategy(searchSequence, gridLayout, pairs) {
    const strategy = {
      type: 'unknown',
      efficiency: 0,
      systematicness: 0,
      backtracking: 0,
      focusedSearch: 0
    };

    if (!searchSequence || searchSequence.length === 0) return strategy;

    // Identificar tipo de estratégia
    strategy.type = this.identifySearchType(searchSequence, gridLayout);

    // Calcular eficiência
    strategy.efficiency = this.calculateSearchEfficiency(searchSequence, pairs);

    // Calcular sistematicidade
    strategy.systematicness = this.calculateSystematicness(searchSequence, gridLayout);

    // Detectar retrocesso
    strategy.backtracking = this.detectBacktracking(searchSequence);

    // Analisar busca focada
    strategy.focusedSearch = this.analyzeFocusedSearch(searchSequence, pairs);

    return strategy;
  }

  identifySearchType(searchSequence, gridLayout) {
    const pattern = this.analyzeSearchPattern(
      searchSequence.map(pos => ({ card1: pos, card2: pos })), 
      gridLayout
    );

    if (pattern.linearity > 0.7) return 'systematic_scan';
    if (pattern.clustering > 0.7) return 'clustered_search';
    return 'exploratory';
  }

  calculateSearchEfficiency(searchSequence, pairs) {
    // Eficiência = encontrou pares / total de buscas
    const relevantSearches = searchSequence.filter(search => 
      pairs.some(pair => pair.card1 === search || pair.card2 === search)
    );

    return searchSequence.length > 0 ? relevantSearches.length / searchSequence.length : 0;
  }

  calculateSystematicness(searchSequence, gridLayout) {
    if (searchSequence.length < 3) return 0;

    // Calcular quão sistemática foi a busca
    const positions = searchSequence.map(card => this.getCardPosition(card, gridLayout))
      .filter(pos => pos !== null);

    return this.calculateLinearity(positions);
  }

  detectBacktracking(searchSequence) {
    const visited = new Set();
    let backtracks = 0;

    searchSequence.forEach(card => {
      if (visited.has(card)) {
        backtracks++;
      }
      visited.add(card);
    });

    return searchSequence.length > 0 ? backtracks / searchSequence.length : 0;
  }

  analyzeFocusedSearch(searchSequence, pairs) {
    // Analisar se a busca foi focada em encontrar pares
    let focusedSearches = 0;

    searchSequence.forEach((card, index) => {
      if (index < searchSequence.length - 1) {
        const nextCard = searchSequence[index + 1];
        const isPair = pairs.some(pair => 
          (pair.card1 === card && pair.card2 === nextCard) ||
          (pair.card1 === nextCard && pair.card2 === card)
        );
        
        if (isPair) focusedSearches++;
      }
    });

    return searchSequence.length > 1 ? focusedSearches / (searchSequence.length - 1) : 0;
  }

  analyzeAttentionPatterns(searchSequence, userMatches) {
    const attention = {
      sustainedAttention: 0,
      selectiveAttention: 0,
      dividedAttention: 0,
      attentionShifts: 0
    };

    if (!searchSequence || searchSequence.length === 0) return attention;

    // Atenção sustentada (consistência ao longo do tempo)
    attention.sustainedAttention = this.calculateSustainedAttention(searchSequence);

    // Atenção seletiva (foco em elementos relevantes)
    attention.selectiveAttention = this.calculateSelectiveAttention(searchSequence, userMatches);

    // Atenção dividida (capacidade de processar múltiplos elementos)
    attention.dividedAttention = this.calculateDividedAttention(userMatches);

    // Mudanças de atenção
    attention.attentionShifts = this.calculateAttentionShifts(searchSequence);

    return attention;
  }

  calculateSustainedAttention(searchSequence) {
    // Medir consistência da busca ao longo do tempo
    if (searchSequence.length < 4) return 1;

    const segments = this.divideIntoSegments(searchSequence, 4);
    const segmentEfficiencies = segments.map(segment => this.calculateSegmentEfficiency(segment));
    
    // Atenção sustentada = baixa variância na eficiência
    const mean = segmentEfficiencies.reduce((sum, eff) => sum + eff, 0) / segmentEfficiencies.length;
    const variance = segmentEfficiencies.reduce((sum, eff) => sum + Math.pow(eff - mean, 2), 0) / segmentEfficiencies.length;
    
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  divideIntoSegments(sequence, segmentCount) {
    const segmentSize = Math.ceil(sequence.length / segmentCount);
    const segments = [];
    
    for (let i = 0; i < sequence.length; i += segmentSize) {
      segments.push(sequence.slice(i, i + segmentSize));
    }
    
    return segments;
  }

  calculateSegmentEfficiency(segment) {
    // Placeholder para eficiência do segmento
    return segment.length > 0 ? 1 : 0;
  }

  calculateSelectiveAttention(searchSequence, userMatches) {
    // Capacidade de focar em elementos relevantes
    const relevantSearches = searchSequence.filter(search => 
      userMatches.some(match => match.card1 === search || match.card2 === search)
    );

    return searchSequence.length > 0 ? relevantSearches.length / searchSequence.length : 0;
  }

  calculateDividedAttention(userMatches) {
    // Capacidade de processar múltiplos pares simultaneamente
    if (userMatches.length < 2) return 0;

    // Analisar sobreposição temporal nos matches
    const avgMatchComplexity = userMatches.reduce((sum, match) => {
      // Complexidade baseada no número de cartas únicas envolvidas
      const uniqueCards = new Set([match.card1, match.card2]);
      return sum + uniqueCards.size;
    }, 0) / userMatches.length;

    return Math.min(1, avgMatchComplexity / 2);
  }

  calculateAttentionShifts(searchSequence) {
    if (searchSequence.length < 2) return 0;

    // Contar mudanças significativas na sequência de busca
    let shifts = 0;
    
    for (let i = 1; i < searchSequence.length; i++) {
      const prev = parseInt(searchSequence[i - 1]);
      const curr = parseInt(searchSequence[i]);
      
      // Shift significativo = mudança de posição maior que adjacente
      if (Math.abs(curr - prev) > 1) {
        shifts++;
      }
    }

    return shifts / (searchSequence.length - 1);
  }

  calculateWorkingMemoryLoad(pairCount, accuracy, gridSize) {
    // Carga baseada no número de pares, tamanho do grid e performance
    const baseLoad = Math.min(100, pairCount * 10 + gridSize * 2);
    const performanceModifier = (1 - accuracy) * 15;
    
    return Math.min(100, baseLoad + performanceModifier);
  }

  calculateGameComplexity(pairCount, gridSize) {
    // Complexidade baseada no número de pares e tamanho do grid
    return Math.min(100, (pairCount * 5) + (gridSize * 3));
  }

  analyzeErrorPatterns(pairs, userMatches) {
    const errors = {
      spatialErrors: 0,
      visualErrors: 0,
      memoryErrors: 0,
      attentionErrors: 0,
      totalErrors: 0
    };

    const correctPairSet = this.convertPairsToSet(pairs);
    const userPairSet = this.convertPairsToSet(userMatches);

    // Contar erros por tipo
    userMatches.forEach(userMatch => {
      const userPairKey = this.convertPairsToSet([userMatch]).values().next().value;
      
      if (!correctPairSet.has(userPairKey)) {
        errors.totalErrors++;
        
        // Classificar tipo de erro
        const errorType = this.classifyError(userMatch, pairs);
        errors[errorType]++;
      }
    });

    // Normalizar por número total de tentativas
    const totalAttempts = userMatches.length;
    if (totalAttempts > 0) {
      Object.keys(errors).forEach(key => {
        if (key !== 'totalErrors') {
          errors[key] = errors[key] / totalAttempts;
        }
      });
    }

    return errors;
  }

  classifyError(userMatch, correctPairs) {
    // Classificar o tipo de erro baseado nas características
    const visualSim = this.calculateVisualSimilarity(userMatch.card1, userMatch.card2, correctPairs);
    
    if (visualSim > 0.7) return 'visualErrors';
    // Adicionar mais classificações conforme necessário
    return 'memoryErrors';
  }

  // Gerar relatório de análise de correspondência
  generateReport() {
    const totalAttempts = this.data.matches.length;
    if (totalAttempts === 0) return null;

    const avgAccuracy = this.data.pairAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / totalAttempts;

    // Análise de estratégias
    const strategyAnalysis = this.calculateStrategyMetrics();
    
    // Análise de memória
    const memoryAnalysis = this.calculateMemoryMetrics();

    return {
      collectorId: this.id,
      totalAttempts,
      avgAccuracy: Math.round(avgAccuracy * 100) / 100,
      strategyAnalysis,
      memoryAnalysis,
      recommendations: this.generateMatchingRecommendations(avgAccuracy, strategyAnalysis),
      cognitiveInsights: {
        visualWorkingMemoryCapacity: this.calculateVisualCapacity(),
        spatialMappingAbility: this.calculateSpatialMapping(),
        attentionControl: this.calculateAttentionControl(),
        searchEfficiency: this.calculateSearchEfficiency()
      }
    };
  }

  calculateStrategyMetrics() {
    if (this.data.searchStrategies.length === 0) return null;

    const strategies = this.data.searchStrategies;
    const avgEfficiency = strategies.reduce((sum, s) => sum + s.efficiency, 0) / strategies.length;
    const avgSystematicness = strategies.reduce((sum, s) => sum + s.systematicness, 0) / strategies.length;

    return {
      averageEfficiency: avgEfficiency,
      averageSystematicness: avgSystematicness,
      preferredStrategy: this.identifyPreferredStrategy(strategies)
    };
  }

  identifyPreferredStrategy(strategies) {
    const strategyTypes = {};
    strategies.forEach(s => {
      strategyTypes[s.type] = (strategyTypes[s.type] || 0) + 1;
    });

    return Object.keys(strategyTypes).reduce((a, b) => 
      strategyTypes[a] > strategyTypes[b] ? a : b
    );
  }

  calculateMemoryMetrics() {
    if (this.data.visualMemory.length === 0) return null;

    const memories = this.data.visualMemory;
    const avgRecognition = memories.reduce((sum, m) => sum + m.recognitionAccuracy, 0) / memories.length;
    const avgMemoryStrength = memories.reduce((sum, m) => sum + m.memoryStrength, 0) / memories.length;

    return {
      averageRecognition: avgRecognition,
      averageMemoryStrength: avgMemoryStrength
    };
  }

  calculateVisualCapacity() {
    if (this.data.workingMemoryLoad.length === 0) return 50;

    const recent = this.data.workingMemoryLoad.slice(-5);
    const avgLoad = recent.reduce((sum, item) => sum + item.load, 0) / recent.length;
    const avgPerformance = recent.reduce((sum, item) => sum + item.performance, 0) / recent.length;

    return Math.min(100, (100 - avgLoad) + (avgPerformance * 40));
  }

  calculateSpatialMapping() {
    if (this.data.spatialMapping.length === 0) return 50;

    const recent = this.data.spatialMapping.slice(-5);
    const avgAccuracy = recent.reduce((sum, item) => sum + item.locationAccuracy, 0) / recent.length;

    return avgAccuracy * 100;
  }

  calculateAttentionControl() {
    if (this.data.attentionPatterns.length === 0) return 50;

    const recent = this.data.attentionPatterns.slice(-5);
    const avgSustained = recent.reduce((sum, item) => sum + item.sustainedAttention, 0) / recent.length;
    const avgSelective = recent.reduce((sum, item) => sum + item.selectiveAttention, 0) / recent.length;

    return ((avgSustained + avgSelective) / 2) * 100;
  }

  calculateSearchEfficiency() {
    if (this.data.searchStrategies.length === 0) return 50;

    const recent = this.data.searchStrategies.slice(-5);
    const avgEfficiency = recent.reduce((sum, item) => sum + item.efficiency, 0) / recent.length;

    return avgEfficiency * 100;
  }

  generateMatchingRecommendations(accuracy, strategyAnalysis) {
    const recommendations = [];

    if (accuracy < 0.7) {
      recommendations.push('Praticar com menos pares');
    }

    if (strategyAnalysis && strategyAnalysis.averageSystematicness < 0.6) {
      recommendations.push('Desenvolver estratégia de busca sistemática');
    }

    if (strategyAnalysis && strategyAnalysis.averageEfficiency < 0.6) {
      recommendations.push('Exercícios de eficiência visual');
    }

    return recommendations;
  }

  reset() {
    this.data = {
      matches: [],
      pairAccuracy: [],
      visualMemory: [],
      spatialMapping: [],
      searchStrategies: [],
      attentionPatterns: [],
      workingMemoryLoad: [],
      errorPatterns: []
    };
  }
}

export default PairMatchingV3Collector;
