/**
 * @file SystemLogs.module.css
 * @description Estilos para o Visualizador de Logs do Sistema
 * @version 1.0.0
 */

.systemLogs {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.title {
  color: #333;
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0;
}

.controls {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filterLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.filterSelect {
  padding: 0.4rem 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  background-color: white;
  min-width: 120px;
}

.searchInput {
  padding: 0.4rem 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  min-width: 200px;
}

.autoRefreshToggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toggleSwitch {
  position: relative;
  width: 50px;
  height: 24px;
  background: #ccc;
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggleSwitch.active {
  background: #007bff;
}

.toggleSlider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s;
}

.toggleSwitch.active .toggleSlider {
  transform: translateX(26px);
}

.refreshButton {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.refreshButton:hover {
  background: #0056b3;
}

.refreshButton:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.clearButton {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.clearButton:hover {
  background: #c82333;
}

.statsBar {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
}

.statItem {
  text-align: center;
  min-width: 100px;
}

.statValue {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.statError {
  color: #dc3545;
}

.statWarning {
  color: #ffc107;
}

.statInfo {
  color: #17a2b8;
}

.statDebug {
  color: #6c757d;
}

.statLabel {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
}

.logsContainer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.logsHeader {
  background: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logsTitle {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.logsCount {
  font-size: 0.9rem;
  color: #666;
}

.logsList {
  max-height: 600px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.logEntry {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  transition: background-color 0.2s;
}

.logEntry:hover {
  background: #f8f9fa;
}

.logEntry:last-child {
  border-bottom: none;
}

.logLevel {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 60px;
  text-align: center;
  white-space: nowrap;
}

.levelError {
  background: #f8d7da;
  color: #721c24;
}

.levelWarning {
  background: #fff3cd;
  color: #856404;
}

.levelInfo {
  background: #d1ecf1;
  color: #0c5460;
}

.levelDebug {
  background: #d1d3d4;
  color: #383d41;
}

.logTimestamp {
  color: #666;
  min-width: 140px;
  white-space: nowrap;
}

.logService {
  color: #007bff;
  font-weight: 500;
  min-width: 100px;
  white-space: nowrap;
}

.logMessage {
  flex: 1;
  color: #333;
  word-break: break-word;
}

.logDetails {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #666;
  white-space: pre-wrap;
}

.logStackTrace {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #ffe6e6;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #dc3545;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
}

.noLogs {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.exportButton {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.exportButton:hover {
  background: #1e7e34;
}

/* Responsividade */
@media (max-width: 1024px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .controls {
    justify-content: space-between;
  }

  .logEntry {
    flex-direction: column;
    gap: 0.5rem;
  }

  .logTimestamp,
  .logService {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .statsBar {
    flex-direction: column;
  }

  .controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filterGroup {
    width: 100%;
    justify-content: space-between;
  }

  .filterSelect,
  .searchInput {
    min-width: auto;
    flex: 1;
  }

  .logsContainer {
    font-size: 0.8rem;
  }

  .logLevel {
    min-width: 50px;
    font-size: 0.7rem;
  }

  .metricsGrid,
  .systemMetricsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Seções de Métricas do Prometheus */
.prometheusSection {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.prometheusSection h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metricCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.metricTitle {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 500;
}

.metricValue {
  font-size: 1.5rem;
  font-weight: bold;
}

.alertsSection h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.alertsList {
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

.alertItem {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alertItem:last-child {
  border-bottom: none;
}

.alertWarning {
  border-left: 4px solid #ffc107;
  background: #fff8e1;
}

.alertError {
  border-left: 4px solid #dc3545;
  background: #ffebee;
}

.alertInfo {
  border-left: 4px solid #17a2b8;
  background: #e3f2fd;
}

.alertIcon {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

.alertContent {
  flex: 1;
}

.alertMessage {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}

.alertTime {
  font-size: 0.85rem;
  color: #666;
}

/* Seção de Métricas do Sistema */
.systemMetricsSection {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.systemMetricsSection h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.systemMetricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.systemMetricCard {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.autoRefreshLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
}

.filters {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.searchBox {
  flex: 1;
  min-width: 250px;
}
