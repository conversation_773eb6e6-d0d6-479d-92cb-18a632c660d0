/**
 * @file CognitiveAnalyzer.js
 * @description Analisador Cognitivo para o Portal Betina V3
 * @version 3.0.0
 * 
 * Funcionalidades:
 * - Análise de padrões cognitivos
 * - Avaliação de domínios cognitivos
 * - Cache inteligente para otimização
 * - Logs estruturados
 */

import { logger } from '../core/logging/StructuredLogger.js';
import { IntelligentCache } from '../core/cache/IntelligentCache.js';
import constants from '../constants.js';

/**
 * Analisador Cognitivo - Processa dados cognitivos das sessões
 */
export class CognitiveAnalyzer {
  constructor (options = {}) {
    this.systemOrchestrator = null;
    
    // Inicializar logger
    this.logger = logger;
    
    // Cache inteligente para análises cognitivas
    this.cache = new IntelligentCache({
      maxSize: 600,
      defaultTTL: 2400000, // 40 minutos para análises cognitivas
      strategy: 'LRU'
    });
    
    this.cognitivePatterns = this._initializeCognitivePatterns();
    this.normativeData = this._initializeNormativeData();
    this.analysisAlgorithms = this._initializeAnalysisAlgorithms();
    
    // Configurações de análise
    this.analysisConfig = {
      minSessionDuration: options.minSessionDuration || 30000, // 30 segundos
      confidenceThreshold: options.confidenceThreshold || 0.7,
      cognitiveDomainsEnabled: options.cognitiveDomainsEnabled || [
        'attention', 'memory', 'executive_function', 'processing_speed',
        'language', 'visual_spatial', 'social_cognition'
      ]
    };
    
    // Métricas do analisador
    this.metrics = {
      totalAnalyses: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageAnalysisTime: 0,
      domainAnalyses: {}
    };
    
    this.connectToSystemOrchestrator();
    
    this.logger.info('🧠 CognitiveAnalyzer inicializado', {
      type: 'cognitive_analyzer_init',
      cacheEnabled: true,
      analysisConfig: this.analysisConfig
    });
  }
  
  /**
   * Conecta ao orquestrador central do sistema
   * ✅ CORRIGIDO: Usando injeção de dependência para evitar dependência circular
   */
  connectToSystemOrchestrator() {
    try {
      // SystemOrchestrator será injetado via setSystemOrchestrator()
      this.systemOrchestrator = null;
      this.logger.info('🧠 CognitiveAnalyzer: Aguardando injeção do SystemOrchestrator');
    } catch (error) {
      this.logger.error('❌ Erro ao preparar conexão com SystemOrchestrator:', error);
    }
  }

  /**
   * Injeta referência do SystemOrchestrator (evita dependência circular)
   * @param {Object} systemOrchestrator - Instância do SystemOrchestrator
   */
  setSystemOrchestrator(systemOrchestrator) {
    this.systemOrchestrator = systemOrchestrator;
    this.logger.info('CognitiveAnalyzer: SystemOrchestrator injetado com sucesso');
  }

  /**
   * Initialize cognitive pattern recognition rules
   */
  _initializeCognitivePatterns () {
    return {
      attention: {
        sustained: {
          excellent: { minDuration: 600, consistency: 0.9 },
          good: { minDuration: 300, consistency: 0.7 },
          developing: { minDuration: 120, consistency: 0.5 },
          emerging: { minDuration: 60, consistency: 0.3 }
        },
        selective: {
          indicators: ['distractorResistance', 'targetFocus', 'filteredAttention'],
          thresholds: { high: 0.8, medium: 0.6, low: 0.4 }
        },
        divided: {
          indicators: ['multitaskPerformance', 'taskSwitching', 'parallelProcessing'],
          thresholds: { high: 0.7, medium: 0.5, low: 0.3 }
        }
      },
      memory: {
        workingMemory: {
          patterns: ['sequenceRecall', 'spatialMemory', 'auditoryMemory'],
          capacityLevels: { high: 7, medium: 5, low: 3 }
        },
        episodic: {
          indicators: ['eventRecall', 'contextualMemory', 'temporalOrder'],
          retentionPeriods: [300, 1800, 3600, 86400] // 5min, 30min, 1hr, 24hr
        },
        semantic: {
          indicators: ['conceptRetrieval', 'categoryFluency', 'knowledgeApplication'],
          domains: ['verbal', 'visual', 'spatial', 'numerical']
        }
      },
      executiveFunction: {
        inhibition: {
          tasks: ['goNoGo', 'stroop', 'flanker'],
          metrics: ['reactionTime', 'accuracy', 'consistency']
        },
        flexibility: {
          tasks: ['setShifting', 'ruleSwitch', 'categoryChange'],
          metrics: ['switchCost', 'errorRate', 'adaptationSpeed']
        },
        planning: {
          indicators: ['goalSetting', 'strategyFormation', 'sequenceOrganization'],
          complexity: ['simple', 'moderate', 'complex', 'advanced']
        }
      },
      processing: {
        speed: {
          domains: ['visual', 'auditory', 'motor', 'cognitive'],
          metrics: ['reactionTime', 'throughput', 'efficiency']
        },
        accuracy: {
          types: ['perceptual', 'conceptual', 'motor'],
          thresholds: { excellent: 0.95, good: 0.85, fair: 0.70, poor: 0.50 }
        }
      }
    }
  }

  /**
   * Initialize normative data for comparison
   */
  _initializeNormativeData () {
    return {
      ageGroups: {
        '3-4': {
          attentionSpan: { mean: 180, std: 60 },
          workingMemory: { mean: 3, std: 1 },
          processingSpeed: { mean: 2000, std: 500 }
        },
        '5-6': {
          attentionSpan: { mean: 300, std: 90 },
          workingMemory: { mean: 4, std: 1 },
          processingSpeed: { mean: 1500, std: 400 }
        },
        '7-8': {
          attentionSpan: { mean: 450, std: 120 },
          workingMemory: { mean: 5, std: 1.5 },
          processingSpeed: { mean: 1200, std: 300 }
        },
        '9-12': {
          attentionSpan: { mean: 600, std: 150 },
          workingMemory: { mean: 6, std: 1.5 },
          processingSpeed: { mean: 1000, std: 250 }
        }
      },
      autismSpecific: {
        attentionProfile: {
          focusedAttention: 'often enhanced',
          shiftedAttention: 'often impaired',
          sustainedAttention: 'variable'
        },
        memoryProfile: {
          visualMemory: 'often strength',
          auditoryMemory: 'often weakness',
          semanticMemory: 'detail-focused'
        },
        executiveProfile: {
          flexibility: 'often impaired',
          inhibition: 'variable',
          planning: 'often impaired'
        }
      }
    }
  }

  /**
   * Initialize analysis algorithms
   */
  _initializeAnalysisAlgorithms () {
    return {
      trendAnalysis: {
        windowSize: 10, // sessions
        significanceThreshold: 0.05,
        minDataPoints: 5
      },
      patternRecognition: {
        correlationThreshold: 0.6,
        frequencyThreshold: 0.3,
        consistencyThreshold: 0.7
      },
      anomalyDetection: {
        zScoreThreshold: 2.5,
        outlierPercentage: 0.05,
        changePointThreshold: 0.8
      }
    }
  }

  /**
   * Perform comprehensive cognitive analysis
   * @param {Array} metricsData - Array of metrics from multiple sessions
   * @param {Object} childProfile - Child's profile information
   * @returns {Object} Comprehensive cognitive analysis
   */  async analyzeCognitive (metricsData, childProfile) {
    try {
      // Notificar o orquestrador central sobre o início da análise cognitiva
      if (this.systemOrchestrator && typeof this.systemOrchestrator.notifyEvent === 'function') {
        try {
          await this.systemOrchestrator.notifyEvent('cognitive_analysis_started', {
            childId: childProfile.id,
            sessionDuration: 0,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.warn('SystemOrchestrator notification failed:', error.message);
        }
      }
      
      const analysis = {
        timestamp: new Date(),
        childId: childProfile.id,
        periodAnalyzed: this._calculatePeriod(metricsData),

        attention: await this._analyzeAttention(metricsData, childProfile),
        memory: await this._analyzeMemory(metricsData, childProfile),
        executiveFunction: await this._analyzeExecutiveFunction(metricsData, childProfile),
        processing: await this._analyzeProcessing(metricsData, childProfile),

        patterns: await this._identifyPatterns(metricsData, childProfile),
        trends: await this._analyzeTrends(metricsData, childProfile),
        correlations: await this._analyzeCorrelations(metricsData),

        strengths: [],
        challenges: [],
        recommendations: [],

        overallProfile: {},
        developmentalTrajectory: {},
        interventionTargets: []
      }

      // Synthesize findings
      analysis.overallProfile = this._synthesizeProfile(analysis)
      analysis.developmentalTrajectory = this._assessTrajectory(analysis, metricsData)
      analysis.interventionTargets = this._identifyInterventionTargets(analysis)      // Generate recommendations
      analysis.recommendations = this._generateRecommendations(analysis, childProfile)

      // Notificar o orquestrador central sobre a conclusão da análise cognitiva
      if (this.systemOrchestrator && typeof this.systemOrchestrator.notifyEvent === 'function') {
        try {
          await this.systemOrchestrator.notifyEvent('cognitive_analysis_completed', {
            childId: childProfile.id,
            sessionDuration: 0,
            summary: {
              attentionScore: analysis.attention.overall,
              memoryScore: analysis.memory.overall,
              executiveFunctionScore: analysis.executiveFunction.overall,
              strengthsCount: analysis.strengths.length,
              challengesCount: analysis.challenges.length,
              recommendationsCount: analysis.recommendations.length
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.warn('SystemOrchestrator notification failed:', error.message);
        }
      }

      return analysis
    } catch (error) {
      this.logger.error('Error in cognitive analysis:', error)
      
      // Notificar erro ao orquestrador central
      if (this.systemOrchestrator && typeof this.systemOrchestrator.notifyEvent === 'function') {
        try {
          await this.systemOrchestrator.notifyEvent('cognitive_analysis_error', {
            childId: childProfile?.id,
            sessionDuration: 0,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.warn('SystemOrchestrator error notification failed:', error.message);
        }
      }
      
      throw new Error(`Cognitive analysis failed: ${error.message}`)
    }
  }

  /**
   * Analisa dados cognitivos da sessão de jogo
   * @param {Object} gameSession - Dados da sessão de jogo
   * @returns {Object} Análise cognitiva completa
   */
  async analyzeCognitiveSession(gameSession) {
    const startTime = Date.now();
    
    try {
      // Verificar cache primeiro
      const cacheKey = `cognitive_analysis_${gameSession.childId}_${gameSession.gameName}_${this.generateSessionHash(gameSession)}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached) {
        this.metrics.cacheHits++;
        this.logger.info('📥 Análise cognitiva obtida do cache', { 
          sessionId: gameSession.id,
          cacheKey 
        });
        return cached;
      }
      
      this.metrics.cacheMisses++;
      this.metrics.totalAnalyses++;
      
      this.logger.info('🧠 Iniciando análise cognitiva da sessão', { 
        sessionId: gameSession.id,
        childId: gameSession.childId,
        gameName: gameSession.gameName
      });
      
      // Notificar início da análise
      if (this.systemOrchestrator && typeof this.systemOrchestrator.notifyEvent === 'function') {
        try {
          await this.systemOrchestrator.notifyEvent('cognitive_analysis_started', {
            sessionId: gameSession.id || gameSession.sessionId || `cognitive_session_${Date.now()}`,
            childId: gameSession.childId,
            sessionDuration: gameSession.duration || 0,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.warn('SystemOrchestrator notification failed:', error.message);
        }
      }

      // Análise por domínios cognitivos
      const cognitiveAnalysis = {
        attention: await this.analyzeAttentionDomain(gameSession),
        memory: await this.analyzeMemoryDomain(gameSession),
        executiveFunction: await this.analyzeExecutiveDomain(gameSession),
        processingSpeed: await this.analyzeProcessingDomain(gameSession),
        language: await this.analyzeLanguageDomain(gameSession),
        visualSpatial: await this.analyzeVisuoSpatialDomain(gameSession),
        socialCognition: await this.analyzeSocialCognitionDomain(gameSession)
      };

      // Análise consolidada
      const analysis = {
        sessionId: gameSession.id,
        childId: gameSession.childId,
        gameName: gameSession.gameName,
        timestamp: new Date().toISOString(),
        cognitiveAnalysis,
        overallScore: this.calculateOverallCognitiveScore(cognitiveAnalysis),
        cognitiveProfile: this.generateCognitiveProfile(cognitiveAnalysis),
        patterns: this.identifyCognitivePatterns(cognitiveAnalysis),
        recommendations: this.generateCognitiveRecommendations(cognitiveAnalysis),
        confidence: this.calculateAnalysisConfidence(cognitiveAnalysis),
        normativeComparison: this.compareToNormativeData(gameSession, cognitiveAnalysis),
        metadata: {
          analysisTime: Date.now() - startTime,
          version: '3.0.0',
          analyzer: 'CognitiveAnalyzer',
          algorithmsUsed: Object.keys(this.analysisAlgorithms)
        }
      };
      
      // Armazenar no cache com tags para invalidação inteligente
      this.cache.set(cacheKey, analysis, {
        tags: [
          `child_${gameSession.childId}`,
          `game_${gameSession.gameName}`,
          'cognitive_analysis',
          `date_${new Date().toISOString().split('T')[0]}`
        ],
        ttl: this.analysisConfig.cacheTTL || 2400000
      });
      
      // Atualizar métricas
      this.updateMetrics(Date.now() - startTime);
      this.updateDomainMetrics(cognitiveAnalysis);
      
      this.logger.info('✅ Análise cognitiva concluída', { 
        sessionId: gameSession.id,
        overallScore: analysis.overallScore,
        patterns: analysis.patterns.length,
        confidence: analysis.confidence,
        analysisTime: Date.now() - startTime
      });
      
      // Notificar conclusão da análise
      if (this.systemOrchestrator && typeof this.systemOrchestrator.notifyEvent === 'function') {
        try {
          await this.systemOrchestrator.notifyEvent('cognitive_analysis_completed', {
            sessionId: gameSession.id,
            childId: gameSession.childId,
            sessionDuration: gameSession.duration || 0,
            summary: {
              overallScore: analysis.overallScore,
              patternCount: analysis.patterns.length,
              confidence: analysis.confidence,
              strongDomains: this.getStrongDomains(cognitiveAnalysis),
              weakDomains: this.getWeakDomains(cognitiveAnalysis)
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.warn('SystemOrchestrator notification failed:', error.message);
        }
      }
      
      return analysis;
    } catch (error) {
      this.logger.error('❌ Erro na análise cognitiva:', {
        sessionId: gameSession.id,
        error: error.message,
        stack: error.stack
      });
      
      // Notificar erro
      if (this.systemOrchestrator && typeof this.systemOrchestrator.notifyEvent === 'function') {
        try {
          await this.systemOrchestrator.notifyEvent('cognitive_analysis_error', {
            sessionId: gameSession.id || gameSession.sessionId || `cognitive_session_${Date.now()}`,
            childId: gameSession.childId,
            sessionDuration: gameSession.duration || 0,
            error: error.message,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          this.logger.warn('SystemOrchestrator error notification failed:', error.message);
        }
      }
      
      throw new Error(`Falha na análise cognitiva: ${error.message}`);
    }
  }

  /**
   * Analyze attention capabilities
   */
  async _analyzeAttention (metricsData, childProfile) {
    const attentionData = metricsData.map(m => m.cognitiveMetrics).filter(Boolean)

    if (attentionData.length === 0) {
      return { status: 'insufficient_data' }
    }

    const analysis = {
      sustained: this._analyzeSustainedAttention(attentionData),
      selective: this._analyzeSelectiveAttention(attentionData),
      divided: this._analyzeDividedAttention(attentionData),
      overall: {}
    }

    // Calculate overall attention profile
    analysis.overall = {
      averageSpan: this._calculateMean(attentionData.map(d => d.attentionSpan).filter(Boolean)),
      consistency: this._calculateConsistency(attentionData.map(d => d.attentionSpan).filter(Boolean)),
      trajectory: this._calculateTrend(attentionData.map(d => d.attentionSpan).filter(Boolean)),
      percentile: this._calculatePercentile(analysis.sustained.averageSpan, childProfile.age, 'attentionSpan')
    }

    // Identify patterns
    analysis.patterns = this._identifyAttentionPatterns(attentionData)

    return analysis
  }

  /**
   * Analyze sustained attention
   */
  _analyzeSustainedAttention (attentionData) {
    const spans = attentionData.map(d => d.attentionSpan).filter(Boolean)
    const focusLevels = attentionData.map(d => d.focusLevel).filter(Boolean)

    if (spans.length === 0) return { status: 'no_data' }

    const averageSpan = this._calculateMean(spans)
    const maxSpan = Math.max(...spans)
    const consistency = this._calculateConsistency(spans)

    let level = 'emerging'
    if (averageSpan >= 600 && consistency >= 0.9) level = 'excellent'
    else if (averageSpan >= 300 && consistency >= 0.7) level = 'good'
    else if (averageSpan >= 120 && consistency >= 0.5) level = 'developing'

    return {
      averageSpan,
      maxSpan,
      consistency,
      level,
      focusQuality: this._calculateMean(focusLevels),
      improvement: this._calculateImprovement(spans),
      variability: this._calculateVariability(spans)
    }
  }

  /**
   * Analyze selective attention
   */
  _analyzeSelectiveAttention (attentionData) {
    // This would analyze distractor resistance and target focus
    // For now, we'll use focus level as a proxy
    const focusLevels = attentionData.map(d => d.focusLevel).filter(Boolean)

    if (focusLevels.length === 0) return { status: 'no_data' }

    const averageFocus = this._calculateMean(focusLevels)
    const consistency = this._calculateConsistency(focusLevels)

    let level = 'low'
    if (averageFocus >= 8 && consistency >= 0.8) level = 'high'
    else if (averageFocus >= 6 && consistency >= 0.6) level = 'medium'

    return {
      averageFocus,
      consistency,
      level,
      distractorResistance: this._assessDistractorResistance(attentionData),
      targetFocus: this._assessTargetFocus(attentionData)
    }
  }

  /**
   * Analyze divided attention
   */
  _analyzeDividedAttention (attentionData) {
    // This would analyze multitasking capabilities
    // For now, we'll use task switching indicators

    return {
      status: 'estimated',
      multitaskingCapability: this._estimateMultitasking(attentionData),
      taskSwitchingEfficiency: this._estimateTaskSwitching(attentionData),
      level: 'developing' // Would be calculated based on actual data
    }
  }

  /**
   * Analyze memory capabilities
   */
  async _analyzeMemory (metricsData, childProfile) {
    const memoryData = metricsData.map(m => m.cognitiveMetrics?.memoryRecall).filter(Boolean)

    if (memoryData.length === 0) {
      return { status: 'insufficient_data' }
    }

    return {
      workingMemory: this._analyzeWorkingMemory(memoryData),
      shortTerm: this._analyzeShortTermMemory(memoryData),
      longTerm: this._analyzeLongTermMemory(memoryData),
      overall: this._synthesizeMemoryProfile(memoryData),
      patterns: this._identifyMemoryPatterns(memoryData)
    }
  }

  /**
   * Analyze working memory
   */
  _analyzeWorkingMemory (memoryData) {
    const workingMemoryScores = memoryData.map(d => d.workingMemory).filter(Boolean)

    if (workingMemoryScores.length === 0) return { status: 'no_data' }

    const average = this._calculateMean(workingMemoryScores)
    const consistency = this._calculateConsistency(workingMemoryScores)
    const trend = this._calculateTrend(workingMemoryScores)

    return {
      averageScore: average,
      consistency,
      trend,
      capacity: this._estimateCapacity(average),
      efficiency: this._estimateEfficiency(workingMemoryScores)
    }
  }

  /**
   * Analyze executive function
   */
  async _analyzeExecutiveFunction (metricsData, childProfile) {
    const executiveData = metricsData.map(m => m.cognitiveMetrics?.executiveFunction).filter(Boolean)

    if (executiveData.length === 0) {
      return { status: 'insufficient_data' }
    }

    return {
      inhibition: this._analyzeInhibition(executiveData),
      flexibility: this._analyzeFlexibility(executiveData),
      planning: this._analyzePlanning(executiveData),
      overall: this._synthesizeExecutiveProfile(executiveData)
    }
  }

  /**
   * Analyze processing capabilities
   */
  async _analyzeProcessing (metricsData, childProfile) {
    const processingData = metricsData.map(m => ({
      reactionTime: m.motorMetrics?.visualMotor?.reactionTime,
      accuracy: m.interactionData?.clicks?.accuracy,
      speed: m.cognitiveMetrics?.processingSpeed
    })).filter(d => Object.values(d).some(Boolean))

    if (processingData.length === 0) {
      return { status: 'insufficient_data' }
    }

    return {
      speed: this._analyzeProcessingSpeed(processingData),
      accuracy: this._analyzeProcessingAccuracy(processingData),
      efficiency: this._analyzeProcessingEfficiency(processingData),
      overall: this._synthesizeProcessingProfile(processingData)
    }
  }

  /**
   * Identify cognitive patterns
   */
  async _identifyPatterns (metricsData, childProfile) {
    const patterns = []

    // Attention patterns
    const attentionPattern = this._identifyAttentionPatterns(metricsData)
    if (attentionPattern.length > 0) patterns.push(...attentionPattern)

    // Memory patterns
    const memoryPattern = this._identifyMemoryPatterns(metricsData)
    if (memoryPattern.length > 0) patterns.push(...memoryPattern)

    // Executive function patterns
    const executivePattern = this._identifyExecutivePatterns(metricsData)
    if (executivePattern.length > 0) patterns.push(...executivePattern)

    // Time-of-day patterns
    const temporalPattern = this._identifyTemporalPatterns(metricsData)
    if (temporalPattern.length > 0) patterns.push(...temporalPattern)

    return patterns
  }

  /**
   * Identify attention patterns
   */
  _identifyAttentionPatterns (metricsData) {
    const patterns = []

    // Check for consistent attention decline pattern
    const attentionData = metricsData.map(m => m.cognitiveMetrics?.attentionSpan).filter(Boolean)
    if (attentionData.length >= 5) {
      const trend = this._calculateTrend(attentionData)
      if (trend < -0.5) {
        patterns.push({
          type: 'attention_decline',
          description: 'Consistent decline in attention span over sessions',
          severity: Math.abs(trend) > 1 ? 'high' : 'medium',
          recommendation: 'Consider break strategies and attention training'
        })
      } else if (trend > 0.5) {
        patterns.push({
          type: 'attention_improvement',
          description: 'Positive trend in attention span development',
          severity: 'positive',
          recommendation: 'Continue current intervention strategies'
        })
      }
    }

    // Check for focus consistency pattern
    const focusData = metricsData.map(m => m.cognitiveMetrics?.focusLevel).filter(Boolean)
    if (focusData.length >= 3) {
      const consistency = this._calculateConsistency(focusData)
      if (consistency < 0.3) {
        patterns.push({
          type: 'focus_inconsistency',
          description: 'Highly variable focus levels across sessions',
          severity: 'medium',
          recommendation: 'Investigate environmental and temporal factors affecting focus'
        })
      }
    }

    return patterns
  }

  /**
   * Analyze trends over time
   */
  async _analyzeTrends (metricsData, childProfile) {
    const trends = {}

    // Attention trends
    const attentionSpans = metricsData.map(m => m.cognitiveMetrics?.attentionSpan).filter(Boolean)
    if (attentionSpans.length >= 3) {
      trends.attention = {
        direction: this._calculateTrend(attentionSpans) > 0 ? 'improving' : 'declining',
        slope: this._calculateTrend(attentionSpans),
        confidence: this._calculateTrendConfidence(attentionSpans)
      }
    }

    // Memory trends
    const memoryScores = metricsData.map(m => {
      const recall = m.cognitiveMetrics?.memoryRecall
      return recall ? (recall.shortTerm + recall.workingMemory + recall.longTerm) / 3 : null
    }).filter(Boolean)

    if (memoryScores.length >= 3) {
      trends.memory = {
        direction: this._calculateTrend(memoryScores) > 0 ? 'improving' : 'declining',
        slope: this._calculateTrend(memoryScores),
        confidence: this._calculateTrendConfidence(memoryScores)
      }
    }

    // Processing speed trends
    const reactionTimes = metricsData.map(m => m.motorMetrics?.visualMotor?.reactionTime).filter(Boolean)
    if (reactionTimes.length >= 3) {
      trends.processingSpeed = {
        direction: this._calculateTrend(reactionTimes) < 0 ? 'improving' : 'declining', // Lower is better
        slope: this._calculateTrend(reactionTimes),
        confidence: this._calculateTrendConfidence(reactionTimes)
      }
    }

    return trends
  }

  /**
   * Analyze correlations between cognitive metrics
   */
  async _analyzeCorrelations (metricsData) {
    const correlations = []

    // Extract relevant metrics
    const metrics = metricsData.map(m => ({
      attention: m.cognitiveMetrics?.attentionSpan,
      focus: m.cognitiveMetrics?.focusLevel,
      memory: m.cognitiveMetrics?.memoryRecall?.workingMemory,
      reactionTime: m.motorMetrics?.visualMotor?.reactionTime,
      accuracy: m.interactionData?.clicks?.accuracy,
      engagement: m.behavioralMetrics?.engagement?.level
    })).filter(m => Object.values(m).some(Boolean))

    if (metrics.length < 3) return correlations

    // Calculate key correlations
    const metricPairs = [
      ['attention', 'focus'],
      ['attention', 'engagement'],
      ['memory', 'accuracy'],
      ['focus', 'accuracy'],
      ['reactionTime', 'accuracy']
    ]

    metricPairs.forEach(([metric1, metric2]) => {
      const correlation = this._calculateCorrelation(
        metrics.map(m => m[metric1]).filter(Boolean),
        metrics.map(m => m[metric2]).filter(Boolean)
      )

      if (Math.abs(correlation) > 0.5) {
        correlations.push({
          metrics: [metric1, metric2],
          correlation,
          strength: Math.abs(correlation) > 0.7 ? 'strong' : 'moderate',
          direction: correlation > 0 ? 'positive' : 'negative',
          interpretation: this._interpretCorrelation(metric1, metric2, correlation)
        })
      }
    })

    return correlations
  }

  /**
   * Generate cognitive-specific recommendations
   */
  _generateRecommendations (analysis, childProfile) {
    const recommendations = []

    // Attention recommendations
    if (analysis.attention?.overall?.percentile < 25) {
      recommendations.push({
        domain: 'attention',
        priority: 'high',
        intervention: 'Attention training exercises',
        description: 'Implement sustained attention activities with gradual duration increases',
        frequency: 'daily',
        duration: '10-15 minutes'
      })
    }

    // Memory recommendations
    if (analysis.memory?.workingMemory?.averageScore < 50) {
      recommendations.push({
        domain: 'memory',
        priority: 'medium',
        intervention: 'Working memory training',
        description: 'Use visual and auditory memory games to strengthen working memory capacity',
        frequency: '3 times per week',
        duration: '15-20 minutes'
      })
    }

    // Executive function recommendations
    if (analysis.executiveFunction?.flexibility?.level === 'low') {
      recommendations.push({
        domain: 'executive_function',
        priority: 'high',
        intervention: 'Cognitive flexibility training',
        description: 'Practice task-switching and rule-changing activities',
        frequency: 'daily',
        duration: '10 minutes'
      })
    }

    // Processing speed recommendations
    if (analysis.processing?.speed?.level === 'slow') {
      recommendations.push({
        domain: 'processing_speed',
        priority: 'medium',
        intervention: 'Processing speed activities',
        description: 'Simple reaction time tasks with gradual complexity increases',
        frequency: '4 times per week',
        duration: '10-15 minutes'
      })
    }

    return recommendations
  }

  /**
   * Analisa padrões cognitivos baseados em dados de jogos
   * @param {Object} data - Dados para análise
   * @returns {Object} Resultado da análise cognitiva
   */
  async analyzeCognitivePatterns(data) {
    try {
      this.logger.info('🧠 Iniciando análise de padrões cognitivos', {
        gameData: !!data.gameData,
        collectorsData: !!data.collectorsData
      });

      const startTime = Date.now();
      const analysisResult = {
        timestamp: new Date().toISOString(),
        gameType: data.gameData?.gameType || 'unknown',
        sessionId: data.gameData?.sessionId || 'unknown',
        metrics: {},
        patterns: [],
        insights: [],
        recommendations: [],
        confidence: 0
      };

      // Analisar domínios cognitivos baseados nos dados coletados
      if (data.collectorsData) {
        analysisResult.metrics = this.extractCognitiveMetrics(data.collectorsData);
        analysisResult.patterns = this.identifyCognitivePatterns(analysisResult.metrics);
        analysisResult.insights = this.generateCognitiveInsights(analysisResult.patterns);
        analysisResult.recommendations = this.generateCognitiveRecommendations(analysisResult.insights);
        analysisResult.confidence = this.calculateAnalysisConfidence(analysisResult);
      }

      const duration = Date.now() - startTime;
      this.logger.info('🧠 Análise de padrões cognitivos concluída', {
        duration: `${duration}ms`,
        metricsCount: Object.keys(analysisResult.metrics).length,
        patternsCount: analysisResult.patterns.length
      });

      return analysisResult;

    } catch (error) {
      this.logger.error('🧠 Erro na análise de padrões cognitivos:', error);
      throw error;
    }
  }

  /**
   * Extrai métricas cognitivas dos dados coletados
   */
  extractCognitiveMetrics(collectorsData) {
    const metrics = {
      attentionSpan: 0,
      memoryPerformance: 0,
      processingSpeed: 0,
      visualPerception: 0,
      motorSkills: 0,
      logicalReasoning: 0
    };

    try {
      // Processar diferentes tipos de métricas baseadas nos coletores
      Object.keys(collectorsData).forEach(metricKey => {
        const metricValue = collectorsData[metricKey];
        
        if (typeof metricValue === 'number') {
          // Mapear métricas para domínios cognitivos
          if (metricKey.includes('attention') || metricKey.includes('focus')) {
            metrics.attentionSpan = Math.max(metrics.attentionSpan, metricValue);
          } else if (metricKey.includes('memory') || metricKey.includes('recall')) {
            metrics.memoryPerformance = Math.max(metrics.memoryPerformance, metricValue);
          } else if (metricKey.includes('speed') || metricKey.includes('response')) {
            metrics.processingSpeed = Math.max(metrics.processingSpeed, 1 - (metricValue / 5000)); // Converter tempo em score
          } else if (metricKey.includes('visual') || metricKey.includes('perception')) {
            metrics.visualPerception = Math.max(metrics.visualPerception, metricValue);
          } else if (metricKey.includes('motor') || metricKey.includes('coordination')) {
            metrics.motorSkills = Math.max(metrics.motorSkills, metricValue);
          } else if (metricKey.includes('logic') || metricKey.includes('reasoning')) {
            metrics.logicalReasoning = Math.max(metrics.logicalReasoning, metricValue);
          }
        }
      });

      // Normalizar métricas (0-1)
      Object.keys(metrics).forEach(key => {
        metrics[key] = Math.max(0, Math.min(1, metrics[key]));
      });

    } catch (error) {
      this.logger.error('🧠 Erro ao extrair métricas cognitivas:', error);
    }

    return metrics;
  }

  /**
   * Identifica padrões cognitivos
   */
  identifyCognitivePatterns(metrics) {
    const patterns = [];

    try {
      // Padrão de forças cognitivas
      const strengths = Object.entries(metrics)
        .filter(([_, value]) => value > 0.7)
        .map(([domain, value]) => ({ domain, strength: value }));

      if (strengths.length > 0) {
        patterns.push({
          type: 'cognitive_strengths',
          data: strengths,
          description: 'Domínios cognitivos com desempenho superior'
        });
      }

      // Padrão de áreas de desenvolvimento
      const developmentAreas = Object.entries(metrics)
        .filter(([_, value]) => value < 0.4)
        .map(([domain, value]) => ({ domain, needsImprovement: 1 - value }));

      if (developmentAreas.length > 0) {
        patterns.push({
          type: 'development_areas',
          data: developmentAreas,
          description: 'Domínios que necessitam desenvolvimento'
        });
      }

      // Padrão de equilíbrio cognitivo
      const variance = this.calculateVariance(Object.values(metrics));
      patterns.push({
        type: 'cognitive_balance',
        data: { variance, isBalanced: variance < 0.2 },
        description: variance < 0.2 ? 'Perfil cognitivo equilibrado' : 'Perfil cognitivo com disparidades'
      });

    } catch (error) {
      this.logger.error('🧠 Erro ao identificar padrões cognitivos:', error);
    }

    return patterns;
  }

  /**
   * Gera insights cognitivos
   */
  generateCognitiveInsights(patterns) {
    const insights = [];

    try {
      patterns.forEach(pattern => {
        switch (pattern.type) {
          case 'cognitive_strengths':
            insights.push(`Forças identificadas em: ${pattern.data.map(s => s.domain).join(', ')}`);
            break;
          case 'development_areas':
            insights.push(`Áreas para desenvolvimento: ${pattern.data.map(d => d.domain).join(', ')}`);
            break;
          case 'cognitive_balance':
            insights.push(pattern.description);
            break;
        }
      });

      if (insights.length === 0) {
        insights.push('Perfil cognitivo dentro dos parâmetros esperados');
      }

    } catch (error) {
      this.logger.error('🧠 Erro ao gerar insights cognitivos:', error);
    }

    return insights;
  }

  /**
   * Gera recomendações cognitivas
   */
  generateCognitiveRecommendations(insights) {
    const recommendations = [];

    try {
      insights.forEach(insight => {
        if (insight.includes('desenvolvimento')) {
          recommendations.push('Implementar exercícios específicos para áreas identificadas');
        } else if (insight.includes('Forças')) {
          recommendations.push('Manter e reforçar as competências já desenvolvidas');
        } else if (insight.includes('disparidades')) {
          recommendations.push('Trabalhar no nivelamento do perfil cognitivo');
        }
      });

      if (recommendations.length === 0) {
        recommendations.push('Continuar com a programação terapêutica atual');
      }

    } catch (error) {
      this.logger.error('🧠 Erro ao gerar recomendações cognitivas:', error);
    }

    return recommendations;
  }

  /**
   * Atualiza métricas do analisador
   * @param {number} analysisTime - Tempo de análise em ms
   * @private
   */
  updateMetrics(analysisTime) {
    const currentAvg = this.metrics.averageAnalysisTime;
    const count = this.metrics.totalAnalyses;
    
    this.metrics.averageAnalysisTime = 
      ((currentAvg * (count - 1)) + analysisTime) / count;
  }

  /**
   * Atualiza métricas de domínios
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @private
   */
  updateDomainMetrics(cognitiveAnalysis) {
    Object.keys(cognitiveAnalysis).forEach(domain => {
      if (!this.metrics.domainAnalyses[domain]) {
        this.metrics.domainAnalyses[domain] = 0;
      }
      this.metrics.domainAnalyses[domain]++;
    });
  }

  /**
   * Identifica domínios fortes
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Array} Domínios com performance alta
   */
  getStrongDomains(cognitiveAnalysis) {
    return Object.entries(cognitiveAnalysis)
      .filter(([_, analysis]) => analysis && analysis.score > 0.7)
      .map(([domain, _]) => domain);
  }

  /**
   * Identifica domínios fracos
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Array} Domínios com performance baixa
   */
  getWeakDomains(cognitiveAnalysis) {
    return Object.entries(cognitiveAnalysis)
      .filter(([_, analysis]) => analysis && analysis.score < 0.4)
      .map(([domain, _]) => domain);
  }

  /**
   * Análise do domínio de atenção
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise do domínio de atenção
   */
  async analyzeAttentionDomain(gameSession) {
    const interactions = gameSession.interactions || [];
    const focusEvents = interactions.filter(i => i.type === 'focus' || i.type === 'engagement');
    const totalDuration = gameSession.duration || 0;
    
    const focusTime = focusEvents.reduce((sum, event) => sum + (event.duration || 0), 0);
    const attentionScore = totalDuration > 0 ? (focusTime / totalDuration) : 0;

    return {
      score: Math.min(1, attentionScore),
      focusTime,
      sustainedAttention: attentionScore > 0.7,
      patterns: focusEvents.length > 3 ? ['consistent_focus'] : ['variable_focus']
    };
  }

  /**
   * Análise do domínio de memória
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise do domínio de memória
   */
  async analyzeMemoryDomain(gameSession) {
    const accuracy = gameSession.accuracy || 0;
    const interactions = gameSession.interactions || [];
    const memoryTasks = interactions.filter(i => i.type === 'memory' || i.type === 'recall');

    return {
      score: accuracy,
      workingMemoryCapacity: memoryTasks.length,
      retention: accuracy > 0.8,
      patterns: accuracy > 0.7 ? ['good_memory'] : ['memory_challenges']
    };
  }

  /**
   * Análise do domínio executivo
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise da função executiva
   */
  async analyzeExecutiveDomain(gameSession) {
    const interactions = gameSession.interactions || [];
    const planningActions = interactions.filter(i => i.type === 'planning' || i.type === 'strategy');
    const flexibility = interactions.filter(i => i.type === 'adaptation').length;

    return {
      score: planningActions.length > 0 ? 0.8 : 0.4,
      planning: planningActions.length,
      flexibility: flexibility > 2,
      patterns: planningActions.length > 3 ? ['good_planning'] : ['needs_structure']
    };
  }

  /**
   * Análise do domínio de processamento
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise da velocidade de processamento
   */
  async analyzeProcessingDomain(gameSession) {
    const responseTime = gameSession.responseTime || 1000;
    const processingScore = Math.max(0, 1 - (responseTime / 3000)); // Normalizar para 3 segundos

    return {
      score: processingScore,
      averageResponseTime: responseTime,
      efficiency: processingScore > 0.6,
      patterns: processingScore > 0.7 ? ['fast_processing'] : ['slow_processing']
    };
  }

  /**
   * Análise do domínio de linguagem
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise das habilidades de linguagem
   */
  async analyzeLanguageDomain(gameSession) {
    const interactions = gameSession.interactions || [];
    const verbalInteractions = interactions.filter(i => i.type === 'verbal' || i.type === 'communication');

    return {
      score: verbalInteractions.length > 0 ? 0.7 : 0.5,
      verbalInteractions: verbalInteractions.length,
      comprehension: verbalInteractions.length > 2,
      patterns: verbalInteractions.length > 3 ? ['good_language'] : ['language_support_needed']
    };
  }

  /**
   * Análise do domínio visuoespacial
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise das habilidades visuoespaciais
   */
  async analyzeVisuoSpatialDomain(gameSession) {
    const interactions = gameSession.interactions || [];
    const spatialTasks = interactions.filter(i => i.type === 'spatial' || i.type === 'visual');
    const accuracy = gameSession.accuracy || 0;

    return {
      score: spatialTasks.length > 0 ? accuracy : 0.6,
      spatialTasks: spatialTasks.length,
      visualProcessing: accuracy > 0.7,
      patterns: spatialTasks.length > 2 ? ['good_spatial'] : ['spatial_challenges']
    };
  }

  /**
   * Análise da cognição social
   * @param {Object} gameSession - Sessão de jogo
   * @returns {Object} Análise da cognição social
   */
  async analyzeSocialCognitionDomain(gameSession) {
    const interactions = gameSession.interactions || [];
    const socialInteractions = interactions.filter(i => i.type === 'social' || i.type === 'cooperation');

    return {
      score: socialInteractions.length > 0 ? 0.7 : 0.4,
      socialInteractions: socialInteractions.length,
      theoryOfMind: socialInteractions.length > 1,
      patterns: socialInteractions.length > 2 ? ['good_social'] : ['social_support_needed']
    };
  }

  /**
   * Calcula score cognitivo geral
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {number} Score geral
   */
  calculateOverallCognitiveScore(cognitiveAnalysis) {
    const scores = Object.values(cognitiveAnalysis)
      .map(domain => domain.score)
      .filter(score => typeof score === 'number');
    
    return scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;
  }

  /**
   * Gera perfil cognitivo
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Object} Perfil cognitivo
   */
  generateCognitiveProfile(cognitiveAnalysis) {
    const strongDomains = this.getStrongDomains(cognitiveAnalysis);
    const weakDomains = this.getWeakDomains(cognitiveAnalysis);

    return {
      strengths: strongDomains,
      challenges: weakDomains,
      profile: strongDomains.length > weakDomains.length ? 'balanced' : 'heterogeneous'
    };
  }

  /**
   * Identifica padrões cognitivos da análise
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Array} Padrões identificados
   */
  identifyCognitivePatterns(cognitiveAnalysis) {
    const patterns = [];
    
    Object.entries(cognitiveAnalysis).forEach(([domain, analysis]) => {
      if (analysis.patterns) {
        patterns.push(...analysis.patterns.map(pattern => ({
          domain,
          pattern,
          score: analysis.score
        })));
      }
    });

    return patterns;
  }

  /**
   * Gera recomendações cognitivas da análise
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Array} Recomendações
   */
  generateCognitiveRecommendations(cognitiveAnalysis) {
    const recommendations = [];
    const weakDomains = this.getWeakDomains(cognitiveAnalysis);

    weakDomains.forEach(domain => {
      recommendations.push({
        domain,
        priority: 'medium',
        suggestion: `Fortalecer habilidades de ${domain}`,
        strategies: [`Exercícios específicos para ${domain}`, 'Prática regular', 'Monitoramento do progresso']
      });
    });

    if (recommendations.length === 0) {
      recommendations.push({
        domain: 'geral',
        priority: 'low',
        suggestion: 'Manter o desenvolvimento cognitivo atual',
        strategies: ['Continuar atividades variadas', 'Monitoramento regular']
      });
    }

    return recommendations;
  }

  /**
   * Calcula confiança da análise cognitiva
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {number} Confiança da análise
   */
  calculateAnalysisConfidence(cognitiveAnalysis) {
    const domainCount = Object.keys(cognitiveAnalysis).length;
    const validScores = Object.values(cognitiveAnalysis)
      .filter(domain => domain && typeof domain.score === 'number').length;
    
    return domainCount > 0 ? (validScores / domainCount) * 0.8 + 0.2 : 0.5;
  }

  /**
   * Compara com dados normativos
   * @param {Object} gameSession - Sessão de jogo
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @returns {Object} Comparação normativa
   */
  compareToNormativeData(gameSession, cognitiveAnalysis) {
    const childAge = gameSession.childAge || 6; // Idade padrão
    const ageGroup = this._getAgeGroup(childAge);
    const norms = this.normativeData.ageGroups[ageGroup];

    if (!norms) {
      return { status: 'no_normative_data' };
    }

    const overallScore = this.calculateOverallCognitiveScore(cognitiveAnalysis);
    const percentile = this._calculatePercentile(overallScore * 100, childAge, 'processingSpeed') || 50;

    return {
      ageGroup,
      percentile,
      comparison: percentile > 50 ? 'above_average' : percentile < 25 ? 'below_average' : 'average',
      normativeScore: overallScore
    };
  }

  /**
   * Determina o grupo etário baseado na idade
   * @param {number} age - Idade da criança
   * @returns {string} Grupo etário
   * @private
   */
  _getAgeGroup(age) {
    if (age <= 4) return '3-4';
    if (age <= 6) return '5-6';
    if (age <= 8) return '7-8';
    return '9-12';
  }

  /**
   * Calcula período analisado
   * @param {Array} metricsData - Dados das métricas
   * @returns {Object} Período analisado
   * @private
   */
  _calculatePeriod(metricsData) {
    if (!metricsData || metricsData.length === 0) {
      return { start: null, end: null, duration: 0 };
    }

    const timestamps = metricsData.map(m => new Date(m.timestamp || Date.now()));
    const start = new Date(Math.min(...timestamps));
    const end = new Date(Math.max(...timestamps));
    
    return {
      start: start.toISOString(),
      end: end.toISOString(),
      duration: end - start,
      sessionsCount: metricsData.length
    };
  }

  /**
   * Calcula média de um array de valores
   * @param {Array} values - Array de valores numéricos
   * @returns {number} Média
   * @private
   */
  _calculateMean(values) {
    if (!values || values.length === 0) return 0;
    const validValues = values.filter(v => typeof v === 'number' && !isNaN(v));
    return validValues.length > 0 ? validValues.reduce((sum, val) => sum + val, 0) / validValues.length : 0;
  }

  /**
   * Calcula consistência de um array de valores
   * @param {Array} values - Array de valores numéricos
   * @returns {number} Consistência (0-1)
   * @private
   */
  _calculateConsistency(values) {
    if (!values || values.length === 0) return 0;
    const mean = this._calculateMean(values);
    const variance = this.calculateVariance(values);
    return mean > 0 ? Math.max(0, 1 - (Math.sqrt(variance) / mean)) : 0;
  }

  /**
   * Calcula tendência de um array de valores
   * @param {Array} values - Array de valores numéricos
   * @returns {number} Tendência (slope)
   * @private
   */
  _calculateTrend(values) {
    if (!values || values.length < 2) return 0;
    
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const meanX = this._calculateMean(x);
    const meanY = this._calculateMean(values);
    
    const numerator = x.reduce((sum, xi, i) => sum + (xi - meanX) * (values[i] - meanY), 0);
    const denominator = x.reduce((sum, xi) => sum + Math.pow(xi - meanX, 2), 0);
    
    return denominator !== 0 ? numerator / denominator : 0;
  }

  /**
   * Calcula percentil baseado em dados normativos
   * @param {number} value - Valor a ser comparado
   * @param {number} age - Idade da criança
   * @param {string} metric - Métrica sendo avaliada
   * @returns {number} Percentil (0-100)
   * @private
   */
  _calculatePercentile(value, age, metric) {
    const ageGroup = this._getAgeGroup(age);
    const norms = this.normativeData.ageGroups[ageGroup];
    
    if (!norms || !norms[metric]) return 50; // Percentil médio se não há dados
    
    const { mean, std } = norms[metric];
    const zScore = (value - mean) / std;
    
    // Conversão aproximada de z-score para percentil
    return Math.round(Math.max(0, Math.min(100, 50 + (zScore * 34.13))));
  }

  /**
   * Calcula variância de um array de valores
   * @param {Array} values - Array de valores numéricos
   * @returns {number} Variância
   */
  calculateVariance(values) {
    if (!values || values.length === 0) return 0;
    const mean = this._calculateMean(values);
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return this._calculateMean(squaredDiffs);
  }

  /**
   * Calcula melhoria baseada em tendência
   * @param {Array} values - Array de valores numéricos
   * @returns {Object} Informações sobre melhoria
   * @private
   */
  _calculateImprovement(values) {
    const trend = this._calculateTrend(values);
    return {
      trend,
      improving: trend > 0,
      rate: Math.abs(trend),
      confidence: values.length >= 5 ? 'high' : 'medium'
    };
  }

  /**
   * Calcula variabilidade de um array de valores
   * @param {Array} values - Array de valores numéricos
   * @returns {number} Coeficiente de variação
   * @private
   */
  _calculateVariability(values) {
    const mean = this._calculateMean(values);
    const variance = this.calculateVariance(values);
    return mean > 0 ? Math.sqrt(variance) / mean : 0;
  }

  /**
   * Calcula correlação entre dois arrays de valores
   * @param {Array} x - Primeiro array de valores
   * @param {Array} y - Segundo array de valores
   * @returns {number} Coeficiente de correlação (-1 a 1)
   * @private
   */
  _calculateCorrelation(x, y) {
    if (!x || !y || x.length !== y.length || x.length < 2) return 0;
    
    const meanX = this._calculateMean(x);
    const meanY = this._calculateMean(y);
    
    let numerator = 0;
    let sumXSquared = 0;
    let sumYSquared = 0;
    
    for (let i = 0; i < x.length; i++) {
      const deltaX = x[i] - meanX;
      const deltaY = y[i] - meanY;
      numerator += deltaX * deltaY;
      sumXSquared += deltaX * deltaX;
      sumYSquared += deltaY * deltaY;
    }
    
    const denominator = Math.sqrt(sumXSquared * sumYSquared);
    return denominator !== 0 ? numerator / denominator : 0;
  }
  
  // ...existing code...
  _synthesizeProfile (analysis) { return {} }
  _assessTrajectory (analysis, metricsData) { return {} }
  _identifyInterventionTargets (analysis) { return [] }
  _assessDistractorResistance (attentionData) { return 0 }
  _assessTargetFocus (attentionData) { return 0 }
  _estimateMultitasking (attentionData) { return 0 }
  _estimateTaskSwitching (attentionData) { return 0 }
  _analyzeShortTermMemory (memoryData) { return {} }
  _analyzeLongTermMemory (memoryData) { return {} }
  _synthesizeMemoryProfile (memoryData) { return {} }
  _identifyMemoryPatterns (memoryData) { return [] }
  _estimateCapacity (average) { return 0 }
  _estimateEfficiency (scores) { return 0 }
  _analyzeInhibition (executiveData) { return {} }
  _analyzeFlexibility (executiveData) { return {} }
  _analyzePlanning (executiveData) { return {} }
  _synthesizeExecutiveProfile (executiveData) { return {} }
  _identifyExecutivePatterns (metricsData) { return [] }
  _identifyTemporalPatterns (metricsData) { return [] }
  _analyzeProcessingSpeed (processingData) { return {} }
  _analyzeProcessingAccuracy (processingData) { return {} }
  _analyzeProcessingEfficiency (processingData) { return {} }
  _synthesizeProcessingProfile (processingData) { return {} }
  _calculateTrendConfidence (values) { return 0 }
  _interpretCorrelation (metric1, metric2, correlation) { return '' }

  /**
   * Gera hash da sessão para cache
   * @param {Object} gameSession - Sessão de jogo
   * @returns {string} Hash da sessão
   * @private
   */
  generateSessionHash(gameSession) {
    const relevantData = {
      duration: Math.round((gameSession.duration || 0) / 1000),
      interactions: (gameSession.interactions || []).length,
      accuracy: Math.round((gameSession.accuracy || 0) * 100) / 100
    };
    return JSON.stringify(relevantData).replace(/[{}":]/g, '').slice(0, 32);
  }

  /**
   * Retorna métricas do analisador para health checks
   * @returns {Object} Métricas do analisador
   */
  getMetrics() {
    return {
      totalAssessments: this.metrics.totalAnalyses,
      cacheHits: this.metrics.cacheHits,
      cacheMisses: this.metrics.cacheMisses,
      cacheHitRate: this.metrics.totalAnalyses > 0 ? 
        (this.metrics.cacheHits / this.metrics.totalAnalyses) * 100 : 0,
      averageAnalysisTime: this.metrics.averageAnalysisTime,
      domainsAnalyzed: Object.keys(this.metrics.domainAnalyses || {}).length,
      lastAssessmentTime: this.metrics.lastAssessmentTime || null,
      cacheMetrics: this.cache.getMetrics()
    };
  }
}

// Instância singleton do CognitiveAnalyzer
let cognitiveAnalyzerInstance = null;

/**
 * Retorna a instância singleton do CognitiveAnalyzer
 * @param {Object} options - Opções de configuração
 * @returns {CognitiveAnalyzer} Instância do CognitiveAnalyzer
 */
export const getCognitiveAnalyzer = (options = {}) => {
  if (!cognitiveAnalyzerInstance) {
    cognitiveAnalyzerInstance = new CognitiveAnalyzer(options);
  }
  return cognitiveAnalyzerInstance;
};

// Exportação padrão para importação direta
export default CognitiveAnalyzer;
