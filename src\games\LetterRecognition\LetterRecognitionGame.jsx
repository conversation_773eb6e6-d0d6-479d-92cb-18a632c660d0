/**
 * 📚 LETTER RECOGNITION GAME - Portal Betina V3
 * Layout baseado no padrão ColorMatch
 */

import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { LetterRecognitionMetrics } from './LetterRecognitionMetrics.js';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// 📚 Importar coletores avançados de reconhecimento de letras
import { LetterRecognitionCollectorsHub } from './collectors/index.js';
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// Importa estilos modulares
import styles from './LetterRecognition.module.css';

// Configurações do jogo
const LETTERS = [
  { id: 'a', letter: 'A', sound: 'A', example: '🐝 Abelha', color: '#FF6B6B' },
  { id: 'b', letter: 'B', sound: 'Bê', example: '⚽ Bola', color: '#4ECDC4' },
  { id: 'c', letter: 'C', sound: 'Cê', example: '🏠 Casa', color: '#45B7D1' },
  { id: 'd', letter: 'D', sound: 'Dê', example: '🎲 Dado', color: '#FFA07A' },
  { id: 'e', letter: 'E', sound: 'É', example: '⭐ Estrela', color: '#98D8C8' },
  { id: 'f', letter: 'F', sound: 'Efe', example: '🌸 Flor', color: '#F7DC6F' },
  { id: 'g', letter: 'G', sound: 'Gê', example: '� Gato', color: '#BB8FCE' },
  { id: 'h', letter: 'H', sound: 'Agá', example: '� Hotel', color: '#85C1E9' }
];

const DIFFICULTIES = {
  EASY: { letters: LETTERS.slice(0, 4), name: 'Fácil' },
  MEDIUM: { letters: LETTERS.slice(0, 6), name: 'Médio' },
  HARD: { letters: LETTERS, name: 'Avançado' }
};

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3
const ACTIVITY_TYPES = {
  LETTER_SELECTION: {
    id: 'letter_selection',
    name: 'Seleção de Letras',
    icon: '🔤',
    description: 'Encontre a letra correta baseada no som e exemplo',
    component: 'LetterSelectionActivity'
  },
  SOUND_MATCHING: {
    id: 'sound_matching',
    name: 'Combinação de Sons',
    icon: '🎵',
    description: 'Associe o som à letra correspondente',
    component: 'SoundMatchingActivity'
  },
  WORD_FORMATION: {
    id: 'word_formation',
    name: 'Formação de Palavras',
    icon: '🔗',
    description: 'Monte palavras usando as letras aprendidas',
    component: 'WordFormationActivity'
  },
  SEQUENCE_RECOGNITION: {
    id: 'sequence_recognition',
    name: 'Reconhecimento de Sequência',
    icon: '📝',
    description: 'Complete sequências alfabéticas',
    component: 'SequenceRecognitionActivity'
  },
  PHONETIC_DISCRIMINATION: {
    id: 'phonetic_discrimination',
    name: 'Discriminação Fonética',
    icon: '🎧',
    description: 'Diferencie sons similares',
    component: 'PhoneticDiscriminationActivity'
  },
  VISUAL_DISCRIMINATION: {
    id: 'visual_discrimination',
    name: 'Discriminação Visual',
    icon: '👁️',
    description: 'Identifique letras com formas similares',
    component: 'VisualDiscriminationActivity'
  }
};

// 🎯 Configuração de palavras para formação
const WORD_BANK = {
  EASY: [
    { word: 'MAMA', emoji: '👩', meaning: 'Mamãe' },
    { word: 'BABA', emoji: '👶', meaning: 'Bebê' },
    { word: 'CACA', emoji: '💩', meaning: 'Cocô' },
    { word: 'DADA', emoji: '🎁', meaning: 'Presente' }
  ],
  MEDIUM: [
    { word: 'BEBE', emoji: '🍼', meaning: 'Bebê' },
    { word: 'FADA', emoji: '🧚', meaning: 'Fada' },
    { word: 'CAFE', emoji: '☕', meaning: 'Café' },
    { word: 'FACE', emoji: '😊', meaning: 'Rosto' }
  ],
  HARD: [
    { word: 'GATO', emoji: '🐱', meaning: 'Gato' },
    { word: 'FOGO', emoji: '🔥', meaning: 'Fogo' },
    { word: 'CASA', emoji: '🏠', meaning: 'Casa' },
    { word: 'BEIJO', emoji: '💋', meaning: 'Beijo' }
  ]
};

// 🎯 Sequências alfabéticas para reconhecimento - EXPANDIDO
const ALPHABET_SEQUENCES = [
  // Sequências simples consecutivas
  { sequence: ['A', 'B', 'C'], missing: 'D', options: ['D', 'E', 'F'] },
  { sequence: ['B', 'C', 'D'], missing: 'E', options: ['E', 'F', 'G'] },
  { sequence: ['C', 'D', 'E'], missing: 'F', options: ['F', 'G', 'H'] },
  { sequence: ['D', 'E', 'F'], missing: 'G', options: ['G', 'H', 'I'] },
  { sequence: ['E', 'F', 'G'], missing: 'H', options: ['H', 'I', 'J'] },
  
  // Sequências saltando uma letra
  { sequence: ['A', 'C', 'E'], missing: 'G', options: ['G', 'F', 'H'] },
  { sequence: ['B', 'D', 'F'], missing: 'H', options: ['H', 'G', 'I'] },
  { sequence: ['C', 'E', 'G'], missing: 'I', options: ['I', 'H', 'J'] },
  
  // Sequências crescentes
  { sequence: ['M', 'N', 'O'], missing: 'P', options: ['P', 'Q', 'R'] },
  { sequence: ['X', 'Y', 'Z'], missing: 'W', options: ['W', 'V', 'U'] },
  
  // Sequências no meio do alfabeto
  { sequence: ['J', 'K', 'L'], missing: 'M', options: ['M', 'N', 'O'] },
  { sequence: ['P', 'Q', 'R'], missing: 'S', options: ['S', 'T', 'U'] }
];

// 🎯 Pares para discriminação fonética
const PHONETIC_PAIRS = [
  { target: 'B', distractors: ['D', 'P'], sounds: ['/ba/', '/da/', '/pa/'] },
  { target: 'D', distractors: ['B', 'T'], sounds: ['/da/', '/ba/', '/ta/'] },
  { target: 'P', distractors: ['B', 'T'], sounds: ['/pa/', '/ba/', '/ta/'] },
  { target: 'M', distractors: ['N', 'W'], sounds: ['/ma/', '/na/', '/wa/'] }
];

// 🎯 Grupos para discriminação visual
const VISUAL_GROUPS = [
  { target: 'b', distractors: ['d', 'p', 'q'], description: 'Letras com círculo' },
  { target: 'n', distractors: ['m', 'h', 'u'], description: 'Letras curvas' },
  { target: 'E', distractors: ['F', 'L', 'T'], description: 'Letras com linhas' },
  { target: 'O', distractors: ['Q', 'C', 'G'], description: 'Letras redondas' }
];

function LetterRecognitionGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const metricsRef = useRef(new LetterRecognitionMetrics());
  const sessionIdRef = useRef(uuidv4());

  // 📚 Inicializar coletores avançados de reconhecimento de letras
  const [collectorsHub] = useState(() => new LetterRecognitionCollectorsHub())

  // 🔄 Hook multissensorial integrado
  const multisensoryIntegration = useMultisensoryIntegration('letter-recognition', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    userId: sessionIdRef.current,
    enableAdvancedMetrics: true,
    enableRealTimeAnalysis: true,
    enableNeurodivergenceSupport: true
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    userId: user?.id || 'anonymous'
  });

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO
  // =====================================================
  
  // Estado do TTS com persistência
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('letterRecognition_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });
  
  // Função para alternar TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('letterRecognition_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }
    
    // Cancelar qualquer fala anterior
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('letter_recognition');

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'menu',
    score: 0,
    round: 1,
    targetLetter: null,
    selectedLetter: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'EASY',
    availableLetters: DIFFICULTIES.EASY.letters,
    allLetters: DIFFICULTIES.EASY.letters,
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
    activityCycle: [
      ACTIVITY_TYPES.LETTER_SELECTION.id,
      ACTIVITY_TYPES.SOUND_MATCHING.id,
      ACTIVITY_TYPES.WORD_FORMATION.id,
      ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id,
      ACTIVITY_TYPES.PHONETIC_DISCRIMINATION.id,
      ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 2, // Mínimo 2 rodadas por atividade antes de permitir troca
    activityRoundCount: 2, // Começar com 2 para permitir troca imediata
    activitiesCompleted: [], // Lista de atividades já completadas
    
    // 🎯 Dados específicos de atividades
    activityData: {
      wordFormation: {
        currentWord: null,
        placedLetters: [],
        availableLetters: []
      },
      sequenceRecognition: {
        currentSequence: null,
        userProgress: []
      },
      phoneticDiscrimination: {
        currentPair: null,
        playedSounds: []
      },
      visualDiscrimination: {
        currentGroup: null,
        foundTargets: 0,
        totalTargets: 0
      }
    },
    
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1.0
    }
  });
  
  const [showStartScreen, setShowStartScreen] = useState(true);

  // 🎯 SISTEMA DE ROTAÇÃO DE ATIVIDADES
  const rotateActivity = useCallback(() => {
    setGameState(prev => {
      const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextActivityIndex];
      
      // 🔊 Anunciar nova atividade
      const activityName = ACTIVITY_TYPES[nextActivity.toUpperCase()]?.name || 'Nova Atividade';
      speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });
      
      return {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextActivityIndex,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false
      };
    });
  }, [speak]);

  // 🎯 Gerador de conteúdo específico por atividade - MELHORADO
  const generateActivityContent = useCallback((activity, difficulty) => {
    const difficultyConfig = DIFFICULTIES[difficulty] || DIFFICULTIES.EASY;
    
    switch (activity) {
      case 'sound_matching':
        // Garantir que sempre gere uma letra diferente da anterior
        let targetLetter;
        do {
          targetLetter = difficultyConfig.letters[Math.floor(Math.random() * difficultyConfig.letters.length)];
        } while (targetLetter === gameState.targetLetter);
        
        // Criar opções com a letra alvo + 2 distrativas
        const soundOptions = [targetLetter];
        const otherLetters = difficultyConfig.letters.filter(l => l !== targetLetter);
        soundOptions.push(...shuffleArray(otherLetters).slice(0, 2));
        
        return {
          targetLetter,
          options: shuffleArray(soundOptions)
        };
        
      case 'word_formation':
        const wordBank = WORD_BANK[difficulty] || WORD_BANK.EASY;
        const selectedWord = wordBank[Math.floor(Math.random() * wordBank.length)];
        
        const wordLetters = selectedWord.word.split('');
        const distractors = generateDistracters(wordLetters, 2);
        const availableLetters = shuffleArray([...wordLetters, ...distractors]);
        
        console.log('📝 Generated word formation content:', {
          selectedWord,
          wordLetters,
          distractors,
          availableLetters
        });
        
        return {
          currentWord: selectedWord,
          availableLetters,
          placedLetters: new Array(selectedWord.word.length).fill(null)
        };
        
      case 'sequence_recognition':
        const selectedSequence = ALPHABET_SEQUENCES[Math.floor(Math.random() * ALPHABET_SEQUENCES.length)];
        
        return {
          currentSequence: selectedSequence
        };
        
      case 'phonetic_discrimination':
        const selectedPair = PHONETIC_PAIRS[Math.floor(Math.random() * PHONETIC_PAIRS.length)];
        
        return {
          currentPair: selectedPair
        };
        
      case 'visual_discrimination':
        const selectedGroup = VISUAL_GROUPS[Math.floor(Math.random() * VISUAL_GROUPS.length)];
        
        return {
          currentGroup: selectedGroup,
          foundTargets: 0,
          totalTargets: 3, // Sempre 3 alvos para encontrar
          selectedItems: [] // Array para rastrear itens selecionados
        };
        
      default: // letter_selection
        // Garantir letra diferente da anterior
        let newTargetLetter;
        do {
          newTargetLetter = difficultyConfig.letters[Math.floor(Math.random() * difficultyConfig.letters.length)];
        } while (newTargetLetter === gameState.targetLetter);
        
        // Criar opções com a letra alvo + 2 distrativas
        const letterOptions = [newTargetLetter];
        const otherAvailable = difficultyConfig.letters.filter(l => l !== newTargetLetter);
        letterOptions.push(...shuffleArray(otherAvailable).slice(0, 2));
        
        return {
          targetLetter: newTargetLetter,
          options: shuffleArray(letterOptions)
        };
    }
  }, []);

  // 🎯 Função auxiliar para embaralhar arrays
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  // 🎯 Função auxiliar para gerar distradores
  function generateDistracters(targetLetters, count) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const available = allLetters.filter(letter => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }

  // �🎮 FUNÇÃO PARA MUDAR ATIVIDADE MANUALMENTE (NOVA)
  const changeActivity = useCallback((activityId) => {
    setGameState(prev => {
  // 🎮 FUNÇÃO SIMPLIFICADA PARA MUDAR ATIVIDADE
  const changeActivity = useCallback((activityId) => {
    console.log('🎯 Changing activity to:', activityId);
    
    setGameState(prev => {
      console.log('🔄 Current state before change:', { 
        currentActivity: prev.currentActivity, 
        targetActivity: activityId 
      });
      
      // Gerar novo conteúdo para a atividade
      const difficultyConfig = DIFFICULTIES[prev.difficulty] || DIFFICULTIES.EASY;
      const newTargetLetter = difficultyConfig.letters[Math.floor(Math.random() * difficultyConfig.letters.length)];
      const newOptions = shuffleArray([...difficultyConfig.letters]).slice(0, 3);
      
      // Preparar dados específicos da atividade
      let newActivityData = { ...prev.activityData };
      
      if (activityId === 'word_formation') {
        const wordBank = WORD_BANK[prev.difficulty] || WORD_BANK.EASY;
        const selectedWord = wordBank[Math.floor(Math.random() * wordBank.length)];
        const wordLetters = selectedWord.word.split('');
        const distractors = generateDistracters(wordLetters, 2);
        
        newActivityData.wordFormation = {
          currentWord: selectedWord,
          placedLetters: new Array(selectedWord.word.length).fill(null),
          availableLetters: shuffleArray([...wordLetters, ...distractors])
        };
      }
      
      if (activityId === 'sequence_recognition') {
        const selectedSequence = ALPHABET_SEQUENCES[Math.floor(Math.random() * ALPHABET_SEQUENCES.length)];
        newActivityData.sequenceRecognition = {
          currentSequence: selectedSequence,
          userProgress: []
        };
      }
      
      if (activityId === 'phonetic_discrimination') {
        const selectedPair = PHONETIC_PAIRS[Math.floor(Math.random() * PHONETIC_PAIRS.length)];
        newActivityData.phoneticDiscrimination = {
          currentPair: selectedPair,
          playedSounds: []
        };
      }
      
      if (activityId === 'visual_discrimination') {
        const selectedGroup = VISUAL_GROUPS[Math.floor(Math.random() * VISUAL_GROUPS.length)];
        newActivityData.visualDiscrimination = {
          currentGroup: selectedGroup,
          foundTargets: 0,
          totalTargets: 3,
          selectedItems: []
        };
      }
      
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now(),
        targetLetter: newTargetLetter,
        availableLetters: newOptions,
        activityData: newActivityData
      };
      
      console.log('✅ New state after change:', { 
        currentActivity: newState.currentActivity,
        targetLetter: newState.targetLetter,
        activityData: newState.activityData
      });
      
      return newState;
    });
  }, [generateDistracters, shuffleArray]);
    });
  }, [speak, ttsActive]);

  // 🎯 Função auxiliar para embaralhar arrays
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  // 🎯 Função auxiliar para gerar distradores
  function generateDistracters(targetLetters, count) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const available = allLetters.filter(letter => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }

  // 🎯 NOVA FUNÇÃO DE GERAÇÃO DE RODADAS COM SISTEMA DE ATIVIDADES
  const generateNewRound = useCallback(() => {
    console.log('🎯 Gerando nova rodada...', { 
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round 
    });
    
    setGameState(prev => {
      // 🚫 REMOVIDO: Rotação automática de atividades - agora é controlada pelo usuário
      // O usuário precisa completar pelo menos 4 rodadas antes de poder trocar atividade
      
      console.log('🔄 Incrementando rodada da atividade:', { 
        currentActivity: prev.currentActivity,
        activityRoundCount: prev.activityRoundCount + 1,
        roundsPerActivity: prev.roundsPerActivity 
      });
      
      let newState = { 
        ...prev, 
        activityRoundCount: prev.activityRoundCount + 1 // Incrementar contador de rodadas
      };
      
      // Gerar conteúdo específico da atividade (sem rotação automática)
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      console.log('📝 Conteúdo gerado para atividade:', { 
        activity: newState.currentActivity, 
        content: activityContent 
      });
      
      // Atualizar estado baseado na atividade atual
      switch (newState.currentActivity) {
        case 'word_formation':
          newState.activityData.wordFormation = {
            currentWord: activityContent.currentWord,
            placedLetters: activityContent.placedLetters,
            availableLetters: activityContent.availableLetters
          };
          break;
          
        case 'sequence_recognition':
          newState.activityData.sequenceRecognition = {
            currentSequence: activityContent.currentSequence,
            userProgress: []
          };
          break;
          
        case 'phonetic_discrimination':
          newState.activityData.phoneticDiscrimination = {
            currentPair: activityContent.currentPair,
            playedSounds: []
          };
          break;
          
        case 'visual_discrimination':
          newState.activityData.visualDiscrimination = {
            currentGroup: activityContent.currentGroup,
            foundTargets: activityContent.foundTargets,
            totalTargets: activityContent.totalTargets
          };
          break;
          
        default: // letter_selection e sound_matching
          newState.targetLetter = activityContent.targetLetter;
          newState.availableLetters = activityContent.options || 
            shuffleArray([...DIFFICULTIES[newState.difficulty].letters]).slice(0, 3);
      }
      
      return {
        ...newState,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now()
      };
    });
  }, [generateActivityContent, speak]); // Removidas dependências do gameState que causavam loop

  // Inicializar jogo com letra aleatória - CORRIGIDO
  useEffect(() => {
    if (!showStartScreen) {
      // Hub de coletores será usado apenas para coletar dados
      generateNewRound();
    }
    
    // Cleanup TTS ao desmontar
    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [showStartScreen]); // Removido generateNewRound para evitar loop infinito
  // 🎯 HANDLER PRINCIPAL ATUALIZADO PARA SISTEMA DE ATIVIDADES
  const handleLetterSelect = async (letterId) => {
    if (gameState.selectedLetter) return;

    setGameState(prev => ({ ...prev, selectedLetter: letterId }));
    
    const isCorrect = letterId === gameState.targetLetter.id;
    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;

    // 🔊 Pronunciar a letra selecionada primeiro
    speakLetter(letterId);

    // 📚 Coletar dados com sistema V3 expandido
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: 'letter_recognition_v3',
          activityType: gameState.currentActivity,
          difficulty: gameState.difficulty
        });
      }

      // 📚 Registrar com coletores V3 - dados completos
      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: gameState.targetLetter,
        selectedLetter: LETTERS.find(l => l.id === letterId),
        isCorrect,
        responseTime: reactionTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || 'anonymous',
        
        // Dados V3 específicos
        activityType: gameState.currentActivity,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        
        // Dados da atividade atual
        activityData: gameState.activityData,
        
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), reactionTime],
          accuracy: [...(gameState.behavioralMetrics.accuracy || []), isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          currentLevel: gameState.currentLevel,
          totalAttempts: gameState.round,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            // Adicionar métricas específicas baseadas na atividade atual
            ...(gameState.currentActivity === 'word_formation' && {
              wordBuildingTime: reactionTime,
              letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
            }),
            ...(gameState.currentActivity === 'visual_discrimination' && {
              visualScanTime: reactionTime,
              targetDetectionRate: gameState.activityData?.detectionRate || 0
            }),
            ...(gameState.currentActivity === 'sound_matching' && {
              audioProcessingTime: reactionTime,
              phoneticAccuracy: isCorrect ? 1 : 0
            }),
            ...(gameState.currentActivity === 'sequence_recognition' && {
              sequenceCompletionTime: reactionTime,
              patternRecognition: isCorrect ? 1 : 0
            })
          }
        },
        
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: reactionTime > 3000 ? reactionTime - 3000 : 0,
          confidence: reactionTime < 1000 ? 'high' : reactionTime < 3000 ? 'medium' : 'low',
          attemptPattern: gameState.round % 5 === 0 ? 'milestone' : 'regular'
        }
      });
    } catch (error) {
      console.warn('📚 LetterRecognition V3: Erro ao coletar dados:', error);
    }
    
    // 🔄 Registrar interação multissensorial V3
    if (multisensoryIntegration.isInitialized) {
      await multisensoryIntegration.recordInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: {
          targetLetter: gameState.targetLetter.id,
          selectedLetter: letterId,
          isCorrect,
          round: gameState.round,
          score: gameState.score,
          responseTime: reactionTime,
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0
        },
        multisensoryProcessing: {
          linguisticProcessing: { 
            letterRecognition: 0.8, 
            phonologicalAwareness: 0.7, 
            linguisticMemory: 0.7,
            activitySpecificSkill: calculateActivitySpecificSkill(gameState.currentActivity, isCorrect)
          },
          cognitiveProcessing: { 
            accuracy: isCorrect ? 1.0 : 0.0, 
            processingSpeed: reactionTime < 3000 ? 0.8 : 0.5, 
            adaptability: 0.7,
            activityComplexity: getActivityComplexity(gameState.currentActivity)
          },
          behavioralProcessing: { 
            interactionCount: gameState.round, 
            averageResponseTime: reactionTime, 
            consistency: 0.8,
            activityProgression: (gameState.activitiesCompleted?.length || 0) / 6
          }
        }
      });
    }

    // 🎯 Usar handlers universais V3
    if (isCorrect) {
      handleCorrectAnswer();
    } else {
      handleIncorrectAnswer();
    }
  };

  const startGame = async (selectedDifficulty) => {
    const difficultyKey = selectedDifficulty.toUpperCase();
    const difficultyConfig = DIFFICULTIES[difficultyKey];
    
    // Inicializar dados da atividade atual
    const initialActivityContent = generateActivityContent('letter_selection', difficultyKey);
    
    setGameState(prev => ({
      ...prev,
      status: 'playing',
      difficulty: difficultyKey,
      availableLetters: difficultyConfig.letters, // Inicialmente todas as letras da dificuldade
      allLetters: difficultyConfig.letters, // Manter todas as letras para gerar opções
      score: 0,
      round: 1,
      accuracy: 100,
      targetLetter: initialActivityContent.targetLetter,
      roundStartTime: Date.now(),
      // Inicializar dados de todas as atividades
      activityData: {
        ...prev.activityData,
        wordFormation: generateActivityContent('word_formation', difficultyKey),
        sequenceRecognition: generateActivityContent('sequence_recognition', difficultyKey),
        phoneticDiscrimination: generateActivityContent('phonetic_discrimination', difficultyKey),
        visualDiscrimination: generateActivityContent('visual_discrimination', difficultyKey)
      }
    }));
    setShowStartScreen(false);
    
    // 📚 Inicializar coletores de reconhecimento de letras
    try {
      if (collectorsHub && typeof collectorsHub.initializeSession === 'function') {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          difficulty: difficultyKey,
          availableLetters: difficultyConfig.letters.map(l => l.id),
          gameMode: 'letter_recognition'
        });
        console.log('📚 LetterRecognition: Coletores inicializados com sucesso');
      } else {
        console.log('📚 LetterRecognition: Coletores já ativos');
      }
    } catch (error) {
      console.warn('⚠️ LetterRecognition: Erro ao inicializar coletores:', error);
    }

    // 🔄 Inicializar integração multissensorial
    try {
      await multisensoryIntegration.initializeSession(sessionIdRef.current, {
        difficulty: difficultyKey,
        gameMode: 'letter_recognition',
        availableLetters: difficultyConfig.letters.map(l => l.id),
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
    }
    
    // Iniciar sessão unificada Portal Betina V3
    if (portalReady) {
      startUnifiedSession({
        gameType: 'LetterRecognition',
        difficulty: difficultyKey,
        userId: user?.id || 'anonymous'
      });
    }
    
    // TTS automático ao iniciar
    setTimeout(() => {
      speak(`Bem-vindo ao Reconhecimento de Letras! Dificuldade: ${difficultyConfig.name}. Ouça o som da letra e encontre a letra correta. Vamos começar!`, {
        rate: 0.8
      });
    }, 1000);
  };
  const restartGame = () => {
    setGameState({
      status: 'menu',
      score: 0,
      round: 1,
      targetLetter: null,
      selectedLetter: null,
      showFeedback: false,
      accuracy: 100,
      totalRounds: 10,
      difficulty: 'EASY',
      availableLetters: DIFFICULTIES.EASY.letters,
      allLetters: DIFFICULTIES.EASY.letters,
      roundStartTime: null,
      
      // 🎯 Sistema de atividades
      currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
      activityCycle: [
        ACTIVITY_TYPES.LETTER_SELECTION.id,
        ACTIVITY_TYPES.SOUND_MATCHING.id,
        ACTIVITY_TYPES.WORD_FORMATION.id,
        ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id,
        ACTIVITY_TYPES.PHONETIC_DISCRIMINATION.id,
        ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id
      ],
      activityIndex: 0,
      roundsPerActivity: 2,
      activityRoundCount: 2, // Permitir troca imediata
      activitiesCompleted: [],
      
      // 🎯 Dados específicos de atividades
      activityData: {
        wordFormation: {
          currentWord: null,
          placedLetters: [],
          availableLetters: []
        },
        sequenceRecognition: {
          currentSequence: null,
          userProgress: []
        },
        phoneticDiscrimination: {
          currentPair: null,
          playedSounds: []
        },
        visualDiscrimination: {
          currentGroup: null,
          foundTargets: 0,
          totalTargets: 0
        }
      },
      
      // 🎯 Métricas comportamentais avançadas
      behavioralMetrics: {
        activityPreferences: {},
        responsePatterns: [],
        adaptiveAdjustments: 0,
        engagementLevel: 1.0
      }
    });
    setShowStartScreen(true);
  };

  // 🔄 Detectar quando o jogo é completado e finalizar sessão multissensorial
  useEffect(() => {
    if (gameState.round > 10 && !showStartScreen) { // Assumindo que 10 é o número máximo de rounds
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await multisensoryIntegration.finalizeSession({
            finalScore: gameState.score,
            finalAccuracy: gameState.accuracy,
            totalInteractions: gameState.round - 1,
            sessionDuration: Date.now() - (gameState.startTime || Date.now()),
            difficulty: gameState.difficulty
          });
          console.log('🔄 LetterRecognition: Relatório multissensorial final:', multisensoryReport);
        } catch (error) {
          console.warn('⚠️ LetterRecognition: Erro ao finalizar sessão multissensorial:', error);
        }
      };

      finalizeMultisensorySession();
    }
  }, [gameState.round, showStartScreen, multisensoryIntegration, gameState.score, gameState.accuracy, gameState.difficulty, gameState.startTime]);

  // 🎯 Explicar o jogo atual
  const explainGame = useCallback(() => {
    const letter = gameState.targetLetter;
    if (!letter) {
      speak("Bem-vindo ao jogo de reconhecimento de letras! Aqui você vai aprender o alfabeto de forma divertida.");
      return;
    }

    const explanation = `Encontre a letra ${letter.sound}. Como em ${letter.example}. Clique na opção correta.`;
    speak(explanation);
    
    // Registrar uso de acessibilidade
    if (portalReady) {
      recordInteraction({
        type: 'tts_usage',
        data: {
          text: explanation,
          targetLetter: letter.id,
          round: gameState.round
        }
      });
    }
  }, [gameState.targetLetter, gameState.round, speak, portalReady, recordInteraction]);

  // 🔊 Repetir a instrução da letra atual
  const repeatInstruction = useCallback(() => {
    if (gameState.targetLetter) {
      const letter = gameState.targetLetter;
      speak(`A letra é ${letter.sound}, como em ${letter.example}`);
    }
  }, [gameState.targetLetter, speak]);

  // 🎉 Feedback sonoro para acertos/erros
  const playFeedback = useCallback((isCorrect, selectedLetter) => {
    if (isCorrect) {
      speak("Muito bem! Você acertou!", { pitch: 1.3, rate: 0.9 });
    } else {
      const targetLetter = gameState.targetLetter;
      const selectedLetterData = LETTERS.find(l => l.id === selectedLetter);
      speak(`Não foi dessa vez. Você escolheu ${selectedLetterData?.sound}, mas a resposta correta é ${targetLetter?.sound}`, { 
        pitch: 0.9, 
        rate: 0.7 
      });
    }
  }, [gameState.targetLetter, speak]);

  // 🎵 Pronunciar letra ao clicar na opção
  const speakLetter = useCallback((letterId) => {
    const letter = LETTERS.find(l => l.id === letterId);
    if (letter) {
      speak(letter.sound, { rate: 0.6, pitch: 1.2 });
    }
  }, [speak]);

  // ==================== MÉTODOS AUXILIARES V3 ====================

  /**
   * Calcula habilidade específica da atividade
   */
  const calculateActivitySpecificSkill = (activityType, isCorrect) => {
    const baseScore = isCorrect ? 0.8 : 0.3;
    
    const skillMultipliers = {
      'letter_selection': 1.0,
      'sound_matching': 1.2,
      'word_formation': 1.5,
      'sequence_recognition': 1.3,
      'phonetic_discrimination': 1.6,
      'visual_discrimination': 1.4
    };
    
    return baseScore * (skillMultipliers[activityType] || 1.0);
  };

  /**
   * Obtém complexidade da atividade
   */
  const getActivityComplexity = (activityType) => {
    const complexityMap = {
      'letter_selection': 0.3,
      'sound_matching': 0.5,
      'word_formation': 0.8,
      'sequence_recognition': 0.6,
      'phonetic_discrimination': 0.9,
      'visual_discrimination': 0.7
    };
    
    return complexityMap[activityType] || 0.5;
  };

  /**
   * Atualiza métricas comportamentais específicas da atividade
   */
  const updateActivitySpecificMetrics = (activityType, isCorrect, responseTime) => {
    const updates = {};
    
    switch (activityType) {
      case 'word_formation':
        updates.wordBuildingTime = [...(gameState.behavioralMetrics.activitySpecific?.wordBuildingTime || []), responseTime];
        updates.letterSequenceAccuracy = [...(gameState.behavioralMetrics.activitySpecific?.letterSequenceAccuracy || []), isCorrect ? 1 : 0];
        break;
      
      case 'visual_discrimination':
        updates.visualScanTime = [...(gameState.behavioralMetrics.activitySpecific?.visualScanTime || []), responseTime];
        updates.targetDetectionRate = [...(gameState.behavioralMetrics.activitySpecific?.targetDetectionRate || []), isCorrect ? 1 : 0];
        break;
      
      case 'sound_matching':
        updates.audioProcessingTime = [...(gameState.behavioralMetrics.activitySpecific?.audioProcessingTime || []), responseTime];
        updates.phoneticAccuracy = [...(gameState.behavioralMetrics.activitySpecific?.phoneticAccuracy || []), isCorrect ? 1 : 0];
        break;
      
      case 'sequence_recognition':
        updates.sequenceCompletionTime = [...(gameState.behavioralMetrics.activitySpecific?.sequenceCompletionTime || []), responseTime];
        updates.patternRecognition = [...(gameState.behavioralMetrics.activitySpecific?.patternRecognition || []), isCorrect ? 1 : 0];
        break;
      
      case 'phonetic_discrimination':
        updates.phoneticDiscriminationTime = [...(gameState.behavioralMetrics.activitySpecific?.phoneticDiscriminationTime || []), responseTime];
        updates.auditoryAnalysisSkill = [...(gameState.behavioralMetrics.activitySpecific?.auditoryAnalysisSkill || []), isCorrect ? 1 : 0];
        break;
      
      default:
        // letter_selection ou outras atividades
        updates.basicProcessingTime = [...(gameState.behavioralMetrics.activitySpecific?.basicProcessingTime || []), responseTime];
        break;
    }
    
    return updates;
  };

  // ==================== HANDLERS ESPECÍFICOS DAS ATIVIDADES V3 ====================

  /**
   * 🔗 Handler para seleção de letras na formação de palavras - CORRIGIDO
   */
  const handleWordLetterSelect = async (letter, letterIndex) => {
    console.log('🎯 Word Letter Select:', { letter, letterIndex, selectedLetter: gameState.selectedLetter });
    
    if (gameState.selectedLetter) {
      console.log('⏳ Already processing a selection, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const wordData = gameState.activityData.wordFormation;
    const currentWord = wordData.currentWord;
    
    // Encontrar próxima posição vazia
    const placedLetters = [...(wordData.placedLetters || [])];
    let nextEmptyIndex = placedLetters.findIndex(slot => slot === null || slot === undefined);
    
    if (nextEmptyIndex === -1) {
      console.log('⚠️ Word already complete');
      return; // Palavra já completa
    }
    
    const expectedLetter = currentWord.word[nextEmptyIndex];
    const isCorrect = letter.toUpperCase() === expectedLetter.toUpperCase();
    
    console.log('🔍 Word formation check:', {
      letter: letter.toUpperCase(),
      expected: expectedLetter.toUpperCase(),
      position: nextEmptyIndex,
      isCorrect
    });
    
    if (isCorrect) {
      // Colocar a letra na posição correta
      placedLetters[nextEmptyIndex] = letter.toUpperCase();
      
      setGameState(prev => ({
        ...prev,
        selectedLetter: letter,
        activityData: {
          ...prev.activityData,
          wordFormation: {
            ...prev.activityData.wordFormation,
            placedLetters
          }
        }
      }));

      // Verificar se a palavra está completa
      const isWordComplete = placedLetters.every(slot => slot !== null && slot !== undefined);
      if (isWordComplete) {
        console.log('✅ Word completed!');
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1000); // Delay para mostrar a palavra completa
      } else {
        // Reset selection after successful placement
        setTimeout(() => {
          setGameState(prev => ({ ...prev, selectedLetter: null }));
        }, 500);
      }
    } else {
      console.log('❌ Wrong letter for word formation');
      setGameState(prev => ({ ...prev, selectedLetter: letter }));
      handleIncorrectAnswer();
    }
  };

  /**
   * 📝 Handler para reconhecimento de sequência
   */
  const handleSequenceSelect = async (letter) => {
    console.log('🎯 Sequence Select:', { letter, selectedLetter: gameState.selectedLetter });
    
    if (gameState.selectedLetter) {
      console.log('⏳ Already processing a selection, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData.currentSequence;
    const expectedLetter = currentSequence?.missing;
    const isCorrect = letter.toUpperCase() === expectedLetter?.toUpperCase();

    console.log('🔍 Sequence check:', {
      letter: letter.toUpperCase(),
      expected: expectedLetter?.toUpperCase(),
      isCorrect
    });

    setGameState(prev => ({
      ...prev,
      selectedLetter: letter
    }));

    if (isCorrect) {
      console.log('✅ Correct sequence letter!');
      handleCorrectAnswer();
    } else {
      console.log('❌ Wrong sequence letter');
      handleIncorrectAnswer();
    }
  };

  /**
   * 🎧 Handler para discriminação fonética
   */
  const handlePhoneticSelect = async (letter) => {
    if (gameState.selectedLetter) return;

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const phoneticData = gameState.activityData.phoneticDiscrimination;
    const currentPair = phoneticData.currentPair;
    const targetLetter = currentPair?.target;
    const isCorrect = letter.toUpperCase() === targetLetter?.toUpperCase();

    setGameState(prev => ({
      ...prev,
      selectedLetter: letter
    }));

    if (isCorrect) {
      handleCorrectAnswer();
    } else {
      handleIncorrectAnswer();
    }
  };

  /**
   * 👁️ Handler para discriminação visual
   */
  const handleVisualSelect = async (letter, itemIndex) => {
    console.log('🎯 Visual Select:', { letter, itemIndex, selectedLetter: gameState.selectedLetter });
    
    // Para discriminação visual, permitimos seleções múltiplas das letras alvo
    const selectionKey = `${letter}-${itemIndex}`;
    
    // Verificar se este item já foi selecionado
    if (gameState.selectedLetter === selectionKey) {
      console.log('⚠️ Item already selected, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const visualData = gameState.activityData.visualDiscrimination;
    const currentGroup = visualData.currentGroup;
    const targetLetter = currentGroup?.target;
    const isCorrect = letter.toUpperCase() === targetLetter?.toUpperCase();

    console.log('🔍 Visual discrimination check:', {
      letter: letter.toUpperCase(),
      target: targetLetter?.toUpperCase(),
      isCorrect,
      currentFound: visualData.foundTargets
    });

    setGameState(prev => ({
      ...prev,
      selectedLetter: selectionKey,
      activityData: {
        ...prev.activityData,
        visualDiscrimination: {
          ...prev.activityData.visualDiscrimination,
          foundTargets: isCorrect ? prev.activityData.visualDiscrimination.foundTargets + 1 : prev.activityData.visualDiscrimination.foundTargets,
          selectedItems: [...(prev.activityData.visualDiscrimination.selectedItems || []), selectionKey]
        }
      }
    }));

    if (isCorrect) {
      // Verificar se encontrou todas as letras alvo
      const newFoundCount = visualData.foundTargets + 1;
      console.log(`✅ Correct target found! (${newFoundCount}/${visualData.totalTargets})`);
      
      if (newFoundCount >= visualData.totalTargets) {
        console.log('🎉 All targets found!');
        setTimeout(() => {
          handleCorrectAnswer();
        }, 500);
      } else {
        // Reset selection para permitir próxima seleção
        setTimeout(() => {
          setGameState(prev => ({ ...prev, selectedLetter: null }));
        }, 300);
      }
    } else {
      console.log('❌ Wrong letter selected in visual discrimination');
      handleIncorrectAnswer();
    }
  };

  /**
   * Função auxiliar para coletar dados específicos da atividade
   */
  const collectActivityData = async (data) => {
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: 'letter_recognition_v3',
          activityType: data.activityType,
          difficulty: gameState.difficulty
        });
      }

      // Preparar métricas comportamentais específicas
      const activitySpecificMetrics = updateActivitySpecificMetrics(
        data.activityType, 
        data.isCorrect, 
        data.responseTime
      );

      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: { id: data.targetLetter, letter: data.targetLetter },
        selectedLetter: { id: data.selectedLetter, letter: data.selectedLetter },
        isCorrect: data.isCorrect,
        responseTime: data.responseTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || 'anonymous',
        
        // Dados V3 específicos
        activityType: data.activityType,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        
        // Dados da atividade atual
        activityData: {
          ...gameState.activityData,
          ...data.activitySpecificData
        },
        
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), data.responseTime],
          accuracy: [...(gameState.behavioralMetrics.accuracy || []), data.isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            ...activitySpecificMetrics
          }
        },
        
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: data.responseTime > 3000 ? data.responseTime - 3000 : 0,
          confidence: data.responseTime < 1000 ? 'high' : data.responseTime < 3000 ? 'medium' : 'low',
          activityEngagement: calculateActivityEngagement(data.activityType, data.responseTime)
        }
      });

      // Registrar interação multissensorial
      if (multisensoryIntegration.isInitialized) {
        await multisensoryIntegration.recordInteraction('activity_interaction', {
          interactionType: 'activity_specific',
          gameSpecificData: {
            activityType: data.activityType,
            targetLetter: data.targetLetter,
            selectedLetter: data.selectedLetter,
            isCorrect: data.isCorrect,
            responseTime: data.responseTime,
            ...data.activitySpecificData
          },
          multisensoryProcessing: {
            linguisticProcessing: {
              activitySpecificSkill: calculateActivitySpecificSkill(data.activityType, data.isCorrect),
              complexityHandling: getActivityComplexity(data.activityType)
            },
            cognitiveProcessing: {
              accuracy: data.isCorrect ? 1.0 : 0.0,
              processingSpeed: data.responseTime < 3000 ? 0.8 : 0.5,
              activityAdaptation: calculateActivityAdaptation(data.activityType, gameState.activityRoundCount || 0)
            },
            behavioralProcessing: {
              activityEngagement: calculateActivityEngagement(data.activityType, data.responseTime),
              progressionRate: (gameState.activitiesCompleted?.length || 0) / 6
            }
          }
        });
      }
    } catch (error) {
      console.warn(`📚 LetterRecognition V3: Erro ao coletar dados da atividade ${data.activityType}:`, error);
    }
  };

  // ==================== FUNÇÕES AUXILIARES ESPECÍFICAS ====================

  /**
   * Obtém letra esperada na sequência
   */
  const getExpectedSequenceLetter = (sequence, missingIndex) => {
    if (!sequence || sequence.length === 0) return null;
    
    if (missingIndex === 0 && sequence.length > 1) {
      return String.fromCharCode(sequence[1].charCodeAt(0) - 1);
    } else if (missingIndex === sequence.length) {
      return String.fromCharCode(sequence[sequence.length - 1].charCodeAt(0) + 1);
    } else if (missingIndex > 0 && missingIndex < sequence.length) {
      // Interpolação para posição no meio
      const prevChar = sequence[missingIndex - 1];
      const nextChar = sequence[missingIndex + 1];
      return String.fromCharCode(Math.round((prevChar.charCodeAt(0) + nextChar.charCodeAt(0)) / 2));
    }
    
    return null;
  };

  /**
   * Identifica padrão da sequência
   */
  const identifySequencePattern = (sequence) => {
    if (sequence.length < 2) return 'simple';
    
    const differences = [];
    for (let i = 1; i < sequence.length; i++) {
      differences.push(sequence[i].charCodeAt(0) - sequence[i-1].charCodeAt(0));
    }
    
    if (differences.every(diff => diff === 1)) return 'consecutive';
    if (differences.every(diff => diff === differences[0] && diff > 1)) return 'skip';
    if (differences.every(diff => diff < 0)) return 'reverse';
    return 'complex';
  };

  /**
   * Calcula similaridade fonética
   */
  const calculatePhoneticSimilarity = (sounds) => {
    if (!sounds || sounds.length < 2) return 0.5;
    
    const sound1 = sounds[0]?.toLowerCase();
    const sound2 = sounds[1]?.toLowerCase();
    
    // Análise simplificada de similaridade fonética
    if (sound1 === sound2) return 1.0;
    
    const similarPairs = [
      ['b', 'p'], ['d', 't'], ['g', 'k'], ['f', 'v'], ['s', 'z'], ['m', 'n']
    ];
    
    const areSimilar = similarPairs.some(pair => 
      (pair[0] === sound1 && pair[1] === sound2) || 
      (pair[1] === sound1 && pair[0] === sound2)
    );
    
    return areSimilar ? 0.8 : 0.3;
  };

  /**
   * Obtém complexidade auditiva
   */
  const getAuditoryComplexity = (phoneticPair) => {
    const sounds = phoneticPair.sounds || [];
    const similarity = calculatePhoneticSimilarity(sounds);
    
    // Maior complexidade = maior similaridade (mais difícil de discriminar)
    return similarity;
  };

  /**
   * Calcula engajamento na atividade
   */
  const calculateActivityEngagement = (activityType, responseTime) => {
    const optimalTimes = {
      'letter_selection': 2000,
      'sound_matching': 3000,
      'word_formation': 5000,
      'sequence_recognition': 4000,
      'phonetic_discrimination': 4500,
      'visual_discrimination': 6000
    };
    
    const optimalTime = optimalTimes[activityType] || 3000;
    const timeRatio = Math.abs(responseTime - optimalTime) / optimalTime;
    
    return Math.max(0.2, 1 - timeRatio);
  };

  /**
   * Calcula adaptação à atividade
   */
  const calculateActivityAdaptation = (activityType, activityRound) => {
    // Adaptação melhora com rounds na mesma atividade
    const adaptationBonus = Math.min(activityRound * 0.1, 0.3);
    const baseAdaptation = 0.5;
    
    return Math.min(baseAdaptation + adaptationBonus, 1.0);
  };

  // ==================== HANDLERS DE RESPOSTA ====================

  /**
   * ✅ Handler para resposta correta
   */
  const handleCorrectAnswer = useCallback(() => {
    setGameState(prev => {
      const newScore = prev.score + 10;
      const newRound = prev.round + 1;
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10) + 1;
      const newAccuracy = Math.round((correctAnswers / totalAttempts) * 100);

      return {
        ...prev,
        selectedLetter: prev.targetLetter?.id || prev.selectedLetter,
        showFeedback: true,
        score: newScore,
        round: newRound,
        accuracy: newAccuracy,
        activityRoundCount: prev.activityRoundCount + 1
      };
    });

    speak("Muito bem! Resposta correta!", { pitch: 1.3, rate: 0.9 });
    
    // ✅ Avanço automático para próxima pergunta (não próximo jogo)
    setTimeout(() => {
      console.log('🎯 Avançando para próxima pergunta automaticamente...');
      setGameState(prev => ({ ...prev, showFeedback: false }));
      generateNewRound();
    }, 1500); // Tempo reduzido para progressão mais rápida
  }, [speak, generateNewRound]);

  /**
   * ❌ Handler para resposta incorreta
   */
  const handleIncorrectAnswer = useCallback(() => {
    setGameState(prev => {
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10);
      const newAccuracy = totalAttempts > 0 ? Math.round((correctAnswers / totalAttempts) * 100) : 100;

      return {
        ...prev,
        showFeedback: true,
        accuracy: newAccuracy,
        round: prev.round + 1,
        activityRoundCount: prev.activityRoundCount + 1
      };
    });

    speak("Tente novamente! Você consegue!", { pitch: 1.0, rate: 0.8 });
    setTimeout(() => {
      setGameState(prev => ({ ...prev, showFeedback: false }));
      generateNewRound();
    }, 1500); // Tempo reduzido para progressão mais rápida
  }, [speak, generateNewRound]);

  // ==================== COLETORES DE DADOS DAS ATIVIDADES ====================

  /**
   * Inicializa a sessão do coletor para reconhecimento de letras
   */
  const initializeCollectors = async () => {
    try {
      if (collectorsHub && typeof collectorsHub.initializeSession === 'function') {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          difficulty: gameState.difficulty,
          availableLetters: gameState.availableLetters.map(l => l.id),
          gameMode: 'letter_recognition'
        });
        console.log('📚 LetterRecognition: Coletores inicializados com sucesso');
      }
    } catch (error) {
      console.warn('⚠️ LetterRecognition: Erro ao inicializar coletores:', error);
    }
  };

  /**
   * Registra interação com os coletores de dados
   */
  const registerDataCollectors = async (isCorrect, reactionTime) => {
    try {
      if (collectorsHub.sessionActive) {
        await collectorsHub.collectComprehensiveData({
          // Dados básicos
          targetLetter: gameState.targetLetter,
          selectedLetter: LETTERS.find(l => l.id === gameState.selectedLetter),
          isCorrect,
          responseTime: reactionTime,
          sessionId: sessionIdRef.current,
          userId: user?.id || 'anonymous',
          
          // Dados V3 específicos
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0,
          activitiesCompleted: gameState.activitiesCompleted || [],
          
          // Dados da atividade atual
          activityData: gameState.activityData,
          
          // Métricas comportamentais V3
          behavioralMetrics: {
            ...gameState.behavioralMetrics,
            reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), reactionTime],
            accuracy: [...(gameState.behavioralMetrics.accuracy || []), isCorrect ? 1 : 0],
            attentionSpan: Date.now() - gameState.roundStartTime,
            currentLevel: gameState.currentLevel,
            totalAttempts: gameState.round,
            activitySpecific: {
              ...gameState.behavioralMetrics.activitySpecific,
              // Adicionar métricas específicas baseadas na atividade atual
              ...(gameState.currentActivity === 'word_formation' && {
                wordBuildingTime: reactionTime,
                letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
              }),
              ...(gameState.currentActivity === 'visual_discrimination' && {
                visualScanTime: reactionTime,
                targetDetectionRate: gameState.activityData?.detectionRate || 0
              }),
              ...(gameState.currentActivity === 'sound_matching' && {
                audioProcessingTime: reactionTime,
                phoneticAccuracy: isCorrect ? 1 : 0
              }),
              ...(gameState.currentActivity === 'sequence_recognition' && {
                sequenceCompletionTime: reactionTime,
                patternRecognition: isCorrect ? 1 : 0
              })
            }
          },
          
          // Comportamento do jogador
          playerBehavior: {
            hesitationTime: reactionTime > 3000 ? reactionTime - 3000 : 0,
            confidence: reactionTime < 1000 ? 'high' : reactionTime < 3000 ? 'medium' : 'low',
            attemptPattern: gameState.round % 5 === 0 ? 'milestone' : 'regular'
          }
        });
      }
    } catch (error) {
      console.warn('📚 LetterRecognition V3: Erro ao registrar interação:', error);
    }
  };

  // Resto do código...

  // 🎯 RENDERIZADOR PRINCIPAL DE ATIVIDADES
  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case 'letter_selection':
        return renderLetterSelection();
      case 'sound_matching':
        return renderSoundMatching();
      case 'word_formation':
        return renderWordFormation();
      case 'sequence_recognition':
        return renderSequenceRecognition();
      case 'phonetic_discrimination':
        return renderPhoneticDiscrimination();
      case 'visual_discrimination':
        return renderVisualDiscrimination();
      default:
        return renderLetterSelection();
    }
  };

  // 🔤 COMPONENTE: Seleção de Letras (atividade original)
  const renderLetterSelection = () => (
    <>
      {gameState.targetLetter && (
        <div className={styles.letterTargetSection}>
          <div className={styles.letterInstruction}>
            <p>Encontre a letra:</p>
            {ttsEnabled && (
              <button 
                className={styles.repeatButton}
                onClick={repeatInstruction}
                title="Repetir instrução"
                aria-label="Repetir instrução da letra"
              >
                🔊 Repetir
              </button>
            )}
          </div>
          <div className={styles.letterDisplay}>
            <div className={styles.letterMain}>{gameState.targetLetter.letter}</div>
            <div className={styles.letterSound}>{gameState.targetLetter.sound}</div>
          </div>
          <div className={styles.letterExample}>{gameState.targetLetter.example}</div>
        </div>
      )}
      
      <div className={styles.optionsGrid}>
        {gameState.availableLetters.map((letter) => (
          <div
            key={letter.id}
            className={`${styles.letterOption} ${gameState.selectedLetter === letter.id ? styles.selected : ''} ${
              gameState.selectedLetter === letter.id && gameState.selectedLetter === gameState.targetLetter.id ? styles.correct : ''
            } ${
              gameState.selectedLetter === letter.id && gameState.selectedLetter !== gameState.targetLetter.id ? styles.incorrect : ''
            }`}
            onClick={() => handleLetterSelect(letter.id)}
          >
            <span className={styles.optionLetter}>{letter.letter}</span>
            <span className={styles.optionName}>{letter.sound}</span>
            {ttsEnabled && !gameState.selectedLetter && (
              <button
                className={styles.letterTtsButton}
                onClick={(e) => {
                  e.stopPropagation();
                  speakLetter(letter.id);
                }}
                title={`Ouvir ${letter.sound}`}
                aria-label={`Pronunciar letra ${letter.sound}`}
              >
                🔊
              </button>
            )}
          </div>
        ))}
      </div>
    </>
  );

  // 🎵 COMPONENTE: Combinação de Sons - LAYOUT CORRIGIDO
  const renderSoundMatching = () => (
    <div className={styles.soundMatchingActivity}>
      <div className={styles.activityInstruction}>
        <h3>Que letra faz este som?</h3>
        <button 
          className={styles.playSoundButton}
          onClick={() => {
            if (gameState.targetLetter) {
              speak(gameState.targetLetter.sound, { rate: 0.7, pitch: 1.1 });
            }
          }}
        >
          ▶️ Tocar Som
        </button>
      </div>
      
      <div className={styles.optionsGrid}>
        {gameState.availableLetters.map((letter) => (
          <div
            key={letter.id}
            className={`${styles.letterOption} ${gameState.selectedLetter === letter.id ? styles.selected : ''}`}
            onClick={() => handleLetterSelect(letter.id)}
          >
            <span className={styles.optionLetter}>{letter.letter}</span>
            <button 
              className={styles.soundButton}
              onClick={(e) => {
                e.stopPropagation();
                speak(letter.sound, { rate: 0.6, pitch: 1.1 });
              }}
              title={`Ouvir ${letter.sound}`}
            >
              🔊
            </button>
            <div className={styles.phoneticText}>/{letter.sound.toLowerCase()}/</div>
          </div>
        ))}
      </div>
    </div>
  );

  // 🔗 COMPONENTE: Formação de Palavras - CORRIGIDO
  const renderWordFormation = () => {
    const wordData = gameState.activityData.wordFormation;
    
    // Verificar se dados estão inicializados
    if (!wordData || !wordData.currentWord) {
      console.log('🚨 Word formation data not initialized:', wordData);
      return (
        <div className={styles.wordActivity}>
          <div className={styles.activityInstruction}>
            <h3>🔄 Carregando formação de palavras...</h3>
            <p>Aguarde enquanto preparamos as palavras para você!</p>
          </div>
        </div>
      );
    }

    const placedLetters = wordData.placedLetters || [];

    console.log('🔤 Rendering word formation:', {
      currentWord: wordData.currentWord,
      placedLetters,
      availableLetters: wordData.availableLetters
    });

    return (
      <div className={styles.wordActivity}>
        <div className={styles.activityInstruction}>
          <h3>Monte a palavra: <span style={{ color: '#4CAF50' }}>{wordData.currentWord.word}</span></h3>
          <p>{wordData.currentWord.emoji} {wordData.currentWord.meaning}</p>
        </div>
        
        <div className={styles.wordFormation}>
          {wordData.currentWord.word.split('').map((letter, index) => (
            <div 
              key={index}
              className={`${styles.wordSlot} ${placedLetters[index] ? styles.filled : ''}`}
            >
              {placedLetters[index] || ''}
            </div>
          ))}
        </div>
        
        <div className={styles.wordLetterGrid}>
          {wordData.availableLetters?.map((letter, index) => (
            <div
              key={`${letter}-${index}`}
              className={`${styles.wordLetter} ${gameState.selectedLetter === letter ? styles.selected : ''}`}
              onClick={() => handleWordLetterSelect(letter, index)}
            >
              {letter}
            </div>
          ))}
        </div>
        
        <div className={styles.wordInfo}>
          Progresso: <span className={styles.wordProgress}>{placedLetters.filter(l => l).length}</span>/{wordData.currentWord.word.length}
        </div>
      </div>
    );
  };

  // 📝 COMPONENTE: Reconhecimento de Sequência
  const renderSequenceRecognition = () => {
    const sequenceData = gameState.activityData.sequenceRecognition;
    
    if (!sequenceData || !sequenceData.currentSequence) {
      console.log('🚨 Sequence data not initialized:', sequenceData);
      return (
        <div className={styles.sequenceActivity}>
          <div className={styles.activityInstruction}>
            <h3>🔄 Carregando sequência...</h3>
            <p>Preparando o reconhecimento de sequência!</p>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.sequenceActivity}>
        <div className={styles.activityInstruction}>
          <h3>Complete a sequência:</h3>
          <p>Que letra vem depois?</p>
        </div>
        
        <div className={styles.sequenceDisplay}>
          <div className={styles.sequenceLetters}>
            {sequenceData.currentSequence.sequence.map((letter, index) => (
              <span key={index} className={styles.sequenceLetter}>{letter}</span>
            ))}
            <span className={styles.arrow}>→</span>
            <span className={styles.missingLetter}>?</span>
          </div>
        </div>
        
        <div className={styles.sequenceOptions}>
          {sequenceData.currentSequence.options.map((letter) => (
            <div
              key={letter}
              className={`${styles.sequenceOption} ${gameState.selectedLetter === letter ? styles.selected : ''}`}
              onClick={() => handleSequenceSelect(letter)}
            >
              {letter}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 🎧 COMPONENTE: Discriminação Fonética
  const renderPhoneticDiscrimination = () => {
    const phoneticData = gameState.activityData.phoneticDiscrimination;
    
    if (!phoneticData || !phoneticData.currentPair) {
      console.log('🚨 Phonetic data not initialized:', phoneticData);
      return (
        <div className={styles.phoneticActivity}>
          <div className={styles.activityInstruction}>
            <h3>🔄 Carregando discriminação fonética...</h3>
            <p>Preparando os sons para você!</p>
          </div>
        </div>
      );
    }

    const allOptions = [phoneticData.currentPair.target, ...phoneticData.currentPair.distractors];
    const shuffledOptions = shuffleArray(allOptions);

    return (
      <div className={styles.phoneticActivity}>
        <div className={styles.activityInstruction}>
          <h3>Qual letra tem som diferente?</h3>
          <p>Ouça com atenção!</p>
        </div>
        
        <div className={styles.phoneticOptions}>
          {shuffledOptions.map((letter, index) => (
            <div
              key={letter}
              className={`${styles.phoneticOption} ${gameState.selectedLetter === letter ? styles.selected : ''}`}
              onClick={() => handlePhoneticSelect(letter)}
            >
              <span className={styles.optionLetter}>{letter}</span>
              <button 
                className={styles.soundButton}
                onClick={(e) => {
                  e.stopPropagation();
                  const soundIndex = allOptions.indexOf(letter);
                  const sound = phoneticData.currentPair.sounds[soundIndex];
                  speak(sound, { rate: 0.6 });
                }}
              >
                🔊
              </button>
              <div className={styles.phoneticText}>
                {phoneticData.currentPair.sounds[allOptions.indexOf(letter)]}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 👁️ COMPONENTE: Discriminação Visual
  const renderVisualDiscrimination = () => {
    const visualData = gameState.activityData.visualDiscrimination;
    if (!visualData.currentGroup) return null;

    const allLetters = [
      ...Array(visualData.totalTargets).fill(visualData.currentGroup.target),
      ...visualData.currentGroup.distractors.slice(0, 3)
    ];
    const shuffledLetters = shuffleArray(allLetters);

    return (
      <div className={styles.visualActivity}>
        <div className={styles.activityInstruction}>
          <h3>Encontre todas as letras: <span style={{ color: '#2196F3' }}>{visualData.currentGroup.target}</span></h3>
          <p>Cuidado com as parecidas!</p>
        </div>
        
        <div className={styles.letterGrid}>
          {shuffledLetters.map((letter, index) => {
            const itemKey = `${letter}-${index}`;
            const isTarget = letter === visualData.currentGroup.target;
            const isSelected = visualData.selectedItems && visualData.selectedItems.includes(itemKey);
            
            return (
              <div
                key={index}
                className={`${styles.letterItem} ${
                  isTarget ? styles.target : ''
                } ${isSelected ? styles.selected : ''}`}
                onClick={() => handleVisualSelect(letter, index)}
              >
                {letter}
              </div>
            );
          })}
        </div>
        
        <div className={styles.progressInfo}>
          Encontradas: <span className={styles.foundCount}>{visualData.foundTargets}</span>/{visualData.totalTargets}
        </div>
      </div>
    );
  };

  // Tela inicial profissional com seleção de dificuldade
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Reconhecimento de Letras"
        gameSubtitle="Aprenda o alfabeto de forma divertida e interativa"
        gameInstruction="Encontre a letra correta baseada no som e exemplo apresentados. Desenvolva suas habilidades de alfabetização!"
        gameIcon="📚"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: '4 letras básicas - Ideal para iniciantes',
            icon: '😊',
            preview: (
              <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
                <span style={{ background: '#FF6B6B', color: 'white', padding: '4px 8px', borderRadius: '4px', fontSize: '14px', fontWeight: 'bold' }}>A</span>
                <span style={{ background: '#4ECDC4', color: 'white', padding: '4px 8px', borderRadius: '4px', fontSize: '14px', fontWeight: 'bold' }}>B</span>
                <span style={{ background: '#45B7D1', color: 'white', padding: '4px 8px', borderRadius: '4px', fontSize: '14px', fontWeight: 'bold' }}>C</span>
              </div>
            )
          },
          {
            id: 'medium',
            name: 'Médio',
            description: '6 letras variadas - Desafio equilibrado',
            icon: '🎯',
            preview: (
              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center', flexWrap: 'wrap' }}>
                <span style={{ background: '#FF6B6B', color: 'white', padding: '3px 6px', borderRadius: '3px', fontSize: '12px', fontWeight: 'bold' }}>A</span>
                <span style={{ background: '#4ECDC4', color: 'white', padding: '3px 6px', borderRadius: '3px', fontSize: '12px', fontWeight: 'bold' }}>B</span>
                <span style={{ background: '#45B7D1', color: 'white', padding: '3px 6px', borderRadius: '3px', fontSize: '12px', fontWeight: 'bold' }}>C</span>
                <span style={{ background: '#FFA07A', color: 'white', padding: '3px 6px', borderRadius: '3px', fontSize: '12px', fontWeight: 'bold' }}>D</span>
                <span style={{ background: '#98D8C8', color: 'white', padding: '3px 6px', borderRadius: '3px', fontSize: '12px', fontWeight: 'bold' }}>E</span>
              </div>
            )
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: '8 letras completas - Para especialistas',
            icon: '�',
            preview: (
              <div style={{ display: 'flex', gap: '2px', justifyContent: 'center', flexWrap: 'wrap' }}>
                {['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'].map((letter, i) => (
                  <span key={i} style={{ 
                    background: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'][i], 
                    color: 'white', 
                    padding: '2px 4px', 
                    borderRadius: '2px', 
                    fontSize: '10px', 
                    fontWeight: 'bold' 
                  }}>{letter}</span>
                ))}
              </div>
            )
          }
        ]}
        onStart={(difficulty) => startGame(difficulty)}
        customContent={
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '1.5rem',
            marginBottom: '1rem'
          }}>
            <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
              🧠 Este jogo desenvolve:
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>�</div>
                <div>Alfabetização</div>
              </div>              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>👁️</div>
                <div>Reconhecimento Visual</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧩</div>
                <div>Associação de Ideias</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📖</div>
                <div>Leitura Inicial</div>
              </div>
            </div>
          </div>
        }
        onStartGame={startGame}
        onBack={onBack}
      />
    );
  }

  // Jogo principal
  return (
    <div 
      className={`${styles.letterRecognitionGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      <div className={styles.gameContent}>
        {/* Header do jogo */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            📚 Reconhecimento de Letras
            <div className={styles.activitySubtitle}>
              {Object.values(ACTIVITY_TYPES).find(activity => activity.id === gameState.currentActivity)?.name || 'Atividade Atual'}
            </div>
          </h1>          
          <button 
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            aria-label={ttsActive ? "Desativar TTS" : "Ativar TTS"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* 🎯 SISTEMA DE ATIVIDADES - PADRÃO MEMORYGAME EXATO */}
        {gameState.status === 'playing' && (
          <>
            {/* Menu de Atividades */}
            <div className={styles.activityMenu}>
              {Object.values(ACTIVITY_TYPES).map((activity) => {
                const isCurrentActivity = gameState.currentActivity === activity.id;
                
                return (
                  <button
                    key={activity.id}
                    className={`${styles.activityButton} ${
                      isCurrentActivity ? styles.active : ''
                    }`}
                    onClick={() => changeActivity(activity.id)}
                    title={activity.description}
                  >
                    <span className={styles.activityIcon}>{activity.icon}</span>
                    <span className={styles.activityName}>{activity.name}</span>
                    {isCurrentActivity && (
                      <span className={styles.activeIndicator}>●</span>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Estatísticas do jogo */}
            <div className={styles.gameStats}>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.round}</div>
                <div className={styles.statLabel}>Rodada</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.score}</div>
                <div className={styles.statLabel}>Pontos</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.accuracy}%</div>
                <div className={styles.statLabel}>Precisão</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statValue}>{gameState.activityRoundCount}</div>
                <div className={styles.statLabel}>Progresso</div>
              </div>
            </div>

            {/* Interface da atividade atual */}
            {renderCurrentActivity()}
          </>
        )}
      </div>
    </div>
   );
}

export default LetterRecognitionGame;