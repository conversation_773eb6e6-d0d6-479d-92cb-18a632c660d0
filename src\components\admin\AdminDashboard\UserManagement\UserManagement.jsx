/**
 * @file UserManagement.jsx
 * @description Gerenciamento de Usuários - Área Administrativa
 * @version 1.0.0
 * @admin true
 */

import React, { useState, useEffect } from 'react'
import styles from './UserManagement.module.css'

const UserManagement = () => {
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')

  // Carregar usuários do localStorage
  const loadUsers = () => {
    try {
      const savedUsers = JSON.parse(localStorage.getItem('registeredUsers') || '[]')
      const savedSessions = JSON.parse(localStorage.getItem('gameSessions') || '[]')
      const savedScores = JSON.parse(localStorage.getItem('gameScores') || '[]')

      // Enriquecer dados dos usuários com estatísticas
      const enrichedUsers = savedUsers.map(user => {
        const userSessions = savedSessions.filter(s => s.userId === user.id)
        const userScores = savedScores.filter(s => s.userId === user.id)
        
        return {
          ...user,
          stats: {
            totalSessions: userSessions.length,
            totalGames: userScores.length,
            avgScore: userScores.length > 0 
              ? (userScores.reduce((sum, s) => sum + s.score, 0) / userScores.length).toFixed(1)
              : 0,
            lastActivity: userSessions.length > 0 
              ? Math.max(...userSessions.map(s => new Date(s.timestamp).getTime()))
              : user.createdAt,
            favoriteGame: userScores.length > 0 
              ? userScores.reduce((acc, score) => {
                  acc[score.gameType] = (acc[score.gameType] || 0) + 1
                  return acc
                }, {})
              : {}
          }
        }
      })

      // Adicionar usuário padrão se não houver usuários
      if (enrichedUsers.length === 0) {
        enrichedUsers.push({
          id: 'default_user',
          name: 'Usuário Padrão',
          email: '<EMAIL>',
          type: 'child',
          status: 'active',
          createdAt: Date.now() - 86400000, // 1 dia atrás
          stats: {
            totalSessions: Math.floor(Math.random() * 20) + 5,
            totalGames: Math.floor(Math.random() * 50) + 10,
            avgScore: (Math.random() * 40 + 60).toFixed(1),
            lastActivity: Date.now() - (Math.random() * 3600000),
            favoriteGame: { 'ColorMatch': 15, 'MemoryGame': 12, 'PadroesVisuais': 8 }
          }
        })
      }

      setUsers(enrichedUsers)
    } catch (error) {
      console.error('Erro ao carregar usuários:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadUsers()
  }, [])

  // Filtrar usuários
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === 'all' || user.status === filterStatus
    return matchesSearch && matchesFilter
  })

  const formatDate = (timestamp) => {
    return new Date(timestamp).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return '#4CAF50'
      case 'inactive': return '#FF9800'
      case 'suspended': return '#F44336'
      default: return '#9E9E9E'
    }
  }

  const getFavoriteGame = (favoriteGame) => {
    if (!favoriteGame || Object.keys(favoriteGame).length === 0) return 'Nenhum'
    
    const sorted = Object.entries(favoriteGame).sort(([,a], [,b]) => b - a)
    return sorted[0][0]
  }

  const getActivityStatus = (lastActivity) => {
    const now = Date.now()
    const diff = now - lastActivity
    const hours = diff / (1000 * 60 * 60)
    
    if (hours < 1) return { text: 'Online', color: '#4CAF50' }
    if (hours < 24) return { text: 'Hoje', color: '#2196F3' }
    if (hours < 168) return { text: 'Esta semana', color: '#FF9800' }
    return { text: 'Inativo', color: '#F44336' }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando usuários...</p>
      </div>
    )
  }

  return (
    <div className={styles.userManagement}>
      {/* Header */}
      <div className={styles.header}>
        <h2>👥 Gerenciamento de Usuários</h2>
        <button onClick={loadUsers} className={styles.refreshButton}>
          🔄 Atualizar
        </button>
      </div>

      {/* Filters */}
      <div className={styles.filters}>
        <div className={styles.searchBox}>
          <input
            type="text"
            placeholder="🔍 Buscar usuários..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.statusFilter}>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">Todos os Status</option>
            <option value="active">Ativos</option>
            <option value="inactive">Inativos</option>
            <option value="suspended">Suspensos</option>
          </select>
        </div>
      </div>

      {/* Stats Summary */}
      <div className={styles.statsSection}>
        <div className={styles.statCard}>
          <div className={styles.statIcon}>👤</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>{users.length}</div>
            <div className={styles.statLabel}>Total de Usuários</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>✅</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>
              {users.filter(u => u.status === 'active').length}
            </div>
            <div className={styles.statLabel}>Usuários Ativos</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>🎮</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>
              {users.reduce((sum, u) => sum + u.stats.totalSessions, 0)}
            </div>
            <div className={styles.statLabel}>Total de Sessões</div>
          </div>
        </div>

        <div className={styles.statCard}>
          <div className={styles.statIcon}>📊</div>
          <div className={styles.statContent}>
            <div className={styles.statValue}>
              {users.length > 0 
                ? (users.reduce((sum, u) => sum + parseFloat(u.stats.avgScore), 0) / users.length).toFixed(1)
                : 0
              }
            </div>
            <div className={styles.statLabel}>Score Médio</div>
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className={styles.usersTable}>
        <div className={styles.tableHeader}>
          <div className={styles.headerCell}>Usuário</div>
          <div className={styles.headerCell}>Status</div>
          <div className={styles.headerCell}>Atividade</div>
          <div className={styles.headerCell}>Estatísticas</div>
          <div className={styles.headerCell}>Jogo Favorito</div>
          <div className={styles.headerCell}>Criado em</div>
        </div>

        <div className={styles.tableBody}>
          {filteredUsers.map(user => {
            const activityStatus = getActivityStatus(user.stats.lastActivity)
            
            return (
              <div key={user.id} className={styles.userRow}>
                <div className={styles.userInfo}>
                  <div className={styles.userAvatar}>
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                  <div className={styles.userDetails}>
                    <div className={styles.userName}>{user.name}</div>
                    <div className={styles.userEmail}>{user.email}</div>
                    <div className={styles.userType}>{user.type || 'child'}</div>
                  </div>
                </div>

                <div className={styles.userStatus}>
                  <span 
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(user.status) }}
                  >
                    {user.status || 'active'}
                  </span>
                </div>

                <div className={styles.userActivity}>
                  <span 
                    className={styles.activityStatus}
                    style={{ color: activityStatus.color }}
                  >
                    {activityStatus.text}
                  </span>
                  <div className={styles.lastSeen}>
                    {formatDate(user.stats.lastActivity)}
                  </div>
                </div>

                <div className={styles.userStats}>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Sessões:</span>
                    <span className={styles.statValue}>{user.stats.totalSessions}</span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Jogos:</span>
                    <span className={styles.statValue}>{user.stats.totalGames}</span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>Score:</span>
                    <span className={styles.statValue}>{user.stats.avgScore}</span>
                  </div>
                </div>

                <div className={styles.favoriteGame}>
                  {getFavoriteGame(user.stats.favoriteGame)}
                </div>

                <div className={styles.createdDate}>
                  {formatDate(user.createdAt)}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {filteredUsers.length === 0 && (
        <div className={styles.noUsers}>
          <div className={styles.noUsersIcon}>👤</div>
          <div className={styles.noUsersText}>
            Nenhum usuário encontrado com os filtros aplicados
          </div>
        </div>
      )}
    </div>
  )
}

export { UserManagement }
export default UserManagement
