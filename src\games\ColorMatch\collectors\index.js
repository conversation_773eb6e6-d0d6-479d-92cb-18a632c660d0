/**
 * 🎨 COLOR MATCH COLLECTORS HUB
 * Hub integrador para todos os coletores especializados do ColorMatch
 * Portal Betina V3
 */

import { ColorPerceptionCollector } from './ColorPerceptionCollector.js';
import { VisualProcessingCollector } from './VisualProcessingCollector.js';
import { AttentionalSelectivityCollector } from './AttentionalSelectivityCollector.js';
import { ColorCognitionCollector } from './ColorCognitionCollector.js';
import { ErrorPatternCollector } from './ErrorPatternCollector.js';

export class ColorMatchCollectorsHub {
  constructor() {
    this._collectors = {
      colorPerception: new ColorPerceptionCollector(),
      visualProcessing: new VisualProcessingCollector(),
      attentionalSelectivity: new AttentionalSelectivityCollector(),
      colorCognition: new ColorCognitionCollector(),
      errorPattern: new ErrorPatternCollector()
    };
    
    this.analysisHistory = [];
    this.currentSession = null;
    this.performanceBaseline = null;
  }

  /**
   * Getter para coletores - necessário para GameSpecificProcessors
   */
  get collectors() {
    return this._collectors;
  }

  /**
   * Executa análise completa usando todos os coletores
   */
  async runCompleteAnalysis(gameData) {
    try {
      console.log('🎨 ColorMatch: Iniciando análise completa...');
      
      if (!this.validateGameData(gameData)) {
        throw new Error('Dados do jogo inválidos para análise');
      }

      const startTime = Date.now();
      
      // Preparar dados específicos para cada coletor
      const collectorData = this.prepareCollectorData(gameData);
      
      if (!collectorData) {
        throw new Error('Falha na preparação dos dados para coletores');
      }
      
      console.log('🎨 ColorMatch: Dados preparados com sucesso, executando coletores...');
      
      // Executar todos os coletores em paralelo para eficiência
      const analysisPromises = [
        this._collectors.colorPerception.analyze(collectorData.colorPerception),
        this._collectors.visualProcessing.analyze(collectorData.visualProcessing),
        this._collectors.attentionalSelectivity.analyze(collectorData.attentionalSelectivity),
        this._collectors.colorCognition.analyze(collectorData.colorCognition)
      ];

      console.log('🎨 ColorMatch: Aguardando resultados dos coletores...');
      
      const [
        colorPerceptionResults,
        visualProcessingResults,
        attentionalSelectivityResults,
        colorCognitionResults
      ] = await Promise.all(analysisPromises);

      console.log('🎨 ColorMatch: Resultados dos coletores obtidos:', {
        colorPerception: !!colorPerceptionResults,
        visualProcessing: !!visualProcessingResults,
        attentionalSelectivity: !!attentionalSelectivityResults,
        colorCognition: !!colorCognitionResults
      });

      const analysisResults = {
        sessionId: gameData.sessionId || `session_${Date.now()}`,
        timestamp: new Date().toISOString(),
        analysisTime: Date.now() - startTime,
        gameData: {
          duration: gameData.sessionDuration,
          difficulty: gameData.difficulty,
          totalColors: gameData.totalColors,
          correctMatches: gameData.correctMatches,
          accuracy: gameData.accuracy
        },
        collectorsResults: {
          colorPerception: colorPerceptionResults,
          visualProcessing: visualProcessingResults,
          attentionalSelectivity: attentionalSelectivityResults,
          colorCognition: colorCognitionResults
        },
        integratedMetrics: this.calculateIntegratedMetrics({
          colorPerceptionResults,
          visualProcessingResults,
          attentionalSelectivityResults,
          colorCognitionResults
        }),
        recommendations: this.generateIntegratedRecommendations({
          colorPerceptionResults,
          visualProcessingResults,
          attentionalSelectivityResults,
          colorCognitionResults
        }),
        colorMatchProfile: this.generateColorMatchProfile({
          colorPerceptionResults,
          visualProcessingResults,
          attentionalSelectivityResults,
          colorCognitionResults
        })
      };

      // Armazenar histórico para análises longitudinais
      this.analysisHistory.push(analysisResults);
      
      console.log(`✅ ColorMatch: Análise completa finalizada em ${analysisResults.analysisTime}ms`);
      console.log('🎨 ColorMatch: Métricas integradas:', {
        overallColorPerception: analysisResults.integratedMetrics.overallColorPerception,
        colorDiscrimination: analysisResults.collectorsResults.colorPerception.colorDiscrimination,
        visualProcessingSpeed: analysisResults.collectorsResults.visualProcessing.processingSpeed,
        attentionalFocus: analysisResults.collectorsResults.attentionalSelectivity.selectiveAttention,
        cognitiveComplexity: analysisResults.collectorsResults.colorCognition.colorCategorization
      });
      
      return analysisResults;

    } catch (error) {
      console.error('❌ ColorMatch: Erro na análise completa:', error);
      console.error('🎨 ColorMatch: Stack trace:', error.stack);
      return this.generateFallbackAnalysis(gameData, error);
    }
  }

  /**
   * Prepara dados específicos para cada coletor - Nova versão para ColorMatch
   */
  prepareCollectorData(rawData) {
    if (!this.validateGameData(rawData)) {
      return null;
    }
    
    // Configurações por dificuldade
    const difficultyConfig = {
      'easy': { expectedColors: 3, expectedItems: 6, timeLimit: null },
      'medium': { expectedColors: 5, expectedItems: 9, timeLimit: 90 },
      'hard': { expectedColors: 8, expectedItems: 12, timeLimit: 60 }
    };
    
    const config = difficultyConfig[rawData.difficulty];
    
    // Calcular métricas específicas do ColorMatch
    const currentTime = Date.now();
    const responseTime = rawData.timestamp ? 
      currentTime - rawData.timestamp : 0;
    
    // Análise do grid atual
    const gridAnalysis = this.analyzeGridState(rawData.gameGrid, rawData.targetColor);
    
    // Análise de progresso
    const progressAnalysis = this.analyzeProgress(rawData.selectedItems, config);
    
    // Estrutura de dados padronizada para coletores
    const collectorData = {
      // Dados básicos
      sessionId: rawData.sessionId,
      timestamp: rawData.timestamp || currentTime,
      difficulty: rawData.difficulty,
      
      // Dados específicos do ColorMatch
      targetColor: rawData.targetColor,
      selectedItems: rawData.selectedItems,
      gameGrid: rawData.gameGrid,
      
      // Métricas calculadas
      responseTime,
      isCorrectSelection: gridAnalysis.isCorrect,
      itemsRemaining: gridAnalysis.itemsRemaining,
      totalCorrectItems: gridAnalysis.totalCorrectItems,
      
      // Contexto da fase
      phaseConfig: config,
      progressPercentage: progressAnalysis.percentage,
      sessionDuration: progressAnalysis.sessionDuration,
      
      // Métricas de performance
      accuracy: progressAnalysis.accuracy,
      averageResponseTime: progressAnalysis.averageResponseTime,
      errorsCount: progressAnalysis.errorsCount,
      
      // Dados para análise específica
      gridPosition: rawData.gridPosition || null,
      previousSelection: rawData.previousSelection || null,
      timeRemaining: rawData.timeRemaining || null,
      
      // Contexto sensorial
      tactileUsed: rawData.tactile || false,
      audioUsed: rawData.audioCue || false,
      
      // Metadados
      collectionTimestamp: currentTime,
      dataVersion: '2.0'
    };
    
    console.log(`🎨 ColorMatch: Dados preparados para ${rawData.difficulty}:`, {
      targetColor: collectorData.targetColor,
      itemsRemaining: collectorData.itemsRemaining,
      accuracy: collectorData.accuracy
    });
    
    // Preparar dados específicos para cada coletor
    return {
      colorPerception: {
        colorData: {
          interactions: [{
            targetColor: collectorData.targetColor,
            selectedColor: rawData.selectedColor || 'unknown',
            correct: collectorData.isCorrectSelection,
            responseTime: collectorData.responseTime,
            timestamp: collectorData.timestamp
          }]
        },
        sessionDuration: collectorData.sessionDuration,
        difficulty: collectorData.difficulty
      },
      
      visualProcessing: {
        visualData: {
          interactions: [{
            responseTime: collectorData.responseTime,
            correct: collectorData.isCorrectSelection,
            elementPosition: collectorData.gridPosition,
            complexity: this.determineVisualComplexity({ 
              difficulty: collectorData.difficulty,
              gridSize: gridAnalysis.gridSize 
            })
          }],
          scanningPatterns: [],
          backgroundComplexity: collectorData.difficulty
        },
        sessionDuration: collectorData.sessionDuration
      },
      
      attentionalSelectivity: {
        attentionData: {
          interactions: [{
            isTarget: collectorData.isCorrectSelection,
            correct: collectorData.isCorrectSelection,
            responseTime: collectorData.responseTime,
            isRelevant: collectorData.isCorrectSelection,
            requiresInhibition: this.requiresInhibition({ 
              distractors: gridAnalysis.gridSize - gridAnalysis.totalCorrectItems 
            }),
            hasConflict: gridAnalysis.gridSize > 6,
            requiresTopDownControl: collectorData.difficulty !== 'easy',
            requiresFlexibility: collectorData.errorsCount > 0,
            isMultitasking: collectorData.timeRemaining !== null,
            memoryLoad: this.calculateMemoryLoad({ 
              colors: config.expectedColors 
            })
          }],
          distractors: gridAnalysis.gridSize - gridAnalysis.totalCorrectItems,
          contextChanges: []
        },
        sessionDuration: collectorData.sessionDuration
      },
      
      colorCognition: {
        cognitiveData: {
          interactions: [{
            taskType: 'color_matching',
            complexity: this.determineCognitiveComplexity({ 
              difficulty: collectorData.difficulty 
            }),
            requiresNaming: true,
            hasSemanticComponent: true,
            responseTime: collectorData.responseTime,
            correct: collectorData.isCorrectSelection,
            targetColor: collectorData.targetColor,
            selectedObject: rawData.selectedItem || null,
            association: 'color_category',
            conceptualAspect: 'discrimination',
            context: collectorData.difficulty,
            creativityRating: 0.5,
            strategy: 'systematic',
            usedVerbalization: false,
            usedVisualization: true,
            usedAssociation: true,
            usedCategorization: true,
            usedSystematicApproach: collectorData.difficulty === 'hard'
          }]
        },
        sessionDuration: collectorData.sessionDuration,
        difficulty: collectorData.difficulty
      }
    };
  }

  /**
   * Calcula métricas integradas combinando resultados de todos os coletores
   */
  calculateIntegratedMetrics(results) {
    const { colorPerceptionResults, visualProcessingResults, attentionalSelectivityResults, colorCognitionResults } = results;

    return {
      // Pontuação geral de percepção cromática
      overallColorPerception: this.calculateOverallColorPerception(results),
      
      // Índice de processamento visual
      visualProcessingIndex: (
        visualProcessingResults.processingSpeed +
        visualProcessingResults.visualScanning +
        visualProcessingResults.patternRecognition +
        visualProcessingResults.visualSearch
      ) / 4,
      
      // Índice de atenção seletiva
      attentionalIndex: (
        attentionalSelectivityResults.selectiveAttention +
        attentionalSelectivityResults.focusedAttention +
        attentionalSelectivityResults.inhibitoryControl +
        attentionalSelectivityResults.distractorResistance
      ) / 4,
      
      // Índice cognitivo cromático
      colorCognitionIndex: (
        colorCognitionResults.colorCategorization +
        colorCognitionResults.colorSemantics +
        colorCognitionResults.colorMemory +
        colorCognitionResults.processingSpeed
      ) / 4,
      
      // Métricas específicas do ColorMatch
      colorDiscriminationAbility: colorPerceptionResults.colorDiscrimination,
      colorMemoryCapacity: colorCognitionResults.colorMemory,
      visualAttentionEfficiency: this.calculateVisualAttentionEfficiency(results),
      chromaticProcessingSpeed: this.calculateChromaticProcessingSpeed(results),
      
      // Potencial de melhoria específico
      improvementPotential: this.calculateColorMatchImprovementPotential(results),
      
      // Estabilidade perceptual
      perceptualStability: this.calculatePerceptualStability(results)
    };
  }

  calculateOverallColorPerception(results) {
    const weights = {
      perception: 0.35,     // Percepção de cores
      processing: 0.25,     // Processamento visual
      attention: 0.20,      // Atenção seletiva
      cognition: 0.20       // Cognição cromática
    };

    const perceptionScore = (
      results.colorPerceptionResults.colorDiscrimination +
      results.colorPerceptionResults.huePerception +
      results.colorPerceptionResults.perceptualAccuracy
    ) / 3;
    
    const processingScore = (
      results.visualProcessingResults.processingSpeed +
      results.visualProcessingResults.processingEfficiency
    ) / 2;
    
    const attentionScore = (
      results.attentionalSelectivityResults.selectiveAttention +
      results.attentionalSelectivityResults.focusedAttention
    ) / 2;
    
    const cognitionScore = (
      results.colorCognitionResults.colorCategorization +
      results.colorCognitionResults.processingSpeed
    ) / 2;

    return (
      perceptionScore * weights.perception +
      processingScore * weights.processing +
      attentionScore * weights.attention +
      cognitionScore * weights.cognition
    );
  }

  calculateVisualAttentionEfficiency(results) {
    const visualEfficiency = results.visualProcessingResults.processingEfficiency || 0.7;
    const attentionalEfficiency = results.attentionalSelectivityResults.filteringEfficiency || 0.7;
    
    return (visualEfficiency + attentionalEfficiency) / 2;
  }

  calculateChromaticProcessingSpeed(results) {
    const perceptionSpeed = 1 - (results.colorPerceptionResults.adaptationEfficiency || 0.3);
    const processingSpeed = results.visualProcessingResults.processingSpeed || 0.7;
    const cognitionSpeed = results.colorCognitionResults.processingSpeed || 0.7;
    
    return (perceptionSpeed + processingSpeed + cognitionSpeed) / 3;
  }

  calculateColorMatchImprovementPotential(results) {
    const currentPerformance = this.calculateOverallColorPerception(results);
    const weakestAreas = this.identifyWeakestAreas(results);
    
    let potential = 1 - currentPerformance;
    
    // Ajustar baseado no número de áreas fracas
    if (weakestAreas.length >= 3) {
      potential *= 1.3; // Mais potencial se há múltiplas áreas para melhorar
    } else if (weakestAreas.length <= 1) {
      potential *= 0.8; // Menos potencial se já está bem
    }
    
    return Math.max(0, Math.min(1, potential));
  }

  calculatePerceptualStability(results) {
    const perceptionStability = results.colorPerceptionResults.colorConstancy || 0.7;
    const attentionStability = results.attentionalSelectivityResults.vigilance || 0.7;
    const processingStability = 1 - (results.visualProcessingResults.processingBottlenecks?.length || 0) * 0.2;
    
    return (perceptionStability + attentionStability + processingStability) / 3;
  }

  identifyWeakestAreas(results) {
    const areas = {
      colorDiscrimination: results.colorPerceptionResults.colorDiscrimination,
      visualProcessing: results.visualProcessingResults.processingEfficiency,
      attentionalControl: results.attentionalSelectivityResults.executiveAttention,
      colorCognition: results.colorCognitionResults.colorCategorization
    };
    
    return Object.entries(areas)
      .filter(([area, score]) => score < 0.6)
      .map(([area]) => area);
  }

  /**
   * Gera recomendações integradas baseadas nos resultados de todos os coletores
   */
  generateIntegratedRecommendations(results) {
    const recommendations = [];
    
    // Recomendações baseadas em percepção de cores
    if (results.colorPerceptionResults.colorDiscrimination < 0.6) {
      recommendations.push({
        category: 'color_perception',
        priority: 'high',
        title: 'Treino de Discriminação Cromática',
        description: 'Exercícios específicos para melhorar a capacidade de distinguir cores similares',
        techniques: ['gradientes de cor', 'jogos de diferenciação', 'treino de contraste'],
        targetArea: 'discriminação cromática'
      });
    }
    
    if (results.colorPerceptionResults.huePerception < 0.5) {
      recommendations.push({
        category: 'color_perception',
        priority: 'high',
        title: 'Desenvolvimento da Percepção de Matiz',
        description: 'Treino focado na identificação e diferenciação de matizes',
        techniques: ['círculo cromático', 'sequências de matiz', 'identificação de temperatura'],
        targetArea: 'percepção de matiz'
      });
    }
    
    // Recomendações baseadas em processamento visual
    if (results.visualProcessingResults.processingSpeed < 0.5) {
      recommendations.push({
        category: 'visual_processing',
        priority: 'high',
        title: 'Aceleração do Processamento Visual',
        description: 'Exercícios para aumentar a velocidade de processamento visual',
        techniques: ['tarefas cronometradas', 'pressão temporal gradual', 'automatização'],
        targetArea: 'velocidade de processamento'
      });
    }
    
    if (results.visualProcessingResults.visualScanning < 0.6) {
      recommendations.push({
        category: 'visual_processing',
        priority: 'medium',
        title: 'Otimização da Varredura Visual',
        description: 'Treino de padrões eficientes de varredura visual',
        techniques: ['padrões sistemáticos', 'estratégias de busca', 'mapas visuais'],
        targetArea: 'varredura visual'
      });
    }
    
    // Recomendações baseadas em atenção seletiva
    if (results.attentionalSelectivityResults.selectiveAttention < 0.6) {
      recommendations.push({
        category: 'attention',
        priority: 'high',
        title: 'Fortalecimento da Atenção Seletiva',
        description: 'Exercícios para melhorar a capacidade de filtrar informações irrelevantes',
        techniques: ['filtragem progressiva', 'foco dirigido', 'eliminação de distratores'],
        targetArea: 'atenção seletiva'
      });
    }
    
    if (results.attentionalSelectivityResults.inhibitoryControl < 0.5) {
      recommendations.push({
        category: 'attention',
        priority: 'high',
        title: 'Desenvolvimento do Controle Inibitório',
        description: 'Treino para fortalecer a capacidade de suprimir respostas inadequadas',
        techniques: ['tarefas stop-signal', 'controle de impulsos', 'pausa reflexiva'],
        targetArea: 'controle inibitório'
      });
    }
    
    // Recomendações baseadas em cognição cromática
    if (results.colorCognitionResults.colorCategorization < 0.6) {
      recommendations.push({
        category: 'color_cognition',
        priority: 'medium',
        title: 'Aprimoramento da Categorização Cromática',
        description: 'Exercícios para melhorar a organização conceitual de cores',
        techniques: ['classificação hierárquica', 'agrupamento semântico', 'taxonomias cromáticas'],
        targetArea: 'categorização'
      });
    }
    
    if (results.colorCognitionResults.colorMemory < 0.5) {
      recommendations.push({
        category: 'color_cognition',
        priority: 'medium',
        title: 'Fortalecimento da Memória Cromática',
        description: 'Treino específico para melhorar a retenção de informações sobre cores',
        techniques: ['associações mnemônicas', 'repetição espaçada', 'contextualização'],
        targetArea: 'memória cromática'
      });
    }
    
    // Recomendações integradas específicas
    const weakestAreas = this.identifyWeakestAreas(results);
    if (weakestAreas.length >= 2) {
      recommendations.push({
        category: 'integrated',
        priority: 'critical',
        title: 'Programa Integrado de Treino Cromático',
        description: 'Programa abrangente combinando múltiplas áreas de desenvolvimento',
        techniques: ['treino multimodal', 'progressão gradual', 'feedback contínuo'],
        targetArea: 'desenvolvimento integrado'
      });
    }
    
    return recommendations.sort((a, b) => {
      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Gera perfil específico do ColorMatch
   */
  generateColorMatchProfile(results) {
    const profile = {
      overallLevel: this.determineOverallLevel(results),
      strengths: this.identifyStrengths(results),
      weaknesses: this.identifyWeaknesses(results),
      dominantProcessingStyle: this.identifyDominantProcessingStyle(results),
      colorPerceptionProfile: this.generateColorPerceptionProfile(results),
      recommendations: this.generateProfileRecommendations(results)
    };
    
    return profile;
  }

  determineOverallLevel(results) {
    const overallScore = this.calculateOverallColorPerception(results);
    
    if (overallScore >= 0.85) return 'superior';
    if (overallScore >= 0.70) return 'above_average';
    if (overallScore >= 0.55) return 'average';
    if (overallScore >= 0.40) return 'below_average';
    return 'needs_improvement';
  }

  identifyStrengths(results) {
    const areas = {
      'Discriminação Cromática': results.colorPerceptionResults.colorDiscrimination,
      'Processamento Visual': results.visualProcessingResults.processingEfficiency,
      'Atenção Seletiva': results.attentionalSelectivityResults.selectiveAttention,
      'Cognição Cromática': results.colorCognitionResults.colorCategorization,
      'Velocidade de Processamento': results.visualProcessingResults.processingSpeed,
      'Memória Cromática': results.colorCognitionResults.colorMemory
    };
    
    return Object.entries(areas)
      .filter(([area, score]) => score >= 0.75)
      .map(([area, score]) => ({ area, score }))
      .sort((a, b) => b.score - a.score);
  }

  identifyWeaknesses(results) {
    const areas = {
      'Discriminação Cromática': results.colorPerceptionResults.colorDiscrimination,
      'Processamento Visual': results.visualProcessingResults.processingEfficiency,
      'Atenção Seletiva': results.attentionalSelectivityResults.selectiveAttention,
      'Cognição Cromática': results.colorCognitionResults.colorCategorization,
      'Velocidade de Processamento': results.visualProcessingResults.processingSpeed,
      'Memória Cromática': results.colorCognitionResults.colorMemory
    };
    
    return Object.entries(areas)
      .filter(([area, score]) => score < 0.6)
      .map(([area, score]) => ({ area, score }))
      .sort((a, b) => a.score - b.score);
  }

  identifyDominantProcessingStyle(results) {
    const styles = {
      visual: results.visualProcessingResults.processingEfficiency,
      cognitive: results.colorCognitionResults.processingSpeed,
      attentional: results.attentionalSelectivityResults.executiveAttention,
      perceptual: results.colorPerceptionResults.perceptualAccuracy
    };
    
    const dominantStyle = Object.entries(styles)
      .sort((a, b) => b[1] - a[1])[0];
    
    return {
      style: dominantStyle[0],
      strength: dominantStyle[1],
      description: this.getProcessingStyleDescription(dominantStyle[0])
    };
  }

  getProcessingStyleDescription(style) {
    const descriptions = {
      visual: 'Processamento predominantemente visual - responde bem a estímulos visuais claros',
      cognitive: 'Processamento predominantemente cognitivo - utiliza estratégias analíticas',
      attentional: 'Processamento predominantemente atencional - foca na seleção de informações',
      perceptual: 'Processamento predominantemente perceptual - confia na intuição perceptiva'
    };
    
    return descriptions[style] || 'Estilo misto de processamento';
  }

  generateColorPerceptionProfile(results) {
    const perception = results.colorPerceptionResults;
    
    return {
      discriminationLevel: this.categorizeScore(perception.colorDiscrimination),
      huePerceptionLevel: this.categorizeScore(perception.huePerception),
      saturationSensitivity: this.categorizeScore(perception.saturationSensitivity),
      brightnessDiscrimination: this.categorizeScore(perception.brightnessDiscrimination),
      colorConstancy: this.categorizeScore(perception.colorConstancy),
      dominantColorPreferences: perception.dominantColorPreferences || [],
      confusionPatterns: perception.colorConfusionMatrix || {}
    };
  }

  categorizeScore(score) {
    if (score >= 0.85) return 'excelente';
    if (score >= 0.70) return 'bom';
    if (score >= 0.55) return 'médio';
    if (score >= 0.40) return 'baixo';
    return 'crítico';
  }

  generateProfileRecommendations(results) {
    const overallLevel = this.determineOverallLevel(results);
    const weaknesses = this.identifyWeaknesses(results);
    
    const recommendations = [];
    
    if (overallLevel === 'needs_improvement') {
      recommendations.push('Programa intensivo de treino cromático recomendado');
    }
    
    if (weaknesses.length >= 3) {
      recommendations.push('Foco em desenvolvimento de habilidades fundamentais');
    }
    
    if (results.colorPerceptionResults.colorDiscrimination < 0.5) {
      recommendations.push('Avaliação oftalmológica recomendada para descartar problemas visuais');
    }
    
    return recommendations;
  }

  /**
   * Inicia uma nova sessão de coleta
   */
  startSession(sessionConfig = {}) {
    const sessionId = sessionConfig.sessionId || `colormatch_session_${Date.now()}`;
    
    this.currentSession = {
      id: sessionId,
      startTime: Date.now(),
      config: sessionConfig,
      collectedData: [],
      status: 'active',
      gameType: 'ColorMatch'
    };
    
    console.log(`🎨 Sessão ColorMatch ${sessionId} iniciada`);
    return this.currentSession;
  }

  /**
   * Coleta dados específicos do ColorMatch
   */
  collectColorMatchData(gameData) {
    if (!this.currentSession) {
      throw new Error('Nenhuma sessão ativa. Inicie uma sessão primeiro.');
    }
    
    const dataPoint = {
      timestamp: Date.now(),
      relativeTime: Date.now() - this.currentSession.startTime,
      data: gameData,
      type: this.identifyColorMatchDataType(gameData)
    };
    
    this.currentSession.collectedData.push(dataPoint);
    
    return {
      sessionId: this.currentSession.id,
      totalDataPoints: this.currentSession.collectedData.length,
      dataPoint: dataPoint
    };
  }

  identifyColorMatchDataType(gameData) {
    if (gameData.colorDiscrimination) return 'color_perception_data';
    if (gameData.visualScanning) return 'visual_processing_data';
    if (gameData.attentionMetrics) return 'attention_data';
    if (gameData.cognitiveMetrics) return 'cognition_data';
    return 'general_colormatch_data';
  }

  /**
   * Gera relatório abrangente da sessão ColorMatch
   */
  generateComprehensiveReport() {
    if (!this.currentSession) {
      throw new Error('Nenhuma sessão ativa para gerar relatório.');
    }
    
    return {
      sessionInfo: {
        id: this.currentSession.id,
        gameType: 'ColorMatch',
        startTime: this.currentSession.startTime,
        currentTime: Date.now(),
        duration: Date.now() - this.currentSession.startTime,
        status: this.currentSession.status
      },
      dataCollection: {
        totalDataPoints: this.currentSession.collectedData.length,
        dataTypes: this.categorizeColorMatchData(),
        qualityMetrics: this.assessColorMatchDataQuality(),
        colorSpecificMetrics: this.extractColorSpecificMetrics()
      },
      collectors: {
        colorPerception: { status: 'active', type: 'ColorPerceptionCollector' },
        visualProcessing: { status: 'active', type: 'VisualProcessingCollector' },
        attentionalSelectivity: { status: 'active', type: 'AttentionalSelectivityCollector' },
        colorCognition: { status: 'active', type: 'ColorCognitionCollector' }
      },
      recommendations: this.generateSessionRecommendations()
    };
  }

  categorizeColorMatchData() {
    const categories = {};
    
    this.currentSession.collectedData.forEach(point => {
      categories[point.type] = (categories[point.type] || 0) + 1;
    });
    
    return categories;
  }

  assessColorMatchDataQuality() {
    const dataPoints = this.currentSession.collectedData;
    
    const quality = {
      completeness: 0,
      consistency: 0,
      colorRelevance: 0,
      overall: 0
    };
    
    if (dataPoints.length === 0) return quality;
    
    // Completude específica para ColorMatch
    const completePoints = dataPoints.filter(point => 
      point.data && 
      (point.data.targetColor || point.data.selectedColor || point.data.colorResponse)
    ).length;
    quality.completeness = completePoints / dataPoints.length;
    
    // Consistência temporal
    const consistentPoints = dataPoints.filter(point => 
      point.timestamp && point.data && point.type
    ).length;
    quality.consistency = consistentPoints / dataPoints.length;
    
    // Relevância cromática
    const colorRelevantPoints = dataPoints.filter(point => 
      point.type.includes('color') || 
      (point.data && (point.data.targetColor || point.data.colorResponse))
    ).length;
    quality.colorRelevance = colorRelevantPoints / dataPoints.length;
    
    // Qualidade geral
    quality.overall = (quality.completeness + quality.consistency + quality.colorRelevance) / 3;
    
    return quality;
  }

  extractColorSpecificMetrics() {
    const dataPoints = this.currentSession.collectedData;
    
    const metrics = {
      colorsProcessed: new Set(),
      avgResponseTime: 0,
      accuracyByColor: {},
      mostChallengingColor: null,
      easiestColor: null
    };
    
    const responseTimes = [];
    
    dataPoints.forEach(point => {
      const data = point.data;
      
      if (data.targetColor) {
        metrics.colorsProcessed.add(data.targetColor);
        
        if (!metrics.accuracyByColor[data.targetColor]) {
          metrics.accuracyByColor[data.targetColor] = { correct: 0, total: 0 };
        }
        
        metrics.accuracyByColor[data.targetColor].total++;
        if (data.correct) {
          metrics.accuracyByColor[data.targetColor].correct++;
        }
        
        if (data.responseTime) {
          responseTimes.push(data.responseTime);
        }
      }
    });
    
    // Calcular métricas derivadas
    metrics.colorsProcessed = metrics.colorsProcessed.size;
    
    if (responseTimes.length > 0) {
      metrics.avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    }
    
    // Encontrar cor mais desafiadora e mais fácil
    const colorAccuracies = Object.entries(metrics.accuracyByColor)
      .map(([color, stats]) => ({
        color,
        accuracy: stats.total > 0 ? stats.correct / stats.total : 0,
        total: stats.total
      }))
      .filter(item => item.total >= 2); // Pelo menos 2 tentativas
    
    if (colorAccuracies.length > 0) {
      colorAccuracies.sort((a, b) => a.accuracy - b.accuracy);
      metrics.mostChallengingColor = colorAccuracies[0];
      metrics.easiestColor = colorAccuracies[colorAccuracies.length - 1];
    }
    
    return metrics;
  }

  generateSessionRecommendations() {
    const recommendations = [];
    const dataLength = this.currentSession.collectedData.length;
    
    if (dataLength === 0) {
      recommendations.push({
        type: 'data_collection',
        priority: 'high',
        description: 'Nenhum dado cromático coletado ainda. Inicie atividades para coleta de dados específicos de cores.'
      });
    } else if (dataLength < 10) {
      recommendations.push({
        type: 'data_collection',
        priority: 'medium',
        description: 'Poucos dados cromáticos coletados. Continue a atividade para obter análise mais precisa da percepção de cores.'
      });
    } else {
      recommendations.push({
        type: 'analysis_ready',
        priority: 'low',
        description: 'Dados cromáticos suficientes coletados. Pronto para análise detalhada da percepção e cognição de cores.'
      });
    }
    
    const colorMetrics = this.extractColorSpecificMetrics();
    if (colorMetrics.mostChallengingColor) {
      recommendations.push({
        type: 'color_specific',
        priority: 'medium',
        description: `Cor mais desafiadora identificada: ${colorMetrics.mostChallengingColor.color}. Considere treino específico.`
      });
    }
    
    return recommendations;
  }

  /**
   * Valida os dados do jogo antes da análise
   */
  validateGameData(gameData) {
    // Validação específica para ColorMatch
    const requiredFields = [
      'selectedItems',
      'gameGrid', 
      'difficulty',
      'targetColor',
      'timestamp',
      'sessionId'
    ];
    
    if (!gameData || typeof gameData !== 'object') {
      console.warn('🎨 ColorMatch: Dados inválidos recebidos');
      return false;
    }
    
    // Verificar campos obrigatórios
    for (const field of requiredFields) {
      if (!gameData.hasOwnProperty(field)) {
        console.warn(`🎨 ColorMatch: Campo obrigatório ausente: ${field}`);
        return false;
      }
    }
    
    // Validar estrutura específica
    if (!Array.isArray(gameData.selectedItems) || !Array.isArray(gameData.gameGrid)) {
      console.warn('🎨 ColorMatch: selectedItems e gameGrid devem ser arrays');
      return false;
    }
    
    // Validar dificuldade
    const validDifficulties = ['easy', 'medium', 'hard'];
    if (!validDifficulties.includes(gameData.difficulty)) {
      console.warn(`🎨 ColorMatch: Dificuldade inválida: ${gameData.difficulty}`);
      return false;
    }
    
    console.log(`🎨 ColorMatch: Dados validados para dificuldade ${gameData.difficulty}`);
    return true;
  }

  /**
   * Analisa o estado atual do grid
   */
  analyzeGridState(gameGrid, targetColor) {
    if (!Array.isArray(gameGrid)) {
      return { isCorrect: false, itemsRemaining: 0, totalCorrectItems: 0 };
    }
    
    // Contar itens da cor alvo no grid
    let totalCorrectItems = 0;
    let itemsRemaining = 0;
    
    gameGrid.forEach(item => {
      if (item && item.color === targetColor) {
        totalCorrectItems++;
        if (!item.selected) {
          itemsRemaining++;
        }
      }
    });
    
    return {
      isCorrect: itemsRemaining >= 0,
      itemsRemaining,
      totalCorrectItems,
      gridSize: gameGrid.length
    };
  }

  /**
   * Analisa o progresso da sessão
   */
  analyzeProgress(selectedItems, phaseConfig) {
    if (!Array.isArray(selectedItems)) {
      return { percentage: 0, sessionDuration: 0, accuracy: 0, averageResponseTime: 0, errorsCount: 0 };
    }
    
    const totalSelections = selectedItems.length;
    const correctSelections = selectedItems.filter(item => item.correct === true).length;
    const errorSelections = totalSelections - correctSelections;
    
    // Calcular duração da sessão
    const timestamps = selectedItems
      .map(item => item.timestamp)
      .filter(ts => ts && !isNaN(ts))
      .sort((a, b) => a - b);
    
    const sessionDuration = timestamps.length > 1 ? 
      timestamps[timestamps.length - 1] - timestamps[0] : 0;
    
    // Calcular tempo médio de resposta
    const responseTimes = selectedItems
      .map(item => item.responseTime)
      .filter(rt => rt && rt > 0);
    
    const averageResponseTime = responseTimes.length > 0 ?
      responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length : 0;
    
    // Calcular porcentagem de progresso
    const expectedItems = phaseConfig ? phaseConfig.expectedItems / phaseConfig.expectedColors : 3;
    const progressPercentage = Math.min((correctSelections / expectedItems) * 100, 100);
    
    return {
      percentage: progressPercentage,
      sessionDuration,
      accuracy: totalSelections > 0 ? (correctSelections / totalSelections) * 100 : 0,
      averageResponseTime,
      errorsCount: errorSelections
    };
  }

  /**
   * Gera análise de fallback em caso de erro
   */
  generateFallbackAnalysis(gameData, error) {
    return {
      sessionId: gameData?.sessionId || `fallback_colormatch_${Date.now()}`,
      timestamp: new Date().toISOString(),
      analysisTime: 0,
      error: error.message,
      status: 'fallback',
      gameType: 'ColorMatch',
      collectorsResults: {
        colorPerception: { colorDiscrimination: 0.5, huePerception: 0.5, perceptualAccuracy: 0.5 },
        visualProcessing: { processingSpeed: 0.5, processingEfficiency: 0.5 },
        attentionalSelectivity: { selectiveAttention: 0.5, focusedAttention: 0.5 },
        colorCognition: { colorCategorization: 0.5, processingSpeed: 0.5 }
      },
      integratedMetrics: {
        overallColorPerception: 0.5,
        improvementPotential: 0.5
      },
      recommendations: [{
        category: 'system',
        priority: 'critical',
        title: 'Erro na Análise ColorMatch',
        description: 'Houve um problema na análise cromática. Tente novamente ou verifique os dados fornecidos.',
        techniques: ['verificação de dados', 'nova tentativa']
      }]
    };
  }

  /**
   * Análise completa da sessão - FASE 1.2
   * Processa todos os dados coletados durante a sessão para relatório final
   */
  async analyzeCompleteSession() {
    try {
      if (!this.currentSession || !this.currentSession.active) {
        console.warn('🎨 ColorMatch: Nenhuma sessão ativa para análise');
        return null;
      }

      console.log('🎨 ColorMatch: Iniciando análise completa da sessão...');
      const startTime = Date.now();

      // Consolidar todas as interações da sessão
      const sessionInteractions = this.currentSession.interactions || [];
      const sessionConfig = this.currentSession.config || {};
      const phaseConfig = this.currentSession.phaseConfig || {};

      if (sessionInteractions.length === 0) {
        console.warn('🎨 ColorMatch: Sessão sem interações para análise');
        return this.generateEmptySessionAnalysis();
      }

      // Dados consolidados da sessão
      const sessionData = {
        sessionId: this.currentSession.sessionId,
        timestamp: Date.now(),
        difficulty: sessionConfig.difficulty,
        targetColor: 'session_complete', // Indica análise de sessão completa
        selectedItems: sessionInteractions.map(i => ({
          color: i.selectedColor,
          correct: i.isCorrect,
          timestamp: i.timestamp,
          responseTime: i.responseTime
        })),
        gameGrid: [], // Grid não é relevante para análise de sessão
        
        // Métricas consolidadas da sessão
        sessionMetrics: this.calculateSessionMetrics(sessionInteractions),
        phaseConfig: phaseConfig,
        totalInteractions: sessionInteractions.length,
        sessionDuration: Date.now() - this.currentSession.startTime,
        
        // Contexto da sessão
        gameVersion: this.currentSession.gameVersion || '2.0',
        userSettings: this.currentSession.userSettings || {}
      };

      console.log('🎨 ColorMatch: Dados da sessão consolidados:', {
        totalInteractions: sessionData.totalInteractions,
        sessionDuration: sessionData.sessionDuration,
        difficulty: sessionData.difficulty,
        accuracy: sessionData.sessionMetrics.overallAccuracy
      });

      // Executar análise completa com dados consolidados
      const analysisResult = await this.runCompleteAnalysis(sessionData);
      
      // Adicionar métricas específicas da sessão
      analysisResult.sessionAnalysis = {
        sessionId: this.currentSession.sessionId,
        totalDuration: sessionData.sessionDuration,
        interactionsCount: sessionData.totalInteractions,
        overallMetrics: sessionData.sessionMetrics,
        progressionAnalysis: this.analyzeProgressionThroughSession(sessionInteractions),
        learningCurve: this.calculateLearningCurve(sessionInteractions),
        consistencyMetrics: this.calculateConsistencyMetrics(sessionInteractions),
        finalRecommendations: this.generateSessionRecommendations(sessionData.sessionMetrics)
      };

      console.log(`✅ ColorMatch: Análise completa da sessão finalizada em ${Date.now() - startTime}ms`);
      console.log('🎨 ColorMatch: Resumo da sessão:', {
        overallAccuracy: analysisResult.sessionAnalysis.overallMetrics.overallAccuracy,
        learningCurveSlope: analysisResult.sessionAnalysis.learningCurve.slope,
        consistencyScore: analysisResult.sessionAnalysis.consistencyMetrics.consistencyScore
      });

      return analysisResult;

    } catch (error) {
      console.error('❌ ColorMatch: Erro na análise completa da sessão:', error);
      return this.generateFallbackSessionAnalysis(error);
    }
  }

  /**
   * Calcula métricas consolidadas da sessão
   */
  calculateSessionMetrics(interactions) {
    if (interactions.length === 0) {
      return { overallAccuracy: 0, averageResponseTime: 0, totalCorrect: 0, totalAttempts: 0 };
    }

    const totalAttempts = interactions.length;
    const correctInteractions = interactions.filter(i => i.isCorrect);
    const totalCorrect = correctInteractions.length;
    const overallAccuracy = (totalCorrect / totalAttempts) * 100;

    const responseTimes = interactions
      .map(i => i.responseTime)
      .filter(rt => rt && rt > 0);
    
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length 
      : 0;

    // Análise por cor (se disponível)
    const colorMetrics = {};
    interactions.forEach(interaction => {
      const targetColor = interaction.targetColor;
      if (targetColor && targetColor !== 'session_complete') {
        if (!colorMetrics[targetColor]) {
          colorMetrics[targetColor] = { total: 0, correct: 0, responseTimes: [] };
        }
        colorMetrics[targetColor].total++;
        if (interaction.isCorrect) {
          colorMetrics[targetColor].correct++;
        }
        if (interaction.responseTime) {
          colorMetrics[targetColor].responseTimes.push(interaction.responseTime);
        }
      }
    });

    return {
      overallAccuracy,
      averageResponseTime,
      totalCorrect,
      totalAttempts,
      colorMetrics,
      improvementTrend: this.calculateImprovementTrend(interactions),
      speedAccuracyBalance: this.calculateSpeedAccuracyBalance(interactions)
    };
  }

  /**
   * Analisa progressão durante a sessão
   */
  analyzeProgressionThroughSession(interactions) {
    const segmentSize = Math.max(1, Math.floor(interactions.length / 3));
    const segments = [
      interactions.slice(0, segmentSize),
      interactions.slice(segmentSize, segmentSize * 2),
      interactions.slice(segmentSize * 2)
    ].filter(segment => segment.length > 0);

    return segments.map((segment, index) => ({
      segment: index + 1,
      accuracy: segment.filter(i => i.isCorrect).length / segment.length * 100,
      averageResponseTime: segment.reduce((sum, i) => sum + (i.responseTime || 0), 0) / segment.length,
      interactionCount: segment.length
    }));
  }

  /**
   * Calcula curva de aprendizado
   */
  calculateLearningCurve(interactions) {
    if (interactions.length < 3) {
      return { slope: 0, trend: 'insufficient_data' };
    }

    const windowSize = Math.max(2, Math.floor(interactions.length / 5));
    const accuracyPoints = [];

    for (let i = 0; i <= interactions.length - windowSize; i += windowSize) {
      const window = interactions.slice(i, i + windowSize);
      const accuracy = window.filter(interaction => interaction.isCorrect).length / window.length;
      accuracyPoints.push(accuracy);
    }

    // Calcular slope da curva de aprendizado
    const slope = accuracyPoints.length > 1 
      ? (accuracyPoints[accuracyPoints.length - 1] - accuracyPoints[0]) / (accuracyPoints.length - 1)
      : 0;

    const trend = slope > 0.1 ? 'improving' : slope < -0.1 ? 'declining' : 'stable';

    return { slope, trend, accuracyPoints };
  }

  /**
   * Calcula métricas de consistência
   */
  calculateConsistencyMetrics(interactions) {
    if (interactions.length < 3) {
      return { consistencyScore: 0.5, variability: 'high' };
    }

    const responseTimes = interactions
      .map(i => i.responseTime)
      .filter(rt => rt && rt > 0);

    if (responseTimes.length === 0) {
      return { consistencyScore: 0.5, variability: 'unknown' };
    }

    const mean = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, rt) => sum + Math.pow(rt - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    const coefficientOfVariation = mean > 0 ? standardDeviation / mean : 1;

    const consistencyScore = Math.max(0, Math.min(1, 1 - coefficientOfVariation));
    const variability = coefficientOfVariation < 0.3 ? 'low' : coefficientOfVariation < 0.6 ? 'medium' : 'high';

    return { consistencyScore, variability, coefficientOfVariation };
  }

  /**
   * Calcula tendência de melhoria
   */
  calculateImprovementTrend(interactions) {
    if (interactions.length < 4) return 'insufficient_data';

    const midPoint = Math.floor(interactions.length / 2);
    const firstHalf = interactions.slice(0, midPoint);
    const secondHalf = interactions.slice(midPoint);

    const firstHalfAccuracy = firstHalf.filter(i => i.isCorrect).length / firstHalf.length;
    const secondHalfAccuracy = secondHalf.filter(i => i.isCorrect).length / secondHalf.length;

    const improvement = secondHalfAccuracy - firstHalfAccuracy;

    if (improvement > 0.15) return 'significant_improvement';
    if (improvement > 0.05) return 'moderate_improvement';
    if (improvement > -0.05) return 'stable';
    if (improvement > -0.15) return 'slight_decline';
    return 'significant_decline';
  }

  /**
   * Calcula equilíbrio velocidade-precisão
   */
  calculateSpeedAccuracyBalance(interactions) {
    const correctInteractions = interactions.filter(i => i.isCorrect);
    const incorrectInteractions = interactions.filter(i => !i.isCorrect);

    if (correctInteractions.length === 0 || incorrectInteractions.length === 0) {
      return { balance: 'skewed', type: correctInteractions.length === 0 ? 'accuracy_focused' : 'speed_focused' };
    }

    const avgCorrectTime = correctInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / correctInteractions.length;
    const avgIncorrectTime = incorrectInteractions.reduce((sum, i) => sum + (i.responseTime || 0), 0) / incorrectInteractions.length;

    const speedDifference = avgIncorrectTime - avgCorrectTime;

    if (Math.abs(speedDifference) < 200) return { balance: 'optimal', speedDifference };
    if (speedDifference > 500) return { balance: 'speed_over_accuracy', speedDifference };
    return { balance: 'accuracy_over_speed', speedDifference };
  }

  /**
   * Gera recomendações baseadas na sessão
   */
  generateSessionRecommendations(sessionMetrics) {
    const recommendations = [];

    if (sessionMetrics.overallAccuracy < 60) {
      recommendations.push({
        category: 'accuracy',
        priority: 'high',
        suggestion: 'Foco em precisão: considere reduzir velocidade e aumentar concentração'
      });
    }

    if (sessionMetrics.averageResponseTime > 3000) {
      recommendations.push({
        category: 'speed',
        priority: 'medium',
        suggestion: 'Trabalhar velocidade de processamento visual'
      });
    }

    if (sessionMetrics.improvementTrend === 'significant_decline') {
      recommendations.push({
        category: 'fatigue',
        priority: 'high',
        suggestion: 'Possível fadiga detectada - considerar pausas mais frequentes'
      });
    }

    if (sessionMetrics.overallAccuracy > 90 && sessionMetrics.averageResponseTime < 1500) {
      recommendations.push({
        category: 'advancement',
        priority: 'low',
        suggestion: 'Excelente performance - considerar aumentar dificuldade'
      });
    }

    return recommendations;
  }

  /**
   * Finalizar sessão e gerar relatório completo - FASE 1.2
   */
  async finalizeSession() {
    try {
      if (!this.currentSession || !this.currentSession.active) {
        console.warn('🎨 ColorMatch: Nenhuma sessão ativa para finalizar');
        return null;
      }

      console.log('🎨 ColorMatch: Finalizando sessão...');
      const startTime = Date.now();

      // Marcar sessão como finalizada
      this.currentSession.active = false;
      this.currentSession.endTime = Date.now();
      this.currentSession.totalDuration = this.currentSession.endTime - this.currentSession.startTime;

      // Executar análise completa da sessão
      const completeAnalysis = await this.analyzeCompleteSession();

      // Gerar relatório final consolidado
      const finalReport = {
        sessionId: this.currentSession.sessionId,
        gameType: 'ColorMatch',
        gameVersion: this.currentSession.gameVersion || '2.0',
        
        // Dados da sessão
        sessionData: {
          startTime: new Date(this.currentSession.startTime).toISOString(),
          endTime: new Date(this.currentSession.endTime).toISOString(),
          totalDuration: this.currentSession.totalDuration,
          difficulty: this.currentSession.config?.difficulty,
          phaseConfig: this.currentSession.phaseConfig,
          userSettings: this.currentSession.userSettings,
          totalInteractions: this.currentSession.interactions.length
        },

        // Resultados da análise completa
        analysisResults: completeAnalysis,

        // Resumo executivo da sessão
        executiveSummary: this.generateExecutiveSummary(completeAnalysis),

        // Recomendações terapêuticas
        therapeuticRecommendations: this.generateTherapeuticRecommendations(completeAnalysis),

        // Dados para próxima sessão
        nextSessionSuggestions: this.generateNextSessionSuggestions(completeAnalysis),

        // Metadados
        reportGeneratedAt: new Date().toISOString(),
        reportGenerationTime: Date.now() - startTime,
        dataVersion: '2.0'
      };

      console.log(`✅ ColorMatch: Sessão finalizada e relatório gerado em ${finalReport.reportGenerationTime}ms`);
      console.log('🎨 ColorMatch: Resumo da sessão finalizada:', {
        sessionId: finalReport.sessionId,
        duration: `${Math.round(finalReport.sessionData.totalDuration / 1000)}s`,
        interactions: finalReport.sessionData.totalInteractions,
        overallAccuracy: finalReport.executiveSummary.overallAccuracy,
        performanceLevel: finalReport.executiveSummary.performanceLevel
      });

      // Limpar dados da sessão (manter apenas o relatório)
      this.currentSession = null;

      return finalReport;

    } catch (error) {
      console.error('❌ ColorMatch: Erro ao finalizar sessão:', error);
      
      // Mesmo com erro, tentar gerar relatório básico
      const errorReport = {
        sessionId: this.currentSession?.sessionId || 'unknown',
        gameType: 'ColorMatch',
        status: 'error',
        error: error.message,
        sessionData: {
          totalInteractions: this.currentSession?.interactions.length || 0,
          duration: this.currentSession ? Date.now() - this.currentSession.startTime : 0
        },
        reportGeneratedAt: new Date().toISOString()
      };

      this.currentSession = null;
      return errorReport;
    }
  }

  /**
   * Gera resumo executivo da sessão
   */
  generateExecutiveSummary(analysisResults) {
    if (!analysisResults || !analysisResults.sessionAnalysis) {
      return {
        overallAccuracy: 0,
        performanceLevel: 'incomplete',
        keyFindings: ['Sessão incompleta ou sem dados suficientes']
      };
    }

    const sessionAnalysis = analysisResults.sessionAnalysis;
    const overallMetrics = sessionAnalysis.overallMetrics;

    // Determinar nível de performance
    let performanceLevel = 'developing';
    if (overallMetrics.overallAccuracy >= 90) performanceLevel = 'excellent';
    else if (overallMetrics.overallAccuracy >= 75) performanceLevel = 'proficient';
    else if (overallMetrics.overallAccuracy >= 60) performanceLevel = 'developing';
    else performanceLevel = 'needs_support';

    // Key findings
    const keyFindings = [];
    
    if (sessionAnalysis.learningCurve.trend === 'improving') {
      keyFindings.push('Demonstrou melhoria consistente durante a sessão');
    }
    
    if (sessionAnalysis.consistencyMetrics.variability === 'low') {
      keyFindings.push('Manteve performance consistente');
    }
    
    if (overallMetrics.averageResponseTime < 1500) {
      keyFindings.push('Velocidade de processamento visual adequada');
    }
    
    if (overallMetrics.speedAccuracyBalance?.balance === 'optimal') {
      keyFindings.push('Bom equilíbrio entre velocidade e precisão');
    }

    if (keyFindings.length === 0) {
      keyFindings.push('Sessão completada com dados básicos coletados');
    }

    return {
      overallAccuracy: Math.round(overallMetrics.overallAccuracy || 0),
      performanceLevel,
      keyFindings,
      totalInteractions: sessionAnalysis.interactionsCount,
      sessionDuration: Math.round(sessionAnalysis.totalDuration / 1000), // em segundos
      improvementTrend: sessionAnalysis.learningCurve?.trend || 'stable'
    };
  }

  /**
   * Gera recomendações terapêuticas específicas
   */
  generateTherapeuticRecommendations(analysisResults) {
    const recommendations = [];

    if (!analysisResults || !analysisResults.sessionAnalysis) {
      return [{
        category: 'data',
        priority: 'high',
        recommendation: 'Coletar mais dados para análise terapêutica adequada',
        rationale: 'Dados insuficientes na sessão atual'
      }];
    }

    const sessionAnalysis = analysisResults.sessionAnalysis;
    const overallMetrics = sessionAnalysis.overallMetrics;
    const collectorsResults = analysisResults.collectorsResults;

    // Recomendações baseadas em precisão
    if (overallMetrics.overallAccuracy < 60) {
      recommendations.push({
        category: 'accuracy',
        priority: 'high',
        recommendation: 'Focar em exercícios de discriminação visual e atenção sustentada',
        rationale: `Precisão atual de ${Math.round(overallMetrics.overallAccuracy)}% indica necessidade de suporte adicional`
      });
    }

    // Recomendações baseadas em velocidade
    if (overallMetrics.averageResponseTime > 3000) {
      recommendations.push({
        category: 'processing_speed',
        priority: 'medium',
        recommendation: 'Implementar exercícios para aumentar velocidade de processamento visual',
        rationale: `Tempo médio de resposta de ${Math.round(overallMetrics.averageResponseTime)}ms está acima do esperado`
      });
    }

    // Recomendações baseadas em coletores específicos
    if (collectorsResults.colorPerception?.colorDiscrimination < 0.6) {
      recommendations.push({
        category: 'color_perception',
        priority: 'medium',
        recommendation: 'Trabalhar especificamente com exercícios de discriminação cromática',
        rationale: 'Dificuldades detectadas na percepção e discriminação de cores'
      });
    }

    if (collectorsResults.attentionalSelectivity?.selectiveAttention < 0.6) {
      recommendations.push({
        category: 'attention',
        priority: 'high',
        recommendation: 'Incorporar atividades para desenvolver atenção seletiva',
        rationale: 'Déficits observados na capacidade de manter foco em estímulos específicos'
      });
    }

    // Recomendações baseadas em tendências
    if (sessionAnalysis.learningCurve?.trend === 'declining') {
      recommendations.push({
        category: 'fatigue',
        priority: 'high',
        recommendation: 'Considerar sessões mais curtas ou pausas mais frequentes',
        rationale: 'Declínio na performance sugere possível fadiga durante a atividade'
      });
    }

    return recommendations.length > 0 ? recommendations : [{
      category: 'continuation',
      priority: 'low',
      recommendation: 'Continuar com atividades similares para consolidar habilidades',
      rationale: 'Performance adequada observada na sessão atual'
    }];
  }

  /**
   * Gera sugestões para próxima sessão
   */
  generateNextSessionSuggestions(analysisResults) {
    if (!analysisResults || !analysisResults.sessionAnalysis) {
      return {
        suggestedDifficulty: 'easy',
        suggestedDuration: 300, // 5 minutos
        specificFocus: ['basic_color_recognition'],
        rationale: 'Dados insuficientes para recomendações específicas'
      };
    }

    const sessionAnalysis = analysisResults.sessionAnalysis;
    const overallMetrics = sessionAnalysis.overallMetrics;
    const currentDifficulty = analysisResults.gameData?.difficulty || 'easy';

    // Sugerir dificuldade para próxima sessão
    let suggestedDifficulty = currentDifficulty;
    
    if (overallMetrics.overallAccuracy >= 90 && overallMetrics.averageResponseTime < 1500) {
      // Performance excelente - pode aumentar dificuldade
      if (currentDifficulty === 'easy') suggestedDifficulty = 'medium';
      else if (currentDifficulty === 'medium') suggestedDifficulty = 'hard';
    } else if (overallMetrics.overallAccuracy < 50) {
      // Performance baixa - pode reduzir dificuldade
      if (currentDifficulty === 'hard') suggestedDifficulty = 'medium';
      else if (currentDifficulty === 'medium') suggestedDifficulty = 'easy';
    }

    // Sugerir duração baseada em consistência
    const baseDuration = 300; // 5 minutos base
    let suggestedDuration = baseDuration;
    
    if (sessionAnalysis.consistencyMetrics?.variability === 'low') {
      suggestedDuration = Math.min(600, baseDuration * 1.5); // Até 10 minutos se consistente
    } else if (sessionAnalysis.learningCurve?.trend === 'declining') {
      suggestedDuration = Math.max(180, baseDuration * 0.7); // Mínimo 3 minutos se há fadiga
    }

    // Focos específicos baseados na análise
    const specificFocus = [];
    
    if (overallMetrics.colorMetrics) {
      const colorAccuracies = Object.entries(overallMetrics.colorMetrics)
        .map(([color, metrics]) => ({
          color,
          accuracy: metrics.correct / metrics.total
        }))
        .filter(item => item.accuracy < 0.7);
      
      if (colorAccuracies.length > 0) {
        specificFocus.push(`cores_específicas: ${colorAccuracies.map(item => item.color).join(', ')}`);
      }
    }

    if (overallMetrics.averageResponseTime > 2500) {
      specificFocus.push('velocidade_processamento');
    }

    if (specificFocus.length === 0) {
      specificFocus.push('manutenção_habilidades');
    }

    return {
      suggestedDifficulty,
      suggestedDuration,
      specificFocus,
      rationale: `Baseado em precisão de ${Math.round(overallMetrics.overallAccuracy)}% e tempo médio de ${Math.round(overallMetrics.averageResponseTime)}ms`,
      adaptiveAdjustments: this.generateAdaptiveAdjustments(analysisResults)
    };
  }

  /**
   * Gera ajustes adaptativos para próxima sessão
   */
  generateAdaptiveAdjustments(analysisResults) {
    const adjustments = [];

    if (analysisResults?.sessionAnalysis?.overallMetrics?.speedAccuracyBalance?.balance === 'speed_over_accuracy') {
      adjustments.push({
        parameter: 'time_pressure',
        adjustment: 'reduce',
        reason: 'Priorizar precisão sobre velocidade'
      });
    }

    if (analysisResults?.sessionAnalysis?.consistencyMetrics?.variability === 'high') {
      adjustments.push({
        parameter: 'difficulty_progression',
        adjustment: 'gradual',
        reason: 'Variabilidade alta sugere necessidade de progressão mais suave'
      });
    }

    return adjustments;
  }

  // Métodos auxiliares para preparação de dados
  determineVisualComplexity(action) {
    if (action.distractors && action.distractors.length > 2) return 'high';
    if (action.similarColors && action.similarColors.length > 1) return 'medium';
    return 'low';
  }

  requiresInhibition(action) {
    return action.hasDistractors || action.requiresInhibition || false;
  }

  hasConflict(action) {
    return action.conflictingStimuli || action.hasConflict || false;
  }

  requiresTopDownControl(action) {
    return action.complexity === 'high' || action.requiresControl || false;
  }

  requiresFlexibility(action) {
    return action.contextChange || action.requiresFlexibility || false;
  }

  isMultitasking(action) {
    return action.multipleTargets || action.isMultitasking || false;
  }

  calculateMemoryLoad(action) {
    if (action.memoryLoad) return action.memoryLoad;
    if (action.complexity === 'high') return 3;
    if (action.complexity === 'medium') return 2;
    return 1;
  }

  identifyTaskType(action) {
    if (action.taskType) return action.taskType;
    if (action.requiresMemory) return 'memory';
    if (action.requiresNaming) return 'naming';
    if (action.hasSemanticComponent) return 'semantic';
    if (action.requiresCategorization) return 'categorization';
    return 'basic';
  }

  determineCognitiveComplexity(action) {
    if (action.complexity) return action.complexity;
    if (action.multipleSteps || action.abstracts) return 'complex';
    if (action.requiresComparison) return 'intermediate';
    return 'basic';
  }

  requiresNaming(action) {
    return action.requiresNaming || action.taskType === 'naming' || false;
  }

  hasSemanticComponent(action) {
    return action.hasSemanticComponent || action.objectAssociation || false;
  }

  identifyConceptualAspect(action) {
    if (action.conceptualAspect) return action.conceptualAspect;
    if (action.taskType === 'semantic') return 'semantic';
    if (action.taskType === 'categorization') return 'categorical';
    if (action.taskType === 'memory') return 'memory';
    return 'perceptual';
  }

  /**
   * Inicializar sessão de coleta - ATUALIZADO para nova estrutura
   */
  async initializeSession(sessionConfig) {
    try {
      // Suporte para ambos os formatos: novo (objeto) e antigo (sessionId + config)
      if (typeof sessionConfig === 'string') {
        // Formato antigo: primeiro parâmetro é sessionId
        const sessionId = sessionConfig;
        const config = arguments[1] || {};
        
        this.currentSession = {
          sessionId,
          config,
          startTime: Date.now(),
          interactions: [],
          active: true
        };
      } else {
        // Formato novo: objeto completo
        this.currentSession = {
          sessionId: sessionConfig.sessionId,
          config: sessionConfig,
          phaseConfig: sessionConfig.phaseConfig,
          userSettings: sessionConfig.userSettings,
          startTime: sessionConfig.startTime || Date.now(),
          interactions: [],
          active: true,
          gameVersion: sessionConfig.gameVersion || '2.0'
        };
      }
      
      console.log('🎨 ColorMatch: Sessão de coletores inicializada:', {
        sessionId: this.currentSession.sessionId,
        difficulty: this.currentSession.config.difficulty,
        gameVersion: this.currentSession.gameVersion,
        phaseConfig: this.currentSession.phaseConfig ? 'presente' : 'ausente'
      });
      
      return { 
        status: 'success', 
        sessionId: this.currentSession.sessionId,
        message: 'Sessão inicializada com nova estrutura'
      };
    } catch (error) {
      console.error('❌ ColorMatch: Erro ao inicializar sessão:', error);
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Coletar interação específica de cor - ATUALIZADO FASE 1.2
   */
  async collectColorInteraction(interactionData) {
    try {
      // Armazenar interação na sessão para análise posterior
      if (this.currentSession) {
        const sessionInteraction = {
          timestamp: interactionData.timestamp || Date.now(),
          selectedColor: interactionData.selectedColor,
          targetColor: interactionData.targetColor,
          isCorrect: interactionData.selectedItems && interactionData.selectedItems.length > 0 
            ? interactionData.selectedItems[interactionData.selectedItems.length - 1].correct
            : false,
          responseTime: interactionData.selectedItems && interactionData.selectedItems.length > 0
            ? interactionData.selectedItems[interactionData.selectedItems.length - 1].responseTime
            : 0,
          difficulty: interactionData.difficulty,
          gridPosition: interactionData.gridPosition,
          timeRemaining: interactionData.timeRemaining
        };

        this.currentSession.interactions.push(sessionInteraction);
        
        console.log('🎨 ColorMatch: Interação armazenada na sessão:', {
          totalInteractions: this.currentSession.interactions.length,
          isCorrect: sessionInteraction.isCorrect,
          targetColor: sessionInteraction.targetColor,
          selectedColor: sessionInteraction.selectedColor
        });
      }
      
      // FASE 1.2: Análise leve a cada interação
      // Não executar análise completa aqui - apenas validar e armazenar
      if (!this.validateGameData(interactionData)) {
        console.warn('🎨 ColorMatch: Dados de interação inválidos, mas continuando...');
        return { 
          status: 'warning', 
          message: 'Dados inválidos, mas interação registrada',
          interactionCount: this.currentSession?.interactions.length || 0
        };
      }
      
      // Análise simples para feedback imediato (sem processamento pesado)
      const quickAnalysis = {
        timestamp: Date.now(),
        difficulty: interactionData.difficulty,
        responseTime: interactionData.selectedItems && interactionData.selectedItems.length > 0
          ? interactionData.selectedItems[interactionData.selectedItems.length - 1].responseTime
          : 0,
        accuracy: this.currentSession 
          ? (this.currentSession.interactions.filter(i => i.isCorrect).length / this.currentSession.interactions.length) * 100
          : 100,
        interactionCount: this.currentSession?.interactions.length || 0
      };

      console.log('🎨 ColorMatch: Análise rápida da interação:', quickAnalysis);
      
      return { 
        status: 'success', 
        message: 'Interação coletada com sucesso',
        quickAnalysis,
        shouldPerformCompleteAnalysis: false // Análise completa será feita estrategicamente
      };
      
    } catch (error) {
      console.error('❌ ColorMatch: Erro ao coletar interação de cor:', error);
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Coletar dados abrangentes (compatibilidade)
   */
  async collectComprehensiveData(gameData) {
    try {
      // Usar o método principal de análise
      return await this.runCompleteAnalysis(gameData);
    } catch (error) {
      console.error('❌ Erro ao coletar dados abrangentes:', error);
      return { status: 'error', message: error.message };
    }
  }

  /**
   * Verificar se a sessão está ativa
   */
  get sessionActive() {
    return this.currentSession && this.currentSession.active;
  }
}

/**
 * Factory function para criar instância dos coletores
 * Função esperada pelos processadores do sistema
 */
export function createCollectors() {
  return new ColorMatchCollectorsHub();
}

/**
 * Função alternativa para obter coletores
 * Compatibilidade com diferentes padrões
 */
export function getCollectors() {
  return new ColorMatchCollectorsHub();
}

// Exportar também os coletores individuais para uso direto
export {
  ColorPerceptionCollector,
  VisualProcessingCollector,
  AttentionalSelectivityCollector,
  ColorCognitionCollector,
  ErrorPatternCollector
};
