import{j as e,P as o}from"./index-BkN74ywZ.js";import{r as s}from"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const t={container:"_container_1szyv_15",backButton:"_backButton_1szyv_45",heroBanner:"_heroBanner_1szyv_95",heroContent:"_heroContent_1szyv_109",heroTitle:"_heroTitle_1szyv_119",fadeInUp:"_fadeInUp_1szyv_1",heroSubtitle:"_heroSubtitle_1szyv_143",badgeContainer:"_badgeContainer_1szyv_161",techBadge:"_techBadge_1szyv_177",badgePrimary:"_badgePrimary_1szyv_209",badgeGreen:"_badgeGreen_1szyv_217",badgePurple:"_badgePurple_1szyv_225",alert:"_alert_1szyv_235",slideInRight:"_slideInRight_1szyv_1",alertSuccess:"_alertSuccess_1szyv_257",alertError:"_alertError_1szyv_269",section:"_section_1szyv_283",sectionTitle:"_sectionTitle_1szyv_297",bounce:"_bounce_1szyv_1",sectionContent:"_sectionContent_1szyv_329",exportOptions:"_exportOptions_1szyv_363",optionItem:"_optionItem_1szyv_371",optionLabel:"_optionLabel_1szyv_379",optionCheckbox:"_optionCheckbox_1szyv_411",optionText:"_optionText_1szyv_427",actionButtons:"_actionButtons_1szyv_463",actionButton:"_actionButton_1szyv_463",primaryButton:"_primaryButton_1szyv_515",successButton:"_successButton_1szyv_537",secondaryButton:"_secondaryButton_1szyv_559",importSection:"_importSection_1szyv_583",fileInput:"_fileInput_1szyv_591",fileLabel:"_fileLabel_1szyv_599",previewContainer:"_previewContainer_1szyv_639",previewContent:"_previewContent_1szyv_667",statusCard:"_statusCard_1szyv_699",statusOnline:"_statusOnline_1szyv_719",statusOffline:"_statusOffline_1szyv_729",statusIcon:"_statusIcon_1szyv_739",statusInfo:"_statusInfo_1szyv_749"};function a({onBack:o,userId:a="user_demo",isDbConnected:r=!1,userDetails:n=null,onImportComplete:i=()=>{}}){const[c,p]=s.useState(null),[l,u]=s.useState(!1),[m,x]=s.useState(!1),[b,d]=s.useState(null),[N,j]=s.useState(null),[v,E]=s.useState(null),[h,k]=s.useState({userProfiles:!0,gameProgress:!0,accessibilitySettings:!0,preferences:!0});s.useEffect(()=>{if(b){const e=setTimeout(()=>{d(null)},5e3);return()=>clearTimeout(e)}},[b]);const B=e=>{k(o=>({...o,[e]:!o[e]}))};return e.jsxDEV("div",{className:t.container,children:[o&&e.jsxDEV("button",{className:t.backButton,onClick:o,"aria-label":"Voltar para a página inicial",children:"← Voltar ao Menu Principal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:215,columnNumber:9},this),e.jsxDEV("div",{className:t.heroBanner,children:e.jsxDEV("div",{className:t.heroContent,children:[e.jsxDEV("h1",{className:t.heroTitle,children:"💾 Backup e Exportação"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:227,columnNumber:11},this),e.jsxDEV("p",{className:t.heroSubtitle,children:"Mantenha seus dados seguros e transfira suas informações entre dispositivos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:228,columnNumber:11},this),e.jsxDEV("div",{className:t.badgeContainer,children:[e.jsxDEV("span",{className:`${t.techBadge} ${t.badgePrimary}`,children:"🔒 Dados Seguros"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:232,columnNumber:13},this),e.jsxDEV("span",{className:`${t.techBadge} ${t.badgeGreen}`,children:"📱 Multi-dispositivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:235,columnNumber:13},this),e.jsxDEV("span",{className:`${t.techBadge} ${t.badgePurple}`,children:"⚡ Rápido e Fácil"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:238,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:231,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:226,columnNumber:9},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:225,columnNumber:7},this),b&&e.jsxDEV("div",{className:`${t.alert} ${t[`alert${b.type.charAt(0).toUpperCase()+b.type.slice(1)}`]}`,role:"alert",children:b.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:247,columnNumber:9},this),e.jsxDEV("section",{className:t.section,children:[e.jsxDEV("h2",{className:t.sectionTitle,children:[e.jsxDEV("span",{children:"📤"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:255,columnNumber:11},this),"Exportar Dados"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:254,columnNumber:9},this),e.jsxDEV("div",{className:t.sectionContent,children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Crie um backup completo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:260,columnNumber:13},this)," dos seus dados para guardar com segurança ou transferir para outro dispositivo. Selecione quais informações deseja incluir:"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:259,columnNumber:11},this),e.jsxDEV("div",{className:t.exportOptions,children:[e.jsxDEV("div",{className:t.optionItem,children:e.jsxDEV("label",{className:t.optionLabel,children:[e.jsxDEV("input",{type:"checkbox",checked:h.userProfiles,onChange:()=>B("userProfiles"),className:t.optionCheckbox},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:267,columnNumber:17},this),e.jsxDEV("span",{className:t.optionText,children:[e.jsxDEV("strong",{children:"👤 Perfis de usuário"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:274,columnNumber:19},this),e.jsxDEV("small",{children:"Informações dos perfis criados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:275,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:273,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:266,columnNumber:15},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:265,columnNumber:13},this),e.jsxDEV("div",{className:t.optionItem,children:e.jsxDEV("label",{className:t.optionLabel,children:[e.jsxDEV("input",{type:"checkbox",checked:h.gameProgress,onChange:()=>B("gameProgress"),className:t.optionCheckbox},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:282,columnNumber:17},this),e.jsxDEV("span",{className:t.optionText,children:[e.jsxDEV("strong",{children:"🎮 Progresso nos jogos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:289,columnNumber:19},this),e.jsxDEV("small",{children:"Histórico e pontuações dos jogos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:290,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:288,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:281,columnNumber:15},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:280,columnNumber:13},this),e.jsxDEV("div",{className:t.optionItem,children:e.jsxDEV("label",{className:t.optionLabel,children:[e.jsxDEV("input",{type:"checkbox",checked:h.accessibilitySettings,onChange:()=>B("accessibilitySettings"),className:t.optionCheckbox},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:297,columnNumber:17},this),e.jsxDEV("span",{className:t.optionText,children:[e.jsxDEV("strong",{children:"♿ Configurações de acessibilidade"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:304,columnNumber:19},this),e.jsxDEV("small",{children:"Preferências de alto contraste, fonte, etc."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:305,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:303,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:296,columnNumber:15},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:295,columnNumber:13},this),e.jsxDEV("div",{className:t.optionItem,children:e.jsxDEV("label",{className:t.optionLabel,children:[e.jsxDEV("input",{type:"checkbox",checked:h.preferences,onChange:()=>B("preferences"),className:t.optionCheckbox},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:312,columnNumber:17},this),e.jsxDEV("span",{className:t.optionText,children:[e.jsxDEV("strong",{children:"⚙️ Preferências gerais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:319,columnNumber:19},this),e.jsxDEV("small",{children:"Configurações personalizadas do sistema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:320,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:318,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:311,columnNumber:15},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:310,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:264,columnNumber:11},this),e.jsxDEV("div",{className:t.actionButtons,children:[e.jsxDEV("button",{onClick:async()=>{if(a){u(!0);try{let o={version:"1.0",exportDate:(new Date).toISOString(),data:{}};if(h.userProfiles&&(o.data.userId=a),h.gameProgress){const s={};for(let o=0;o<localStorage.length;o++){const t=localStorage.key(o);if(t.startsWith("betina_")&&t.includes("_history"))try{s[t]=JSON.parse(localStorage.getItem(t))}catch(e){s[t]=localStorage.getItem(t)}}o.data.localStorage=s}if(h.accessibilitySettings){const s=localStorage.getItem("betina_accessibility_settings");if(s)try{o.data.accessibilitySettings=JSON.parse(s)}catch(e){o.data.accessibilitySettings=s}}if(h.preferences){const s=localStorage.getItem("betina_user_preferences");if(s)try{o.data.preferences=JSON.parse(s)}catch(e){o.data.preferences=s}}p(o),d({type:"success",message:"Backup gerado com sucesso!"})}catch(o){d({type:"error",message:`Erro ao gerar backup: ${o.message}`})}finally{u(!1)}}else d({type:"error",message:"Erro: Não foi possível identificar o usuário."})},disabled:l||!a,className:`${t.actionButton} ${t.primaryButton}`,children:l?"🔄 Gerando...":"📦 Gerar Backup"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:327,columnNumber:13},this),c&&e.jsxDEV("button",{onClick:()=>{if(!c)return;const e=JSON.stringify(c,null,2),o=new Blob([e],{type:"application/json"}),s=document.createElement("a");s.href=URL.createObjectURL(o),s.download=`betina-backup-${(new Date).toISOString().split("T")[0]}.json`,s.click(),URL.revokeObjectURL(s.href)},className:`${t.actionButton} ${t.successButton}`,children:"💾 Baixar Backup"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:336,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:326,columnNumber:11},this),c&&e.jsxDEV("div",{className:t.previewContainer,children:[e.jsxDEV("h4",{children:"📋 Preview do Backup:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:347,columnNumber:15},this),e.jsxDEV("pre",{className:t.previewContent,children:e.jsxDEV("code",{children:[JSON.stringify(c,null,2).substring(0,500),"..."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:349,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:348,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:346,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:258,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:253,columnNumber:7},this),e.jsxDEV("section",{className:t.section,children:[e.jsxDEV("h2",{className:t.sectionTitle,children:[e.jsxDEV("span",{children:"📥"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:359,columnNumber:11},this),"Importar Dados"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:358,columnNumber:9},this),e.jsxDEV("div",{className:t.sectionContent,children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Restaure seus dados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:364,columnNumber:13},this)," a partir de um backup anterior. Selecione o arquivo de backup (.json) que você deseja importar:"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:363,columnNumber:11},this),e.jsxDEV("div",{className:t.importSection,children:[e.jsxDEV("input",{type:"file",accept:".json",onChange:e=>{const o=e.target.files[0];if(!o)return;j(o);const s=new FileReader;s.onload=e=>{try{const o=JSON.parse(e.target.result);E(o)}catch(o){d({type:"error",message:"Arquivo JSON inválido. Verifique o formato."}),j(null)}},s.readAsText(o)},className:t.fileInput,id:"import-file"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:369,columnNumber:13},this),e.jsxDEV("label",{htmlFor:"import-file",className:t.fileLabel,children:"📁 Escolher arquivo de backup (.json)"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:376,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:368,columnNumber:11},this),v&&e.jsxDEV(e.Fragment,{children:[e.jsxDEV("div",{className:t.previewContainer,children:[e.jsxDEV("h4",{children:"👀 Preview dos dados para importar:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:384,columnNumber:17},this),e.jsxDEV("pre",{className:t.previewContent,children:e.jsxDEV("code",{children:[JSON.stringify(v,null,2).substring(0,500),"..."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:386,columnNumber:19},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:385,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:383,columnNumber:15},this),e.jsxDEV("div",{className:t.actionButtons,children:[e.jsxDEV("button",{onClick:async()=>{if(v){x(!0);try{if(!v.version)throw new Error("Formato de backup inválido ou não reconhecido");v.data.localStorage&&Object.entries(v.data.localStorage).forEach(([e,o])=>{localStorage.setItem(e,JSON.stringify(o))}),v.data.accessibilitySettings&&localStorage.setItem("betina_accessibility_settings",JSON.stringify(v.data.accessibilitySettings)),v.data.preferences&&localStorage.setItem("betina_user_preferences",JSON.stringify(v.data.preferences)),d({type:"success",message:"Dados importados com sucesso! Pode ser necessário recarregar a página."}),j(null),E(null),i()}catch(e){d({type:"error",message:`Erro ao importar backup: ${e.message}`})}finally{x(!1)}}},disabled:m,className:`${t.actionButton} ${t.primaryButton}`,children:m?"🔄 Importando...":"📥 Importar Dados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:391,columnNumber:17},this),e.jsxDEV("button",{onClick:()=>{j(null),E(null)},className:`${t.actionButton} ${t.secondaryButton}`,children:"❌ Cancelar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:399,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:390,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:382,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:362,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:357,columnNumber:7},this),e.jsxDEV("section",{className:t.section,children:[e.jsxDEV("h2",{className:t.sectionTitle,children:[e.jsxDEV("span",{children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:417,columnNumber:11},this),"Sincronização Automática"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:416,columnNumber:9},this),e.jsxDEV("div",{className:t.sectionContent,children:[e.jsxDEV("p",{children:r?"Seus dados são sincronizados automaticamente quando você está conectado à internet.":"No modo offline, seus dados são armazenados apenas localmente neste dispositivo."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:421,columnNumber:11},this),e.jsxDEV("div",{className:`${t.statusCard} ${r?t.statusOnline:t.statusOffline}`,children:[e.jsxDEV("div",{className:t.statusIcon,children:r?"🟢":"🔴"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:428,columnNumber:13},this),e.jsxDEV("div",{className:t.statusInfo,children:[e.jsxDEV("h4",{children:["Status da Sincronização: ",r?"Ativada":"Desativada"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:432,columnNumber:15},this),e.jsxDEV("p",{children:r?"Seus dados estão sendo salvos automaticamente na nuvem.":"Seus dados estão sendo salvos apenas localmente. Considere fazer backup regularmente."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:433,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:431,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:427,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:420,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:415,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/BackupExport/BackupExport.jsx",lineNumber:212,columnNumber:5},this)}a.propTypes={onBack:o.func,userId:o.string,isDbConnected:o.bool,userDetails:o.object,onImportComplete:o.func};export{a as default};
