/**
 * @file SessionAnalyzer.js
 * @description Analisador de sessões terapêuticas - responsável por extrair insights das sessões
 * @version 3.0.0
 */

import logger from '../logger.js';
import SystemOrchestrator, { getSystemOrchestrator } from '../core/SystemOrchestrator.js';

/**
 * Analisador de Sessões - Extrai insights terapêuticos de sessões de jogos
 * 1. Analisar métricas e padrões
 * 2. Combinar insights de diferentes fontes
 * 3. Gerar relatórios compreensivos
 * 4. Identificar tendências e anomalias
 */
export class SessionAnalyzer {
  constructor () {
    this.systemOrchestrator = null;
    
    this.analyzerConfig = {
      analysisDepth: 'comprehensive', // comprehensive, standard, quick
      trendsAnalysis: {
        minSessions: 3, // Mínimo de sessões para análise de tendências
        timeframe: 'month', // Timeframe de análise (day, week, month, all)
        significanceThreshold: 0.15 // Limiar para mudança significativa
      },
      cognitiveAnalysis: {
        dimensions: [
          'attention', // Atenção
          'memory', // Memória
          'processing_speed', // Velocidade de processamento
          'executive_function', // Função executiva
          'language', // Linguagem
          'visual_spatial' // Habilidades visuoespaciais
        ],
        contextual: true // Considerar fatores contextuais
      },
      behavioralAnalysis: {
        patterns: [
          'engagement', // Engajamento
          'consistency', // Consistência
          'frustration', // Frustração
          'perseverance', // Perseverança
          'motivation' // Motivação
        ],
        sessionSegmentation: true // Analisar diferentes segmentos da sessão
      },
      insights: {
        individualizedRecommendations: true, // Recomendações personalizadas
        therapeuticGoals: true, // Sugestão de objetivos terapêuticos
        autismSpecific: true // Insights específicos para autismo
      }
    }

    // Não conectar ao SystemOrchestrator no construtor
    // Será conectado depois quando o SystemOrchestrator estiver pronto
  }

  /**
   * Injeta referência do SystemOrchestrator (evita dependência circular)
   * @param {Object} systemOrchestrator - Instância do SystemOrchestrator
   */
  setSystemOrchestrator(systemOrchestrator) {
    this.systemOrchestrator = systemOrchestrator;
    logger.info('SessionAnalyzer: SystemOrchestrator injetado com sucesso');
  }

  /**
   * Analisa uma sessão com métricas e histórico
   * @param {string} userId - ID do usuário
   * @param {string} gameId - ID do jogo
   * @param {Object} history - Histórico de sessões
   * @param {Object} metrics - Métricas atuais
   * @returns {Promise<Object>} - Análise terapêutica
   */  async analyze (userId, gameId, history, metrics) {
    try {
      // Notificar o orquestrador central sobre o início da análise de sessão
      if (this.systemOrchestrator) {
        await this.systemOrchestrator.notifyEvent('session_analysis_started', {
          userId,
          gameId,
          sessionCount: history?.length || 0,
          timestamp: new Date().toISOString()
        });
      }
      
      // 1. Análise Cognitiva
      const cognitiveAnalysis = this._analyzeCognitiveMetrics(metrics, history, gameId)

      // 2. Análise Comportamental
      const behavioralAnalysis = this._analyzeBehavioralPatterns(metrics, history)

      // 3. Análise de Progressão
      const progressionAnalysis = await this._analyzeProgressionTrends(userId, gameId, history)

      // 4. Análise de Consistência
      const consistencyAnalysis = this._analyzeConsistency(history)

      // 5. Integração das análises
      const integratedAnalysis = this._integrateAnalyses(
        cognitiveAnalysis,
        behavioralAnalysis,
        progressionAnalysis,
        consistencyAnalysis
      )

      // 6. Gerar recomendações terapêuticas
      const therapeuticRecommendations = this._generateRecommendations(
        integratedAnalysis,
        gameId
      )

      // 7. Identificar objetivos terapêuticos relevantes
      const therapeuticGoals = this._identifyTherapeuticGoals(
        integratedAnalysis,
        metrics,
        userId
      );
      
      const result = {
        timestamp: new Date().toISOString(),
        cognitiveProfile: cognitiveAnalysis.profile,
        cognitiveMetrics: cognitiveAnalysis.metrics,
        behavioralProfile: behavioralAnalysis.profile,
        behavioralMetrics: behavioralAnalysis.metrics,
        progressionPatterns: progressionAnalysis.patterns,
        consistencyIndicators: consistencyAnalysis.indicators,
        integratedInsights: integratedAnalysis.insights,
        therapeuticRecommendations,
        therapeuticGoals,
        confidence: this._calculateConfidenceScore(
          cognitiveAnalysis,
          behavioralAnalysis,
          history.length
        ),
        analysisDepth: this.analyzerConfig.analysisDepth
      }
      
      // Notificar o orquestrador central sobre a conclusão da análise de sessão
      if (this.systemOrchestrator) {
        await this.systemOrchestrator.notifyEvent('session_analysis_completed', {
          userId,
          gameId,
          summary: {
            confidence: result.confidence,
            recommendationsCount: therapeuticRecommendations.length,
            goalsCount: therapeuticGoals.length,
            insightsCount: integratedAnalysis.insights.length
          },
          timestamp: new Date().toISOString()
        });
      }

      return result
    } catch (error) {
      logger.error('Erro na análise de sessão:', error)
      
      // Notificar erro ao orquestrador central
      if (this.systemOrchestrator) {
        await this.systemOrchestrator.notifyEvent('session_analysis_error', {
          userId,
          gameId,
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
      
      return {
        timestamp: new Date().toISOString(),
        error: 'Falha na análise de sessão',
        partialAnalysis: true,
        cognitiveProfile: {},
        behavioralProfile: {},
        therapeuticRecommendations: [],
        therapeuticGoals: []
      }
    }
  }

  /**
   * Obter métricas do analisador de sessão para health checks
   * @returns {Object} Métricas do analisador
   */
  getMetrics() {
    return {
      totalSessions: this.sessionCount || 0,
      realTimeAnalyses: this.realTimeCount || 0,
      lastSessionTime: this.lastSessionAnalysis || null,
      analysisEnabled: true,
      cacheSize: this.cache ? this.cache.getMetrics().size : 0
    };
  }

  /**
   * Agrega métricas de múltiplas sessões
   * @param {Array} metrics - Conjunto de métricas
   * @returns {Promise<Object>} - Métricas agregadas
   */
  async aggregateMetrics (metrics) {
    // Implementação simplificada - em produção seria mais complexa
    const aggregated = {
      accuracy: {
        average: 0,
        trend: 'stable',
        variance: 0
      },
      responseTime: {
        average: 0,
        trend: 'improving',
        variance: 0
      },
      completion: {
        average: 0,
        trend: 'stable',
        variance: 0
      },
      engagement: {
        average: 0,
        trend: 'stable',
        variance: 0
      },
      difficulty: {
        average: 1,
        progression: 'adaptive',
        optimality: 0.8
      }
    }

    // Em produção, processaria métricas reais
    console.log(`Agregando ${metrics.length || 0} conjuntos de métricas`)

    return aggregated
  }

  /**
   * Gera um relatório compreensivo para um usuário
   * @param {string} userId - ID do usuário
   * @param {Array} sessions - Sessões do usuário
   * @returns {Promise<Object>} - Relatório compreensivo
   */
  async generateComprehensiveReport (userId, sessions) {
    try {
      // Em produção, faria análises complexas de todas as sessões
      console.log(`Gerando relatório para ${userId} com ${sessions.length} sessões`)

      return {
        userId,
        timestamp: new Date().toISOString(),
        sessionsAnalyzed: sessions.length,
        overallProgress: 'steady_improvement',
        cognitiveProfile: {
          attention: 7,
          memory: 6,
          processingSpeed: 8,
          executiveFunction: 6,
          language: 7,
          visualSpatial: 8
        },
        behavioralProfile: {
          engagement: 8,
          consistency: 7,
          frustration: 4,
          perseverance: 7,
          motivation: 8
        },
        strengths: ['visual_processing', 'pattern_recognition'],
        areasOfDevelopment: ['working_memory', 'sequential_processing'],
        therapeuticRecommendations: [
          {
            area: 'memory',
            recommendation: 'Aumentar atividades de memória funcional',
            games: ['MemoryGame', 'SequenceRecall']
          }
        ]
      }
    } catch (error) {
      console.error('Erro ao gerar relatório:', error)
      return {
        error: 'Falha ao gerar relatório',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Combina insights terapêuticos de múltiplas fontes
   * @param {Object} sessionReport - Relatório de sessão
   * @param {Object} multisensoryReport - Relatório multissensorial
   * @returns {Promise<Object>} - Insights combinados
   */
  async combineTherapeuticInsights (sessionReport, multisensoryReport) {
    try {
      // Base dos insights combinados
      const combinedInsights = {
        timestamp: new Date().toISOString(),
        sources: ['session_data'],
        cognitiveProfile: sessionReport.cognitiveProfile,
        behavioralProfile: sessionReport.behavioralProfile,
        strengths: sessionReport.strengths,
        areasOfDevelopment: sessionReport.areasOfDevelopment,
        therapeuticRecommendations: sessionReport.therapeuticRecommendations
      }

      // Adicionar insights multissensoriais se disponíveis
      if (multisensoryReport && multisensoryReport.available) {
        combinedInsights.sources.push('multisensory_data')

        // Enriquecimento de perfis com dados multissensoriais
        // Em produção, teria lógica complexa de combinação

        console.log('Insights multissensoriais incorporados com sucesso')
      }

      return combinedInsights
    } catch (error) {
      console.error('Erro ao combinar insights terapêuticos:', error)
      return {
        error: 'Falha ao combinar insights',
        timestamp: new Date().toISOString(),
        fallbackInsights: sessionReport
      }
    }
  }

  /**
   * Analisa métricas cognitivas
   * @private
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @param {string} gameId - ID do jogo
   * @returns {Object} - Análise cognitiva
   */
  _analyzeCognitiveMetrics (metrics, history, gameId) {
    // Dimensões cognitivas mapeadas por tipo de jogo
    const gameCognitiveFocus = {
      ColorMatch: ['attention', 'visual_processing', 'processing_speed'],
      MemoryGame: ['memory', 'attention', 'visual_processing'],
      NumberCounting: ['numerical_cognition', 'attention', 'sequential_processing'],
      LetterRecognition: ['language', 'visual_processing', 'symbolic_understanding'],
      MusicalSequence: ['auditory_processing', 'memory', 'sequential_processing'],
      PadroesVisuais: ['pattern_recognition', 'visual_processing', 'attention'],
      QuebraCabeca: ['spatial_reasoning', 'visual_processing', 'problem_solving'],
      ImageAssociation: ['categorical_thinking', 'semantic_understanding', 'visual_processing']
    }

    // Dimensões relevantes para este jogo
    const relevantDimensions = gameCognitiveFocus[gameId] || ['attention', 'processing_speed']

    // Perfil cognitivo baseado nas métricas e tipo de jogo
    const profile = {
      attention: this._calculateCognitiveDimension('attention', metrics, history),
      memory: this._calculateCognitiveDimension('memory', metrics, history),
      processingSpeed: this._calculateCognitiveDimension('processing_speed', metrics, history),
      executiveFunction: this._calculateCognitiveDimension('executive_function', metrics, history),
      language: this._calculateCognitiveDimension('language', metrics, history),
      visualSpatial: this._calculateCognitiveDimension('visual_spatial', metrics, history),
      relevantFocus: relevantDimensions
    }

    // Métricas cognitivas específicas
    const cognitiveMetrics = {
      responseConsistency: this._calculateResponseConsistency(metrics, history),
      errorPatterns: this._analyzeErrorPatterns(metrics, history),
      learningCurve: this._analyzeLearningCurve(history),
      adaptabilityIndex: this._calculateAdaptabilityIndex(metrics, history),
      cognitiveStamina: this._calculateCognitiveStamina(metrics)
    }

    return {
      profile,
      metrics: cognitiveMetrics,
      timestamp: new Date().toISOString(),
      confidence: this._calculateAnalysisConfidence(metrics, history)
    }
  }

  /**
   * Calcula valor de uma dimensão cognitiva específica
   * @private
   * @param {string} dimension - Nome da dimensão
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Avaliação da dimensão
   */
  _calculateCognitiveDimension (dimension, metrics, history) {
    // Em produção, teria algoritmos complexos para cada dimensão
    // Aqui, simulamos um cálculo simplificado

    const base = {
      attention: 0.7,
      memory: 0.6,
      processing_speed: 0.75,
      executive_function: 0.65,
      language: 0.8,
      visual_spatial: 0.7
    }[dimension] || 0.5

    // Adicionar variação aleatória para simular
    const score = Math.min(1, Math.max(0, base + (Math.random() * 0.2 - 0.1)))

    return {
      score: Math.round(score * 10), // Escala 0-10
      percentile: Math.round(score * 100),
      classification: this._classifyCognitiveScore(score),
      trend: ['stable', 'improving', 'variable'][Math.floor(Math.random() * 3)]
    }
  }

  /**
   * Classifica pontuação cognitiva
   * @private
   * @param {number} score - Pontuação (0-1)
   * @returns {string} - Classificação
   */
  _classifyCognitiveScore (score) {
    if (score < 0.3) return 'needs_significant_support'
    if (score < 0.5) return 'needs_support'
    if (score < 0.7) return 'developing'
    if (score < 0.9) return 'proficient'
    return 'advanced'
  }

  /**
   * Calcula consistência de respostas
   * @private
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Avaliação de consistência
   */
  _calculateResponseConsistency (metrics, history) {
    // Em produção, analisaria variância real nas métricas
    return {
      score: 7,
      classification: 'consistent',
      variabilityIndex: 0.2
    }
  }

  /**
   * Analisa padrões de erro
   * @private
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Análise de erros
   */
  _analyzeErrorPatterns (metrics, history) {
    // Em produção, analisaria tipos específicos e distribuição de erros
    return {
      patternDetected: false,
      predominantErrorType: 'random',
      systematicErrors: []
    }
  }

  /**
   * Analisa curva de aprendizado
   * @private
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Análise da curva
   */
  _analyzeLearningCurve (history) {
    // Em produção, analisaria progressão e inclinação real da curva
    return {
      shape: 'steady_improvement',
      plateaus: 1,
      significantGains: true
    }
  }

  /**
   * Calcula índice de adaptabilidade
   * @private
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Índice de adaptabilidade
   */
  _calculateAdaptabilityIndex (metrics, history) {
    // Em produção, mediria quão bem o usuário se adapta a mudanças
    return {
      score: 7,
      adaptationSpeed: 'moderate',
      resistanceToChange: 'low'
    }
  }

  /**
   * Calcula estamina cognitiva
   * @private
   * @param {Object} metrics - Métricas atuais
   * @returns {Object} - Avaliação de estamina
   */
  _calculateCognitiveStamina (metrics) {
    // Em produção, analisaria degradação de performance ao longo da sessão
    return {
      enduranceLevel: 'good',
      fatigueOnset: 'late',
      consistencyOverTime: 'stable'
    }
  }

  /**
   * Analisa padrões comportamentais
   * @private
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Análise comportamental
   */
  _analyzeBehavioralPatterns (metrics, history) {
    // Em produção, utilizaria análise comportamental complexa
    // Aqui simulamos perfil e métricas básicas

    const profile = {
      engagement: {
        score: 8,
        classification: 'high',
        trend: 'stable'
      },
      consistency: {
        score: 7,
        classification: 'good',
        trend: 'improving'
      },
      frustration: {
        score: 3,
        classification: 'low',
        trend: 'decreasing'
      },
      perseverance: {
        score: 8,
        classification: 'high',
        trend: 'stable'
      },
      motivation: {
        score: 7,
        classification: 'good',
        trend: 'variable'
      }
    }

    const behavioralMetrics = {
      sessionEngagement: {
        beginning: 'high',
        middle: 'high',
        end: 'moderate'
      },
      behavioralConsistency: 'stable',
      frustrationIndicators: {
        frequency: 'rare',
        intensity: 'mild',
        triggers: ['difficult_items']
      },
      motivationPatterns: {
        intrinsic: 'high',
        extrinsic: 'moderate',
        sustainment: 'good'
      }
    }

    return {
      profile,
      metrics: behavioralMetrics,
      timestamp: new Date().toISOString(),
      confidence: this._calculateAnalysisConfidence(metrics, history)
    }
  }

  /**
   * Analisa tendências de progressão
   * @private
   * @param {string} userId - ID do usuário
   * @param {string} gameId - ID do jogo
   * @param {Object} history - Histórico de sessões
   * @returns {Promise<Object>} - Análise de progressão
   */
  async _analyzeProgressionTrends (userId, gameId, history) {
    // Em produção, analisaria dados reais de progressão ao longo do tempo
    // Aqui simulamos análises básicas

    const patterns = {
      overall: {
        trend: 'improving',
        rate: 'steady',
        significanceScore: 0.8
      },
      accuracy: {
        trend: 'improving',
        rate: 'rapid',
        plateaus: false,
        regressions: false
      },
      speed: {
        trend: 'improving',
        rate: 'moderate',
        plateaus: true,
        plateauPoints: [5, 12]
      },
      difficulty: {
        progression: 'appropriate',
        adaptationRate: 'well_matched',
        optimalChallenge: true
      }
    }

    return {
      patterns,
      timeframe: 'all_sessions',
      confidence: history && history.length > 5 ? 'high' : 'moderate',
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Analisa consistência entre sessões
   * @private
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Análise de consistência
   */
  _analyzeConsistency (history) {
    // Em produção, mediria variância real entre sessões
    // Aqui simulamos indicadores básicos

    const indicators = {
      performanceConsistency: {
        score: 7,
        classification: 'consistent',
        variabilityIndex: 0.25
      },
      environmentalFactors: {
        impactLevel: 'moderate',
        significantFactors: ['time_of_day', 'device_type']
      },
      behavioralConsistency: {
        score: 8,
        classification: 'highly_consistent',
        variabilityIndex: 0.15
      }
    }

    return {
      indicators,
      confidence: history && history.length > 3 ? 'high' : 'low',
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Integra diferentes análises
   * @private
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @param {Object} behavioralAnalysis - Análise comportamental
   * @param {Object} progressionAnalysis - Análise de progressão
   * @param {Object} consistencyAnalysis - Análise de consistência
   * @returns {Object} - Análises integradas
   */
  _integrateAnalyses (cognitiveAnalysis, behavioralAnalysis, progressionAnalysis, consistencyAnalysis) {
    // Em produção, aplicaria algoritmos complexos de integração
    // Aqui simulamos insights básicos

    const insights = {
      strengths: [
        'visual_processing',
        'pattern_recognition',
        'sustained_attention'
      ],
      challenges: [
        'working_memory',
        'cognitive_flexibility'
      ],
      learningStyle: {
        primary: 'visual',
        secondary: 'kinesthetic',
        processingType: 'global'
      },
      interactionPreferences: {
        pace: 'moderately_fast',
        feedback: 'immediate_visual',
        support: 'minimal_prompting'
      }
    }

    return {
      insights,
      confidenceScore: this._calculateIntegratedConfidence([
        cognitiveAnalysis,
        behavioralAnalysis,
        progressionAnalysis,
        consistencyAnalysis
      ]),
      timestamp: new Date().toISOString()
    }
  }

  /**
   * Gera recomendações terapêuticas
   * @private
   * @param {Object} integratedAnalysis - Análises integradas
   * @param {string} gameId - ID do jogo
   * @returns {Array} - Recomendações terapêuticas
   */
  _generateRecommendations (integratedAnalysis, gameId) {
    // Em produção, utilizaria sistema complexo de recomendações
    // Aqui simulamos recomendações básicas

    return [
      {
        area: 'cognitive',
        focus: 'working_memory',
        recommendation: 'Aumentar progressivamente sequências na atividade MemoryGame',
        expectedBenefit: 'Melhoria na capacidade de memória operacional',
        priority: 'high',
        games: ['MemoryGame', 'SequenceRecall']
      },
      {
        area: 'behavioral',
        focus: 'engagement',
        recommendation: 'Manter sessões curtas (10-15 min) com alto feedback visual',
        expectedBenefit: 'Manutenção do engajamento em níveis ideais',
        priority: 'medium',
        games: ['ColorMatch', 'ImageAssociation']
      },
      {
        area: 'adaptive',
        focus: 'difficulty_progression',
        recommendation: 'Usar progressão de dificuldade mais gradual',
        expectedBenefit: 'Reduzir frustração enquanto mantém desafio adequado',
        priority: 'medium',
        applicableToAllGames: true
      }
    ]
  }

  /**
   * Identifica objetivos terapêuticos
   * @private
   * @param {Object} integratedAnalysis - Análises integradas
   * @param {Object} metrics - Métricas atuais
   * @param {string} userId - ID do usuário
   * @returns {Array} - Objetivos terapêuticos
   */
  _identifyTherapeuticGoals (integratedAnalysis, metrics, userId) {
    // Em produção, alinharia com planos terapêuticos existentes
    // Aqui simulamos objetivos básicos

    return [
      {
        area: 'cognitive',
        domain: 'working_memory',
        currentLevel: 6,
        targetLevel: 8,
        description: 'Aumentar capacidade de memória operacional',
        strategies: [
          'Iniciar com sequências curtas e aumentar gradualmente',
          'Incorporar dicas visuais que são gradualmente removidas'
        ],
        estimatedTimeframe: '4 weeks',
        measuredBy: 'MemoryGame metrics, span tasks',
        priority: 'high'
      },
      {
        area: 'behavioral',
        domain: 'sustained_attention',
        currentLevel: 7,
        targetLevel: 9,
        description: 'Melhorar capacidade de sustentar atenção em tarefas estruturadas',
        strategies: [
          'Sessões gradualmente mais longas',
          'Reduzir gradualmente estímulos distratores'
        ],
        estimatedTimeframe: '6 weeks',
        measuredBy: 'Session duration analytics, completion rates',
        priority: 'medium'
      }
    ]
  }

  /**
   * Calcula confiança da análise
   * @private
   * @param {Object} metrics - Métricas atuais
   * @param {Object} history - Histórico de sessões
   * @returns {Object} - Pontuação de confiança
   */
  _calculateAnalysisConfidence (metrics, history) {
    // Em produção, calcularia confiança real baseada em dados
    // Aqui simulamos pontuação básica

    const historyLength = history && history.length ? history.length : 0

    let score
    if (historyLength > 10) score = 0.9
    else if (historyLength > 5) score = 0.8
    else if (historyLength > 2) score = 0.7
    else score = 0.6

    return {
      score,
      level: score > 0.8 ? 'high' : score > 0.6 ? 'moderate' : 'low',
      factors: {
        dataCompleteness: historyLength > 5 ? 'good' : 'limited',
        metricsQuality: 'standard',
        historicalData: historyLength > 2 ? 'available' : 'limited'
      }
    }
  }

  /**
   * Calcula confiança integrada das análises
   * @private
   * @param {Array} analyses - Conjunto de análises
   * @returns {Object} - Pontuação de confiança
   */
  _calculateIntegratedConfidence (analyses) {
    // Em produção, ponderaria diferentes análises
    // Aqui simulamos pontuação básica

    return {
      score: 0.8,
      level: 'high',
      lowestComponent: 'consistencyAnalysis',
      highestComponent: 'cognitiveAnalysis'
    }
  }

  /**
   * Calcula pontuação de confiança geral
   * @private
   * @param {Object} cognitiveAnalysis - Análise cognitiva
   * @param {Object} behavioralAnalysis - Análise comportamental
   * @param {number} sessionsCount - Número de sessões
   * @returns {number} - Pontuação de confiança (0-1)
   */
  _calculateConfidenceScore (cognitiveAnalysis, behavioralAnalysis, sessionsCount) {
    // Base da confiança
    let score = 0.6

    // Ajuste pelo número de sessões
    if (sessionsCount > 10) score += 0.2
    else if (sessionsCount > 5) score += 0.1
    else if (sessionsCount < 2) score -= 0.1

    // Ajuste por confiança das análises individuais
    if (cognitiveAnalysis && cognitiveAnalysis.confidence) {
      score += cognitiveAnalysis.confidence.score * 0.1
    }

    if (behavioralAnalysis && behavioralAnalysis.confidence) {
      score += behavioralAnalysis.confidence.score * 0.1
    }

    // Limitar entre 0 e 1
    return Math.max(0, Math.min(1, score))
  }

  /**
   * Conecta ao orquestrador central do sistema
   * @param {Object} databaseService - Optional database service for initialization
   */
  connectToSystemOrchestrator(databaseService = null) {
    try {
      this.systemOrchestrator = getSystemOrchestrator(databaseService);
      
      if (this.systemOrchestrator) {
        logger.info('SessionAnalyzer conectado ao SystemOrchestrator');
      } else {
        logger.warn('SystemOrchestrator ainda não disponível');
      }
    } catch (error) {
      logger.error('Erro ao conectar ao SystemOrchestrator:', error);
    }
  }
}

// Instância singleton do SessionAnalyzer
let sessionAnalyzerInstance = null;

/**
 * Retorna a instância singleton do SessionAnalyzer
 * @param {Object} options - Opções de configuração
 * @returns {SessionAnalyzer} Instância do SessionAnalyzer
 */
export const getSessionAnalyzer = (options = {}) => {
  if (!sessionAnalyzerInstance) {
    sessionAnalyzerInstance = new SessionAnalyzer(options);
  }
  return sessionAnalyzerInstance;
};

// Exportação padrão para importação direta
export default SessionAnalyzer;
