import{u as e,j as s}from"./index-CrlBCiRw.js";import{r as i}from"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const a="_container_xqmmk_15",t="_pageHeader_xqmmk_33",c="_backButton_xqmmk_53",o="_pageTitle_xqmmk_89",l="_pageSubtitle_xqmmk_103",n="_pageContent_xqmmk_117",r="_accessibilityPanel_xqmmk_127",m="_panelContent_xqmmk_139",b="_panelInfo_xqmmk_147",p="_accessibilityGroup_xqmmk_171",u="_groupTitle_xqmmk_191",N="_presetsGrid_xqmmk_207",g="_presetButton_xqmmk_219",d="_optionRow_xqmmk_257",P="_optionLabel_xqmmk_267",j="_optionText_xqmmk_281",h="_switchContainer_xqmmk_295",x="_switchInput_xqmmk_307",v="_switchSlider_xqmmk_319",y="_selectInput_xqmmk_385",A="_successMessage_xqmmk_419";function f({onBack:f}){const{settings:C,updateSettings:E,applyPreset:D}=e(),[V,_]=i.useState(!1);i.useEffect(()=>{(()=>{const e=document.documentElement;C.highContrast?e.classList.add("high-contrast"):e.classList.remove("high-contrast"),e.classList.remove("font-small","font-medium","font-large","font-extra-large"),e.classList.add(`font-${C.fontSize}`),C.dyslexiaFriendly?e.classList.add("dyslexia-friendly"):e.classList.remove("dyslexia-friendly"),C.reducedMotion?e.classList.add("reduced-motion"):e.classList.remove("reduced-motion"),e.classList.remove("scheme-default","scheme-dark","scheme-soft","scheme-high-contrast"),e.classList.add(`scheme-${C.colorScheme}`);const s=document.getElementById("accessibility-styles");s&&s.remove();const i=document.createElement("style");i.id="accessibility-styles",i.textContent="\n        .high-contrast * {\n          filter: contrast(1.5) !important;\n          border: 1px solid #000 !important;\n        }\n        \n        .font-small { font-size: 12px !important; }\n        .font-medium { font-size: 16px !important; }\n        .font-large { font-size: 20px !important; }\n        .font-extra-large { font-size: 24px !important; }\n        \n        .dyslexia-friendly * {\n          font-family: 'OpenDyslexic', 'Comic Sans MS', cursive !important;\n        }\n        \n        .reduced-motion * {\n          animation: none !important;\n          transition: none !important;\n        }\n        \n        .scheme-dark {\n          background: #1a1a1a !important;\n          color: #ffffff !important;\n        }\n        \n        .scheme-soft {\n          filter: brightness(0.9) saturate(0.8) !important;\n        }\n        \n        .scheme-high-contrast {\n          filter: contrast(2) brightness(1.2) !important;\n        }\n      ",document.head.appendChild(i)})()},[C]);const[k,S]=i.useState(C.textToSpeech),q=()=>{_(!0),setTimeout(()=>_(!1),2e3)},L=e=>{if(C.textToSpeech&&"speechSynthesis"in window){const s=new SpeechSynthesisUtterance(e);s.rate=.8,s.pitch=1,speechSynthesis.speak(s)}},T=i.useCallback((e,s)=>{E({[e]:s}),q(),L(`${e} ${s?"ativado":"desativado"}`)},[E,C.textToSpeech]),z=i.useCallback(e=>{D(e),q(),L(`Preset ${e} aplicado`)},[D,C.textToSpeech]);return s.jsxDEV("div",{className:a,children:[s.jsxDEV("div",{className:t,children:[s.jsxDEV("button",{className:c,onClick:f,children:"← Voltar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:141,columnNumber:9},this),s.jsxDEV("h1",{className:o,children:"♿ Configurações de Acessibilidade"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:144,columnNumber:9},this),s.jsxDEV("p",{className:l,children:"Personalize sua experiência para ter o melhor acesso ao Portal Betina"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:145,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:140,columnNumber:7},this),s.jsxDEV("div",{className:n,children:s.jsxDEV("div",{className:r,children:s.jsxDEV("div",{className:m,children:[s.jsxDEV("p",{className:b,children:"Configure suas preferências de acessibilidade para uma melhor experiência no Portal Betina."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:153,columnNumber:13},this),s.jsxDEV("div",{className:p,children:[s.jsxDEV("h3",{className:u,children:"⚡ Configurações Rápidas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:159,columnNumber:15},this),s.jsxDEV("div",{className:N,children:[s.jsxDEV("button",{className:g,onClick:()=>z("default"),children:"Padrão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:161,columnNumber:17},this),s.jsxDEV("button",{className:g,onClick:()=>z("high-contrast"),children:"Alto Contraste"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:167,columnNumber:17},this),s.jsxDEV("button",{className:g,onClick:()=>z("autism-friendly"),children:"Autismo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:173,columnNumber:17},this),s.jsxDEV("button",{className:g,onClick:()=>z("dyslexia"),children:"Dislexia"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:179,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:160,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:158,columnNumber:13},this),s.jsxDEV("div",{className:p,children:[s.jsxDEV("h3",{className:u,children:"👁️ Visual"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:190,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Alto Contraste"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:194,columnNumber:19},this),s.jsxDEV("div",{className:h,children:[s.jsxDEV("input",{type:"checkbox",className:x,checked:C.highContrast,onChange:e=>T("highContrast",e.target.checked)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:196,columnNumber:21},this),s.jsxDEV("span",{className:v},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:202,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:195,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:193,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:192,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Tamanho da Fonte"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:209,columnNumber:19},this),s.jsxDEV("select",{className:y,value:C.fontSize,onChange:e=>T("fontSize",e.target.value),children:[s.jsxDEV("option",{value:"small",children:"Pequena"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:215,columnNumber:21},this),s.jsxDEV("option",{value:"medium",children:"Média"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:216,columnNumber:21},this),s.jsxDEV("option",{value:"large",children:"Grande"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:217,columnNumber:21},this),s.jsxDEV("option",{value:"extra-large",children:"Extra Grande"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:218,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:210,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:208,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:207,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Fonte para Dislexia"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:225,columnNumber:19},this),s.jsxDEV("div",{className:h,children:[s.jsxDEV("input",{type:"checkbox",className:x,checked:C.dyslexiaFriendly,onChange:e=>T("dyslexiaFriendly",e.target.checked)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:227,columnNumber:21},this),s.jsxDEV("span",{className:v},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:233,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:226,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:224,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:223,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:189,columnNumber:13},this),s.jsxDEV("div",{className:p,children:[s.jsxDEV("h3",{className:u,children:"🎭 Movimento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:241,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Reduzir Animações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:245,columnNumber:19},this),s.jsxDEV("div",{className:h,children:[s.jsxDEV("input",{type:"checkbox",className:x,checked:C.reducedMotion,onChange:e=>T("reducedMotion",e.target.checked)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:247,columnNumber:21},this),s.jsxDEV("span",{className:v},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:253,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:246,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:244,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:243,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:240,columnNumber:13},this),s.jsxDEV("div",{className:p,children:[s.jsxDEV("h3",{className:u,children:"🔊 Áudio"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:261,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Leitura de Texto"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:265,columnNumber:19},this),s.jsxDEV("div",{className:h,children:[s.jsxDEV("input",{type:"checkbox",className:x,checked:C.textToSpeech,onChange:e=>T("textToSpeech",e.target.checked)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:267,columnNumber:21},this),s.jsxDEV("span",{className:v},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:273,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:266,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:264,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:263,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Sons Ativados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:280,columnNumber:19},this),s.jsxDEV("div",{className:h,children:[s.jsxDEV("input",{type:"checkbox",className:x,checked:C.soundEnabled,onChange:e=>T("soundEnabled",e.target.checked)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:282,columnNumber:21},this),s.jsxDEV("span",{className:v},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:288,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:281,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:279,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:278,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Leitura Automática"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:295,columnNumber:19},this),s.jsxDEV("div",{className:h,children:[s.jsxDEV("input",{type:"checkbox",className:x,checked:C.autoRead,onChange:e=>T("autoRead",e.target.checked)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:297,columnNumber:21},this),s.jsxDEV("span",{className:v},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:303,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:296,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:294,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:293,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:260,columnNumber:13},this),s.jsxDEV("div",{className:p,children:[s.jsxDEV("h3",{className:u,children:"🎨 Tema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:311,columnNumber:15},this),s.jsxDEV("div",{className:d,children:s.jsxDEV("label",{className:P,children:[s.jsxDEV("span",{className:j,children:"Esquema de Cores"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:315,columnNumber:19},this),s.jsxDEV("select",{className:y,value:C.colorScheme,onChange:e=>T("colorScheme",e.target.value),children:[s.jsxDEV("option",{value:"default",children:"Padrão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:321,columnNumber:21},this),s.jsxDEV("option",{value:"dark",children:"Escuro"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:322,columnNumber:21},this),s.jsxDEV("option",{value:"soft",children:"Suave"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:323,columnNumber:21},this),s.jsxDEV("option",{value:"high-contrast",children:"Alto Contraste"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:324,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:316,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:314,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:313,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:310,columnNumber:13},this),V&&s.jsxDEV("div",{className:A,children:"✅ Configurações salvas com sucesso!"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:331,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:152,columnNumber:11},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:151,columnNumber:9},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:150,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AccessibilityPage/AccessibilityPage.jsx",lineNumber:139,columnNumber:5},this)}export{f as default};
