/**
 * 🎨 CREATIVE PAINTING V3 - JOGO DE PINTURA CRIATIVA COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { SystemContext } from '../../components/context/SystemContext.jsx'
import { useAccessibilityContext } from '../../components/context/AccessibilityContext'
import { v4 as uuidv4 } from 'uuid';

// Importa configurações e métricas específicas do jogo
import PAINTING_CONFIG from './CreativePaintingConfig.js'

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx'

// 🧠 Integração com sistema unificado de métricas
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic'

// 🎨 Importar coletores avançados de pintura criativa
import { CreativePaintingCollectorsHub } from './collectors/index.js'
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js'
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js'

// Importa estilos modulares baseados no preview
import styles from './CreativePainting.module.css'

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - CREATIVE PAINTING
const ACTIVITY_TYPES = {
  FREE_PAINTING: {
    id: 'free_painting',
    name: 'Pintura Livre',
    icon: '🎨',
    description: 'Pinte livremente usando sua criatividade',
    component: 'FreePaintingActivity'
  },
  COLORING_BOOK: {
    id: 'coloring_book',
    name: 'Livro de Colorir',
    icon: '📚',
    description: 'Colora desenhos pré-definidos',
    component: 'ColoringBookActivity'
  },
  PATTERN_PAINTING: {
    id: 'pattern_painting',
    name: 'Pintura de Padrões',
    icon: '🔄',
    description: 'Crie e reproduza padrões visuais',
    component: 'PatternPaintingActivity'
  },
  COLOR_MIXING: {
    id: 'color_mixing',
    name: 'Mistura de Cores',
    icon: '🌈',
    description: 'Aprenda a misturar cores primárias',
    component: 'ColorMixingActivity'
  },
  SHAPE_PAINTING: {
    id: 'shape_painting',
    name: 'Pintura de Formas',
    icon: '⭐',
    description: 'Pinte usando formas geométricas',
    component: 'ShapePaintingActivity'
  },
  COLLABORATIVE_ART: {
    id: 'collaborative_art',
    name: 'Arte Colaborativa',
    icon: '👥',
    description: 'Crie arte em colaboração',
    component: 'CollaborativeArtActivity'
  }
};

function CreativePaintingGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.FREE_PAINTING.id,
    activityCycle: [
      ACTIVITY_TYPES.FREE_PAINTING.id,
      ACTIVITY_TYPES.COLORING_BOOK.id,
      ACTIVITY_TYPES.PATTERN_PAINTING.id,
      ACTIVITY_TYPES.COLOR_MIXING.id,
      ACTIVITY_TYPES.SHAPE_PAINTING.id,
      ACTIVITY_TYPES.COLLABORATIVE_ART.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      freePainting: {
        canvas: null,
        brushSize: 5,
        currentColor: '#000000',
        savedPaintings: []
      },
      coloringBook: {
        currentTemplate: null,
        completedAreas: [],
        colorPalette: []
      },
      patternPainting: {
        currentPattern: null,
        userPattern: [],
        patternLibrary: []
      },
      colorMixing: {
        primaryColors: ['#FF0000', '#00FF00', '#0000FF'],
        mixedColors: [],
        targetColor: null
      },
      shapePainting: {
        availableShapes: [],
        paintedShapes: [],
        currentShape: null
      },
      collaborativeArt: {
        sharedCanvas: null,
        contributors: [],
        isSharing: false
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // 🧠 Integração com sistema unificado de métricas
  const { 
    collectMetrics, 
    processGameSession, 
    initializeSession,
    processAdvancedMetrics // Para análise de criatividade e expressão
  } = useUnifiedGameLogic('CreativePainting')

  // 🎨 Inicializar coletores avançados de pintura criativa
  const [collectorsHub] = useState(() => new CreativePaintingCollectorsHub())

  // 🔄 Hook multissensorial integrado
  const multisensoryIntegration = useMultisensoryIntegration('creative-painting', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info'
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    gameType: 'creative-painting',
    collectorsHub,
    multisensoryIntegration,
    autoUpdate: true,
    logLevel: 'info'
  });

  // Estados para métricas avançadas de criatividade
  const [sessionStartTime, setSessionStartTime] = useState(null)
  const [brushStrokes, setBrushStrokes] = useState([])
  const [colorTransitions, setColorTransitions] = useState([])
  const [creativityMetrics, setCreativityMetrics] = useState({
    originalityScore: 0,
    complexityScore: 0,
    expressiveRange: 0,
    spatialUtilization: 0
  })

  // Referências
  const canvasRef = useRef(null)
  const strokesContainerRef = useRef(null)
  const drawingRef = useRef({ isDrawing: false, lastPoint: null })

  // Inicializar sessão ao começar o jogo
  useEffect(() => {
    if (!showStartScreen && !sessionStartTime) {
      setSessionStartTime(Date.now())
      initializeSession({
        gameType: 'CreativePainting',
        difficulty: currentDifficulty,
        timestamp: new Date().toISOString()
      })
    }
  }, [showStartScreen, sessionStartTime, currentDifficulty, initializeSession])

  // Timer para atualizar estatísticas
  useEffect(() => {
    const timer = setInterval(() => {
      setGameState(prev => ({ ...prev })) // Force re-render para atualizar tempo
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  // Cleanup do TTS quando componente é desmontado
  useEffect(() => {
    return () => {
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // Calcular tempo decorrido
  const getElapsedTime = useCallback(() => {
    const elapsed = Math.floor((Date.now() - gameState.startTime) / 1000)
    const minutes = Math.floor(elapsed / 60)
    const seconds = elapsed % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [gameState.startTime])

  // Função para inicializar o jogo baseado na dificuldade
  const initializeGame = useCallback((difficulty) => {
    setCurrentDifficulty(difficulty)
    
    // Configurar brushes baseado na dificuldade
    const difficultyConfig = PAINTING_CONFIG.difficulties[difficulty]
    const defaultBrushSize = difficultyConfig?.defaultBrush || 10
    
    setGameState(prev => ({
      ...prev,
      brushSize: defaultBrushSize,
      startTime: Date.now(),
      strokes: [],
      undoStack: [],
      redoStack: [],
      colorsUsed: new Set(['#ff6b6b']),
      savedCount: 0,
      showPlaceholder: true
    }))
    
    setShowStartScreen(false)
  }, [])

  // Função para voltar à tela inicial
  const backToStart = useCallback(() => {
    setShowStartScreen(true)
  }, [])

  // Funções de seleção
  const selectColor = useCallback((color) => {
    setGameState(prev => ({
      ...prev,
      currentColor: color,
      colorsUsed: new Set([...prev.colorsUsed, color])
    }))
  }, [])

  const selectBrush = useCallback((brush) => {
    setGameState(prev => ({ ...prev, currentBrush: brush }))
  }, [])

  const updateBrushSize = useCallback((size) => {
    setGameState(prev => ({ ...prev, brushSize: parseInt(size) }))
  }, [])

  const selectTemplate = useCallback((template) => {
    setGameState(prev => ({ ...prev, selectedTemplate: template }))
    if (template === 'blank') {
      clearCanvas()
    } else {
      // Aqui seria implementada a lógica de carregar templates pré-definidos
      console.log(`Template selecionado: ${template}`)
    }
  }, [])

  // Funções de desenho
  const startDrawing = useCallback((event) => {
    const rect = canvasRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    drawingRef.current.isDrawing = true
    drawingRef.current.lastPoint = { x, y }
    
    // Salvar estado para undo
    setGameState(prev => ({
      ...prev,
      undoStack: [...prev.undoStack, [...prev.strokes]],
      redoStack: [], // Limpar redo stack
      showPlaceholder: false
    }))
    
    // 🎨 Iniciar nova pincelada para métricas
    const strokeStart = {
      startPoint: { x, y },
      startTime: Date.now(),
      path: [{ x, y }]
    }
    
    // Criar primeira stroke
    createStroke(x, y, strokeStart)
  }, [])

  const draw = useCallback((event) => {
    if (!drawingRef.current.isDrawing) return
    
    const rect = canvasRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top
    
    // 🎨 Adicionar ponto à pincelada atual para análise
    const currentStroke = {
      path: [{ x, y }],
      velocity: calculateVelocity(drawingRef.current.lastPoint, { x, y }),
      direction: calculateDirection(drawingRef.current.lastPoint, { x, y })
    }
    
    createStroke(x, y, currentStroke)
    drawingRef.current.lastPoint = { x, y }
  }, [])

  const stopDrawing = useCallback(() => {
    if (drawingRef.current.isDrawing) {
      // 🎨 Finalizar pincelada e registrar métricas
      const strokeData = {
        endTime: Date.now(),
        path: [], // Path seria coletado durante o desenho
        pressure: 1, // Simplificado para mouse
        velocity: 0,
        direction: 0
      }
      
      recordBrushStroke(strokeData)
    }
    
    drawingRef.current.isDrawing = false
    drawingRef.current.lastPoint = null
  }, [recordBrushStroke])

  const createStroke = useCallback((x, y, metricsData = {}) => {
    const strokeData = {
      x,
      y,
      color: gameState.currentColor,
      size: gameState.brushSize,
      brush: gameState.currentBrush,
      id: Date.now() + Math.random()
    }
    
    setGameState(prev => ({
      ...prev,
      strokes: [...prev.strokes, strokeData]
    }))
  }, [gameState.currentColor, gameState.brushSize, gameState.currentBrush])

  // Funções de undo/redo
  const undoStroke = useCallback(() => {
    setGameState(prev => {
      if (prev.undoStack.length === 0) return prev
      
      const previousState = prev.undoStack[prev.undoStack.length - 1]
      return {
        ...prev,
        strokes: previousState,
        undoStack: prev.undoStack.slice(0, -1),
        redoStack: [...prev.redoStack, prev.strokes],
        showPlaceholder: previousState.length === 0
      }
    })
  }, [])

  const redoStroke = useCallback(() => {
    setGameState(prev => {
      if (prev.redoStack.length === 0) return prev
      
      const nextState = prev.redoStack[prev.redoStack.length - 1]
      return {
        ...prev,
        strokes: nextState,
        redoStack: prev.redoStack.slice(0, -1),
        undoStack: [...prev.undoStack, prev.strokes],
        showPlaceholder: nextState.length === 0
      }
    })
  }, [])

  const clearCanvas = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      strokes: [],
      undoStack: [...prev.undoStack, prev.strokes],
      redoStack: [],
      showPlaceholder: true
    }))
  }, [])

  // Funções do footer
  const saveDrawing = useCallback(async () => {
    setGameState(prev => ({ ...prev, savedCount: prev.savedCount + 1 }))
    console.log('🎨 Desenho salvo!')
    
    // 🎨 Coletar métricas ao salvar obra
    try {
      await collectCreativeMetrics()
      console.log('📊 Métricas criativas coletadas ao salvar!')
    } catch (error) {
      console.error('❌ Erro ao coletar métricas:', error)
    }
    
    // Mostrar notificação
    alert('💾 Obra salva com sucesso!')
  }, [collectCreativeMetrics])

  const shareDrawing = useCallback(() => {
    console.log('Compartilhando desenho...')
    alert('📤 Funcionalidade de compartilhamento será implementada na versão final!')
  }, [])

  const printDrawing = useCallback(() => {
    console.log('Imprimindo desenho...')
    alert('🖨️ Funcionalidade de impressão será implementada na versão final!')
  }, [])

  const newDrawing = useCallback(() => {
    if (gameState.strokes.length > 0) {
      if (confirm('🎨 Tem certeza que deseja criar uma nova obra?\n\nO desenho atual será perdido se não foi salvo.')) {
        clearCanvas()
        selectTemplate('blank')
      }
    }
  }, [gameState.strokes.length, clearCanvas, selectTemplate])

  // Suporte touch
  const handleTouchStart = useCallback((event) => {
    event.preventDefault()
    const touch = event.touches[0]
    const mouseEvent = new MouseEvent('mousedown', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    startDrawing(mouseEvent)
  }, [startDrawing])

  const handleTouchMove = useCallback((event) => {
    event.preventDefault()
    const touch = event.touches[0]
    const mouseEvent = new MouseEvent('mousemove', {
      clientX: touch.clientX,
      clientY: touch.clientY
    })
    draw(mouseEvent)
  }, [draw])

  const handleTouchEnd = useCallback((event) => {
    event.preventDefault()
    stopDrawing()
  }, [stopDrawing])

  // Tela inicial profissional com seleção de dificuldade
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Pintura Criativa"
        gameSubtitle="Expresse sua criatividade com ferramentas de pintura digital"
        gameInstruction="Escolha cores, pincéis e modelos para criar suas obras de arte! Desenvolva coordenação motora e criatividade."
        gameIcon="🎨"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: 'Pincéis grandes - Ideal para iniciantes',
            icon: '😊',
            preview: '🖌️ Pincel grande'
          },
          {
            id: 'medium',
            name: 'Médio',
            description: 'Pincéis variados - Desafio equilibrado',
            icon: '🎯',
            preview: '🖌️ Pincéis médios'
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: 'Pincéis precisos - Para especialistas',
            icon: '🚀',
            preview: '✏️ Pincéis pequenos'
          }
        ]}
        onStart={(difficulty) => initializeGame(difficulty)}
        onBack={onBack}
        customContent={
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '1.5rem',
            marginBottom: '1rem'
          }}>
            <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
              🎨 Este jogo desenvolve:
            </h3>
            <div style={{
              display: 'flex',
              justifyContent: 'space-around',
              flexWrap: 'wrap',
              gap: '1rem'
            }}>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🖌️</div>
                <div style={{ fontSize: '0.9rem', fontWeight: '600' }}>Coordenação</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎭</div>
                <div style={{ fontSize: '0.9rem', fontWeight: '600' }}>Criatividade</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🌈</div>
                <div style={{ fontSize: '0.9rem', fontWeight: '600' }}>Cores</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧠</div>
                <div style={{ fontSize: '0.9rem', fontWeight: '600' }}>Concentração</div>
              </div>
            </div>
          </div>
        }
      />
    )
  }

  // INTERFACE PRINCIPAL DO JOGO - EXATAMENTE COMO O PREVIEW
  return (
    <div className={styles.gameContainer}>
      {/* HEADER PADRÃO */}
      <div className={styles.gameHeader}>
        <button className={styles.backButton} onClick={backToStart} aria-label="Voltar à seleção de dificuldade">
          ← Voltar
        </button>
        <div className={styles.gameTitle}>
          <span className={styles.gameIcon}>🎨</span>
          <span>Pintura Criativa</span>
          <span className={styles.difficultyBadge}>
            {PAINTING_CONFIG.difficulties[currentDifficulty]?.name || 'Médio'}
          </span>
        </div>
        <div className={styles.gameSubtitle}>
          Expresse sua criatividade com ferramentas de pintura digital
        </div>
        <div className={styles.gameStats}>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{gameState.strokes.length}</span>
            <span className={styles.statLabel}>Pinceladas</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{gameState.colorsUsed.size}</span>
            <span className={styles.statLabel}>Cores Usadas</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{getElapsedTime()}</span>
            <span className={styles.statLabel}>Tempo</span>
          </div>
          <div className={styles.statItem}>
            <span className={styles.statValue}>{gameState.savedCount}</span>
            <span className={styles.statLabel}>Salvos</span>
          </div>
        </div>
      </div>

      {/* LAYOUT PRINCIPAL - EXATAMENTE COMO O PREVIEW */}
      <div className={styles.gameMain}>
        {/* SIDEBAR DE FERRAMENTAS - ESQUERDA */}
        <div className={styles.toolsSidebar}>
          {/* PALETA DE CORES */}
          <div className={styles.toolSection}>
            <div className={styles.toolTitle}>
              <span>🎨</span>
              <span>Paleta de Cores</span>
            </div>
            <div className={styles.colorPalette}>
              {[
                '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4',
                '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd',
                '#2c2c54', '#ffffff', '#6c757d', '#000000'
              ].map((color) => (
                <motion.div
                  key={color}
                  className={`${styles.colorBtn} ${gameState.currentColor === color ? styles.active : ''}`}
                  style={{ backgroundColor: color, border: color === '#ffffff' ? '2px solid #dee2e6' : 'none' }}
                  onClick={() => selectColor(color)}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                />
              ))}
            </div>
          </div>

          {/* FERRAMENTAS DE PINCEL */}
          <div className={styles.toolSection}>
            <div className={styles.toolTitle}>
              <span>🖌️</span>
              <span>Ferramentas</span>
            </div>
            <div className={styles.brushTools}>
              {[
                { id: 'brush', icon: '🖌️', name: 'Pincel' },
                { id: 'pencil', icon: '✏️', name: 'Lápis' },
                { id: 'marker', icon: '🖍️', name: 'Marcador' },
                { id: 'spray', icon: '💨', name: 'Spray' }
              ].map((brush) => (
                <motion.div
                  key={brush.id}
                  className={`${styles.brushBtn} ${gameState.currentBrush === brush.id ? styles.active : ''}`}
                  onClick={() => selectBrush(brush.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  title={brush.name}
                >
                  {brush.icon}
                </motion.div>
              ))}
            </div>
          </div>

          {/* CONTROLE DE TAMANHO */}
          <div className={styles.toolSection}>
            <div className={styles.toolTitle}>
              <span>📏</span>
              <span>Tamanho do Pincel</span>
            </div>
            <div className={styles.sizeControl}>
              <input
                type="range"
                min="2"
                max="50"
                value={gameState.brushSize}
                onChange={(e) => updateBrushSize(e.target.value)}
                className={styles.sizeSlider}
              />
              <div className={styles.sizeDisplay}>{gameState.brushSize}px</div>
            </div>
          </div>

          {/* AÇÕES RÁPIDAS */}
          <div className={styles.toolSection}>
            <div className={styles.toolTitle}>
              <span>🔧</span>
              <span>Ações</span>
            </div>
            <div className={styles.actionTools}>
              <motion.button
                className={styles.actionBtn}
                onClick={undoStroke}
                disabled={gameState.undoStack.length === 0}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                title="Desfazer"
              >
                <span>↶</span>
                <span>Desfazer</span>
              </motion.button>
              <motion.button
                className={styles.actionBtn}
                onClick={redoStroke}
                disabled={gameState.redoStack.length === 0}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                title="Refazer"
              >
                <span>↷</span>
                <span>Refazer</span>
              </motion.button>
              <motion.button
                className={`${styles.actionBtn} ${styles.clear}`}
                onClick={clearCanvas}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                title="Limpar Tudo"
              >
                <span>🗑️</span>
                <span>Limpar</span>
              </motion.button>
            </div>
          </div>
        </div>

        {/* ÁREA PRINCIPAL - DIREITA */}
        <div className={styles.canvasArea}>
          {/* SEÇÃO DE TEMPLATES */}
          <div className={styles.templatesSection}>
            <div className={styles.templatesTitle}>
              <span>📋</span>
              <span>Modelos e Desenhos Guiados</span>
            </div>
            <div className={styles.templatesGrid}>
              {[
                { id: 'blank', icon: '⭐', name: 'Canvas em Branco' },
                { id: 'house', icon: '🏠', name: 'Casa' },
                { id: 'tree', icon: '🌳', name: 'Árvore' },
                { id: 'sun', icon: '☀️', name: 'Sol' },
                { id: 'flower', icon: '🌸', name: 'Flor' },
                { id: 'car', icon: '🚗', name: 'Carro' },
                { id: 'heart', icon: '❤️', name: 'Coração' },
                { id: 'star', icon: '⭐', name: 'Estrela' }
              ].map((template) => (
                <motion.div
                  key={template.id}
                  className={`${styles.templateBtn} ${gameState.selectedTemplate === template.id ? styles.active : ''}`}
                  onClick={() => selectTemplate(template.id)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  title={template.name}
                >
                  {template.icon}
                </motion.div>
              ))}
            </div>
          </div>

          {/* CANVAS PRINCIPAL */}
          <div className={styles.canvasContainer}>
            <div 
              className={styles.drawingCanvas}
              ref={canvasRef}
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              {gameState.showPlaceholder && (
                <div className={styles.canvasPlaceholder}>
                  <div className={styles.canvasPlaceholderIcon}>🎨</div>                  <div className={styles.canvasPlaceholderText}>
                    <strong>Clique e arraste para começar a pintar!</strong><br />
                    Escolha suas cores e ferramentas à esquerda
                  </div>
                </div>
              )}
              <div ref={strokesContainerRef} className={styles.strokesContainer}>
                {gameState.strokes.map((stroke) => (
                  <div
                    key={stroke.id}
                    className={styles.stroke}
                    style={{
                      left: stroke.x - stroke.size / 2,
                      top: stroke.y - stroke.size / 2,
                      width: stroke.size,
                      height: stroke.size,
                      backgroundColor: stroke.color,
                      borderRadius: '50%',
                      position: 'absolute',
                      pointerEvents: 'none',
                      filter: stroke.brush === 'spray' ? 'blur(2px)' : 'none',
                      opacity: stroke.brush === 'pencil' ? 0.8 : stroke.brush === 'spray' ? 0.7 : 1
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* FOOTER DE AÇÕES */}
      <div className={styles.gameFooter}>
        <div className={styles.paintingInfo}>
          <strong>Dica:</strong> Use diferentes pincéis para criar texturas únicas!
        </div>
        <div className={styles.footerActions}>
          <motion.button
            className={styles.footerBtn}
            onClick={saveDrawing}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>💾</span>
            <span>Salvar Obra</span>
          </motion.button>
          <motion.button
            className={`${styles.footerBtn} ${styles.secondary}`}
            onClick={shareDrawing}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>📤</span>
            <span>Compartilhar</span>
          </motion.button>
          <motion.button
            className={`${styles.footerBtn} ${styles.secondary}`}
            onClick={printDrawing}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>🖨️</span>
            <span>Imprimir</span>
          </motion.button>
          <motion.button
            className={`${styles.footerBtn} ${styles.secondary}`}
            onClick={newDrawing}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>📄</span>
            <span>Nova Obra</span>
          </motion.button>
          <motion.button
            className={`${styles.footerBtn} ${styles.secondary}`}
            onClick={handleCreativeSessionEnd}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span>🏠</span>
            <span>Sair</span>
          </motion.button>
        </div>
      </div>
    </div>
  )
}

export default CreativePaintingGame

// Funções auxiliares para cálculos de métricas durante o desenho
const calculateVelocity = (point1, point2) => {
  if (!point1 || !point2) return 0
  
  const distance = Math.sqrt(
    Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2)
  )
  
  // Assumir tempo fixo entre pontos para simplificar
  return distance / 16 // Aproximadamente 16ms entre eventos
}

const calculateDirection = (point1, point2) => {
  if (!point1 || !point2) return 0
  
  return Math.atan2(point2.y - point1.y, point2.x - point1.x) * (180 / Math.PI)
}
