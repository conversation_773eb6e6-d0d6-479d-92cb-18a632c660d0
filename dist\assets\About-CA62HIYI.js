import{j as e}from"./index-BkN74ywZ.js";import"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const o="_container_z1ypv_15",s="_heroBanner_z1ypv_81",a="_heroContent_z1ypv_99",t="_heroTitle_z1ypv_109",i="_heroSubtitle_z1ypv_125",n="_badgeContainer_z1ypv_139",r="_techBadge_z1ypv_153",l="_badgePrimary_z1ypv_177",m="_badgeGreen_z1ypv_185",c="_badgePurple_z1ypv_193",u="_section_z1ypv_203",b="_sectionTitle_z1ypv_221",p="_sectionContent_z1ypv_249",N="_benefitsList_z1ypv_271",d="_benefitItem_z1ypv_283",j="_benefitEmoji_z1ypv_305",v="_benefitText_z1ypv_315",h="_highlightBox_z1ypv_327",A="_highlightTitle_z1ypv_345",x="_highlightText_z1ypv_357",g="_aiFeatureGrid_z1ypv_371",f="_aiFeatureCard_z1ypv_385",P="_aiFeatureIcon_z1ypv_415",C="_aiFeatureTitle_z1ypv_425",D="_aiFeatureDescription_z1ypv_439",E="_techCategory_z1ypv_453",V="_techCategoryTitle_z1ypv_461",_="_techBadges_z1ypv_481";function z({onBackToHome:z}){return e.jsxDEV("div",{className:o,children:[e.jsxDEV("div",{className:s,children:e.jsxDEV("div",{className:a,children:[e.jsxDEV("h1",{className:t,children:"Portal Betina"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:16,columnNumber:11},this),e.jsxDEV("p",{className:i,children:"Transformando vidas através de atividades neuropedagógicas potencializadas por inteligência artificial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:17,columnNumber:11},this),e.jsxDEV("div",{className:n,children:[e.jsxDEV("span",{className:`${r} ${l}`,children:"🧠 Desenvolvimento Cognitivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:21,columnNumber:13},this),e.jsxDEV("span",{className:`${r} ${m}`,children:"🤖 Potencializado por IA"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:24,columnNumber:13},this),e.jsxDEV("span",{className:`${r} ${c}`,children:"♿ 100% Acessível"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:27,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:20,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:15,columnNumber:9},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:14,columnNumber:7},this),e.jsxDEV("section",{className:u,children:[e.jsxDEV("h2",{className:b,children:[e.jsxDEV("span",{children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:37,columnNumber:11},this),"O que são Atividades Neuropedagógicas?"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:36,columnNumber:9},this),e.jsxDEV("div",{className:p,children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Atividades neuropedagógicas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:42,columnNumber:13},this)," são intervenções estruturadas que estimulam o desenvolvimento cognitivo, emocional e social, especialmente para crianças com autismo, TDAH ou outras necessidades específicas."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:41,columnNumber:11},this),e.jsxDEV("p",{children:["Elas combinam princípios da ",e.jsxDEV("strong",{children:"neurociência"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:47,columnNumber:41},this),", ",e.jsxDEV("strong",{children:"psicologia"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:47,columnNumber:72},this)," e",e.jsxDEV("strong",{children:"pedagogia"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:48,columnNumber:13},this)," para promover habilidades essenciais como:"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:46,columnNumber:11},this),e.jsxDEV("ul",{className:N,children:[e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:53,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Atenção e Concentração:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:55,columnNumber:17},this)," Melhorar o foco e a capacidade de manter a atenção"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:54,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:52,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:59,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Memória:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:61,columnNumber:17},this)," Fortalecer a memória de trabalho e de longo prazo"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:60,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:58,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"🤔"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:65,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Raciocínio Lógico:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:67,columnNumber:17},this)," Desenvolver habilidades de resolução de problemas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:66,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:64,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"✋"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:71,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Coordenação Motora:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:73,columnNumber:17},this)," Aprimorar habilidades motoras finas e grossas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:72,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:70,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"😊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:77,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Regulação Emocional:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:79,columnNumber:17},this)," Aprender a identificar e gerenciar emoções"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:78,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:76,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:51,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:40,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:35,columnNumber:7},this),e.jsxDEV("section",{className:u,children:[e.jsxDEV("h2",{className:b,children:[e.jsxDEV("span",{children:"🤖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:89,columnNumber:11},this),"Como a Inteligência Artificial Potencializa o Aprendizado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:88,columnNumber:9},this),e.jsxDEV("div",{className:p,children:[e.jsxDEV("div",{className:h,children:["          ",e.jsxDEV("h3",{className:A,children:"🚀 Portal Betina V3 - Tecnologia Avançada para Desenvolvimento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:93,columnNumber:58},this),e.jsxDEV("p",{className:x,children:"Nossa plataforma integra inteligência artificial com metodologias terapêuticas comprovadas para oferecer uma experiência personalizada e eficaz para cada criança."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:96,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:93,columnNumber:11},this),e.jsxDEV("div",{className:g,children:[e.jsxDEV("div",{className:f,children:[e.jsxDEV("div",{className:P,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:104,columnNumber:15},this),e.jsxDEV("h4",{className:C,children:"Personalização Inteligente"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:105,columnNumber:15},this),e.jsxDEV("p",{className:D,children:"A IA adapta a dificuldade e o ritmo das atividades baseado no desempenho individual da criança"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:106,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:103,columnNumber:13},this),e.jsxDEV("div",{className:f,children:[e.jsxDEV("div",{className:P,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:112,columnNumber:15},this),e.jsxDEV("h4",{className:C,children:"Análise de Progresso"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:113,columnNumber:15},this),e.jsxDEV("p",{className:D,children:"Algoritmos analisam padrões de aprendizado e fornecem insights sobre o desenvolvimento cognitivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:114,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:111,columnNumber:13},this),e.jsxDEV("div",{className:f,children:[e.jsxDEV("div",{className:P,children:"🎮"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:120,columnNumber:15},this),e.jsxDEV("h4",{className:C,children:"Engajamento Otimizado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:121,columnNumber:15},this),e.jsxDEV("p",{className:D,children:"IA determina os melhores momentos e tipos de feedback para manter a motivação e interesse"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:122,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:119,columnNumber:13},this),e.jsxDEV("div",{className:f,children:[e.jsxDEV("div",{className:P,children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:128,columnNumber:15},this),e.jsxDEV("h4",{className:C,children:"Adaptação Contínua"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:129,columnNumber:15},this),e.jsxDEV("p",{className:D,children:"O sistema aprende continuamente com as interações, melhorando constantemente a experiência"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:130,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:127,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:102,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:92,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:87,columnNumber:7},this),e.jsxDEV("section",{className:u,children:[e.jsxDEV("h2",{className:b,children:[e.jsxDEV("span",{children:"⚙️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:141,columnNumber:11},this),"Tecnologias e Metodologias Aplicadas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:140,columnNumber:9},this),e.jsxDEV("div",{className:p,children:[e.jsxDEV("p",{children:"O Portal Betina utiliza tecnologias modernas e metodologias baseadas em evidências científicas:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:145,columnNumber:11},this),e.jsxDEV("div",{className:E,children:[e.jsxDEV("h4",{className:V,children:"🧬 Base Científica"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:150,columnNumber:13},this),e.jsxDEV("div",{className:_,children:[e.jsxDEV("span",{className:r,children:"Neurociência Cognitiva"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:152,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Psicologia do Desenvolvimento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:153,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Pedagogia Inclusiva"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:154,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Terapia ABA"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:155,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Neuroplasticidade"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:156,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:151,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:149,columnNumber:11},this),e.jsxDEV("div",{className:E,children:[e.jsxDEV("h4",{className:V,children:"💻 Tecnologia"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:161,columnNumber:13},this),e.jsxDEV("div",{className:_,children:[e.jsxDEV("span",{className:r,children:"React + IA"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:163,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Machine Learning"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:164,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Design Responsivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:165,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Acessibilidade Web"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:166,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Progressive Web App"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:167,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:162,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:160,columnNumber:11},this),e.jsxDEV("div",{className:E,children:[e.jsxDEV("h4",{className:V,children:"🌈 Acessibilidade"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:172,columnNumber:13},this),e.jsxDEV("div",{className:_,children:[e.jsxDEV("span",{className:r,children:"Screen Reader"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:174,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Alto Contraste"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:175,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Navegação por Teclado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:176,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"Feedback Háptico"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:177,columnNumber:15},this),e.jsxDEV("span",{className:r,children:"WCAG 2.1 AA"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:178,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:173,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:171,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:144,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:139,columnNumber:7},this),e.jsxDEV("section",{className:u,children:[e.jsxDEV("h2",{className:b,children:[e.jsxDEV("span",{children:"👨‍👩‍👧‍👦"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:187,columnNumber:11},this),"Para Pais, Terapeutas e Educadores"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:186,columnNumber:9},this),e.jsxDEV("div",{className:p,children:[e.jsxDEV("p",{children:"O Portal Betina foi desenvolvido para ser uma ferramenta colaborativa entre famílias e profissionais:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:191,columnNumber:11},this),e.jsxDEV("ul",{className:N,children:[e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"👩‍⚕️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:197,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Para Terapeutas:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:199,columnNumber:17},this)," Ferramentas complementares para sessões presenciais e atividades para casa"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:198,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:196,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"👨‍🏫"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:203,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Para Educadores:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:205,columnNumber:17},this)," Recursos para inclusão escolar e desenvolvimento de habilidades específicas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:204,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:202,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"👨‍👩‍👧"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:209,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Para Famílias:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:211,columnNumber:17},this)," Atividades estruturadas para momentos de qualidade e desenvolvimento em casa"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:210,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:208,columnNumber:13},this),e.jsxDEV("li",{className:d,children:[e.jsxDEV("span",{className:j,children:"🤝"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:215,columnNumber:15},this),e.jsxDEV("span",{className:v,children:[e.jsxDEV("strong",{children:"Colaboração:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:217,columnNumber:17},this)," Dados e progresso compartilhados entre todos os envolvidos no cuidado da criança"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:216,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:214,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:195,columnNumber:11},this),e.jsxDEV("div",{className:h,children:e.jsxDEV("p",{className:x,children:["💝 ",e.jsxDEV("strong",{children:"100% Gratuito e Sempre Será"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:224,columnNumber:18},this),e.jsxDEV("br",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:224,columnNumber:62},this),"Acreditamos que toda criança merece acesso a ferramentas de qualidade para seu desenvolvimento, independentemente da condição socioeconômica da família."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:223,columnNumber:13},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:222,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:190,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:185,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/About/About.jsx",lineNumber:12,columnNumber:5},this)}export{z as default};
