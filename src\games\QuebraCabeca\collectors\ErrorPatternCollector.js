/**
 * 🧩 ERROR PATTERN COLLECTOR - QUEBRA-CABEÇA
 * Coleta padrões de erros específicos do jogo Quebra-Cabeça
 * Portal Betina V3
 */

export class ErrorPatternCollector {
  constructor() {
    this.name = 'QuebraCabecaErrorPatternCollector';
    this.description = 'Coleta padrões de erros no Quebra-Cabeça';
    this.version = '1.0.0';
    this.isActive = true;
    
    // Padrões de erro específicos do Quebra-Cabeça
    this.errorPatterns = {
      spatialOrientation: [], // Erros de orientação espacial
      shapeRecognition: [],   // Erros de reconhecimento de forma
      edgeMatching: [],       // Erros no encaixe de bordas
      colorMatching: [],      // Erros na combinação de cores
      persistence: [],        // Abandono ou desistência
      sequenceErrors: []      // Erros na sequência de montagem
    };
    
    this.collectedData = [];
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🧩 ${this.name} v${this.version} inicializado`);
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("QuebraCabecaErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }

    console.log(`📊 QuebraCabecaErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || 'sem ID'}`);
    
    try {
      // Extrair e categorizar erros dos dados do jogo
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      
      // Analisar erros de posicionamento de peças
      if (gameData.piecePlacements && Array.isArray(gameData.piecePlacements)) {
        gameData.piecePlacements.forEach((placement, index) => {
          if (!placement.isCorrect) {
            const placementError = this.collectPlacementError(
              placement.pieceId,
              placement.correctPosition,
              placement.actualPosition,
              { 
                difficulty: gameData.difficulty || 'medium',
                responseTime: placement.responseTime || 0,
                attemptNumber: index
              }
            );
            if (placementError) errors.push(placementError);
          }
        });
      }
      
      // Analisar erros de encaixe
      if (gameData.connectionAttempts && Array.isArray(gameData.connectionAttempts)) {
        gameData.connectionAttempts.forEach((attempt, index) => {
          if (!attempt.isCorrect) {
            const connectionError = this.collectConnectionError(
              attempt.piece1Id,
              attempt.piece2Id,
              { 
                difficulty: gameData.difficulty || 'medium',
                responseTime: attempt.responseTime || 0,
                attemptNumber: index
              }
            );
            if (connectionError) errors.push(connectionError);
          }
        });
      }
      
      // Salvar dados coletados para análise futura
      const collectedMetric = {
        timestamp: Date.now(),
        type: 'error_pattern',
        gameType: 'QuebraCabeca',
        data: errorMetrics,
        errors: errors,
        sessionData: {
          sessionId: gameData.sessionId,
          level: gameData.level || 1,
          attempt: gameData.attempt || 1
        }
      };

      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error) {
      console.error('❌ Erro no ErrorPatternCollector (QuebraCabeca):', error);
      return { errors: [], patterns: [], metrics: {}, error: error.message };
    }
  }

  /**
   * Coleta erros de posicionamento de peças
   */
  collectPlacementError(pieceId, correctPosition, actualPosition, context) {
    const errorKey = `piece_${pieceId}`;
    
    const placementError = {
      timestamp: new Date().toISOString(),
      pieceId: pieceId,
      correctPosition: correctPosition,
      actualPosition: actualPosition,
      errorType: this.identifyPlacementErrorType(correctPosition, actualPosition),
      context: {
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculatePlacementErrorSeverity(correctPosition, actualPosition, context),
      spatialDistance: this.calculateSpatialDistance(correctPosition, actualPosition)
    };

    if (!this.errorPatterns.spatialOrientation[errorKey]) {
      this.errorPatterns.spatialOrientation[errorKey] = [];
    }
    this.errorPatterns.spatialOrientation[errorKey].push(placementError);

    return placementError;
  }

  /**
   * Coleta erros de conexão entre peças
   */
  collectConnectionError(piece1Id, piece2Id, context) {
    const errorKey = `connection_${piece1Id}_${piece2Id}`;
    
    const connectionError = {
      timestamp: new Date().toISOString(),
      piece1Id: piece1Id,
      piece2Id: piece2Id,
      errorType: 'incorrect_connection',
      context: {
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: 0.6 // Valor padrão médio
    };

    if (!this.errorPatterns.edgeMatching[errorKey]) {
      this.errorPatterns.edgeMatching[errorKey] = [];
    }
    this.errorPatterns.edgeMatching[errorKey].push(connectionError);

    return connectionError;
  }

  /**
   * Identifica o tipo de erro de posicionamento
   */
  identifyPlacementErrorType(correct, actual) {
    if (!actual) return 'no_placement';
    
    // Erro de rotação
    if (actual.x === correct.x && actual.y === correct.y && 
        actual.rotation !== correct.rotation) {
      return 'rotation_error';
    }
    
    // Erro horizontal (mesma linha, coluna errada)
    if (actual.y === correct.y && actual.x !== correct.x) {
      return 'horizontal_error';
    }
    
    // Erro vertical (mesma coluna, linha errada)
    if (actual.x === correct.x && actual.y !== correct.y) {
      return 'vertical_error';
    }
    
    // Erro completo (linha e coluna erradas)
    return 'position_error';
  }

  /**
   * Calcula a distância espacial entre duas posições
   */
  calculateSpatialDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 1.0; // Máxima distância se não há uma das posições
    
    // Distância euclidiana normalizada
    const xDiff = pos1.x - pos2.x;
    const yDiff = pos1.y - pos2.y;
    const distance = Math.sqrt(xDiff * xDiff + yDiff * yDiff);
    
    // Normalizar baseado no tamanho do tabuleiro (assumindo 10x10 como máximo)
    return Math.min(distance / 14.14, 1.0); // 14.14 é aproximadamente a diagonal de um tabuleiro 10x10
  }

  /**
   * Calcula a severidade do erro de posicionamento
   */
  calculatePlacementErrorSeverity(correctPos, actualPos, context) {
    let severity = 0.5; // Base
    
    // Ajuste por distância espacial
    const distance = this.calculateSpatialDistance(correctPos, actualPos);
    severity += distance * 0.3;
    
    // Ajuste por tempo de resposta
    if (context.responseTime > 5000) severity += 0.1; // Muito lento
    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade
    
    // Ajuste por dificuldade
    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis
    
    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1
  }

  /**
   * Analisa padrões de erro com base nos dados do jogo
   */
  analyzeErrorPatterns(gameData) {
    const metrics = {
      spatialErrors: this.analyzeSpatialErrors(gameData),
      connectionErrors: this.analyzeConnectionErrors(gameData),
      persistenceIssues: this.analyzePersistence(gameData),
      sequenceErrors: this.analyzeSequenceErrors(gameData)
    };

    return metrics;
  }

  /**
   * Analisa erros espaciais
   */
  analyzeSpatialErrors(gameData) {
    // Implementação simplificada
    return {
      count: Object.values(this.errorPatterns.spatialOrientation).reduce(
        (sum, errors) => sum + errors.length, 0
      ),
      severity: 'medium'
    };
  }

  /**
   * Analisa erros de conexão
   */
  analyzeConnectionErrors(gameData) {
    // Implementação simplificada
    return {
      count: Object.values(this.errorPatterns.edgeMatching).reduce(
        (sum, errors) => sum + errors.length, 0
      ),
      severity: 'medium'
    };
  }

  /**
   * Analisa problemas de persistência
   */
  analyzePersistence(gameData) {
    // Implementação simplificada
    return {
      abandonedPieces: 0,
      giveUpAttempts: 0,
      severity: 'low'
    };
  }

  /**
   * Analisa erros de sequência
   */
  analyzeSequenceErrors(gameData) {
    // Implementação simplificada
    return {
      count: this.errorPatterns.sequenceErrors.length,
      severity: 'low'
    };
  }

  /**
   * Categoriza erros para análise posterior
   */
  categorizeErrors(errorMetrics) {
    // Implementação simplificada para categorizar erros
    // Esta função seria expandida em uma implementação completa
  }

  /**
   * Gera métricas de erro com base nos dados coletados
   */
  generateErrorMetrics(gameData) {
    // Aqui calculamos métricas agregadas sobre os padrões de erro
    return {
      totalSpatialErrors: Object.values(this.errorPatterns.spatialOrientation).reduce(
        (sum, errors) => sum + errors.length, 0
      ),
      totalConnectionErrors: Object.values(this.errorPatterns.edgeMatching).reduce(
        (sum, errors) => sum + errors.length, 0
      ),
      totalSequenceErrors: this.errorPatterns.sequenceErrors.length,
      spatialPerceptionScore: this.calculateSpatialPerceptionScore(),
      visualProcessingScore: this.calculateVisualProcessingScore(),
      spatialRelationsUnderstanding: this.calculateSpatialRelationsScore(),
      improvementTrend: this.calculateImprovementTrend(gameData)
    };
  }

  /**
   * Calcula pontuação de percepção espacial
   */
  calculateSpatialPerceptionScore() {
    // Implementação simplificada
    return 0.7;
  }

  /**
   * Calcula pontuação de processamento visual
   */
  calculateVisualProcessingScore() {
    // Implementação simplificada
    return 0.65;
  }

  /**
   * Calcula pontuação de compreensão de relações espaciais
   */
  calculateSpatialRelationsScore() {
    // Implementação simplificada
    return 0.6;
  }

  /**
   * Calcula tendência de melhoria ao longo do tempo
   */
  calculateImprovementTrend(gameData) {
    // Implementação simplificada
    return 0.5;
  }

  /**
   * Método de análise para compatibilidade com outros coletores
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
}

export default ErrorPatternCollector;
