/**
 * @file PerformanceDashboard.jsx
 * @description Dashboard de Performance - Portal Betina V3
 * @version 3.0.0
 */

import React, { useState, useEffect } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'
import LoadingSpinner from '../../common/LoadingSpinner'
import DashboardLayout from '../DashboardLayout/DashboardLayout'
import { useRealMetrics } from '../../../utils/realMetrics'
import styles from './PerformanceDashboard.module.css'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

const PerformanceDashboard = () => {
  const [timeframe, setTimeframe] = useState('30d')
  const [data, setData] = useState({
    metrics: {
      totalSessions: 0,
      avgAccuracy: 0,
      avgTime: 0,
      completionRate: 0,
      improvement: 0
    },
    performanceOverTime: { labels: [], datasets: [] },
    gamePerformance: { labels: [], datasets: [] },
    skillDistribution: { labels: [], datasets: [] }
  })
  const { metrics, loading, refresh } = useRealMetrics()

  // Função para formatar dados para gráficos
  const formatChartData = () => {
    if (!metrics || !metrics.weeklyData || metrics.weeklyData.length === 0) {
      return {
        performanceOverTime: {
          labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
          datasets: [{
            label: 'Precisão (%)',
            data: [0, 0, 0, 0],
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        gamePerformance: {
          labels: ['Sem dados'],
          datasets: [{
            label: 'Sessões',
            data: [0],
            backgroundColor: 'rgba(102, 126, 234, 0.8)'
          }]
        },
        skillDistribution: {
          labels: ['Sem dados'],
          datasets: [{
            data: [1],
            backgroundColor: ['#94a3b8'],
            borderWidth: 0
          }]
        }
      }
    }

    // Dados de performance ao longo do tempo
    const performanceOverTime = {
      labels: metrics.weeklyData.map(day => {
        const date = new Date(day.date)
        return date.toLocaleDateString('pt-BR', { weekday: 'short' })
      }),
      datasets: [{
        label: 'Precisão (%)',
        data: metrics.weeklyData.map(day => day.avgAccuracy || 0),
        borderColor: '#667eea',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        tension: 0.4,
        fill: true,
        yAxisID: 'y'
      }, {
        label: 'Sessões',
        data: metrics.weeklyData.map(day => day.sessions || 0),
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
        fill: true,
        yAxisID: 'y1'
      }]
    }

    // Performance por jogo
    const gameNames = Object.keys(metrics.gameProgress || {})
    const gamePerformance = {
      labels: gameNames.length > 0 ? gameNames.map(key => 
        metrics.gameProgress[key].name || key
      ) : ['Sem dados'],
      datasets: [{
        label: 'Sessões Completadas',
        data: gameNames.length > 0 ? gameNames.map(key => 
          metrics.gameProgress[key].sessions || 0
        ) : [0],
        backgroundColor: [
          'rgba(102, 126, 234, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)'
        ]
      }]
    }

    // Distribuição de precisão
    const accuracyRanges = {
      'Excelente (90-100%)': 0,
      'Bom (80-89%)': 0,
      'Regular (70-79%)': 0,
      'Precisa melhorar (<70%)': 0
    }

    gameNames.forEach(key => {
      const game = metrics.gameProgress[key]
      if (game && game.sessions > 0) {
        const avgAccuracy = game.avgScore || 0
        if (avgAccuracy >= 90) accuracyRanges['Excelente (90-100%)']++
        else if (avgAccuracy >= 80) accuracyRanges['Bom (80-89%)']++
        else if (avgAccuracy >= 70) accuracyRanges['Regular (70-79%)']++
        else accuracyRanges['Precisa melhorar (<70%)']++
      }
    })

    const skillDistribution = {
      labels: Object.keys(accuracyRanges),
      datasets: [{
        data: Object.values(accuracyRanges),
        backgroundColor: [
          '#48bb78',
          '#4ecdc4',
          '#ed8936',
          '#f56565'
        ],
        borderWidth: 2,
        borderColor: '#fff'
      }]
    }

    return {
      performanceOverTime,
      gamePerformance,
      skillDistribution
    }
  }

  // Função para carregar dados de performance
  const loadPerformanceData = () => {
    try {
      const { performanceOverTime, gamePerformance, skillDistribution } = formatChartData()

      const totalSessions = metrics.weeklyData?.reduce((sum, day) => sum + (day.sessions || 0), 0) || 0
      const avgAccuracy = metrics.weeklyData?.length > 0 
        ? metrics.weeklyData.reduce((sum, day) => sum + (day.avgAccuracy || 0), 0) / metrics.weeklyData.length 
        : 0
      const avgTime = metrics.weeklyData?.length > 0 
        ? metrics.weeklyData.reduce((sum, day) => sum + (day.avgTime || 0), 0) / metrics.weeklyData.length 
        : 0
      const completionRate = metrics.weeklyData?.length > 0 
        ? metrics.weeklyData.reduce((sum, day) => sum + (day.completionRate || 0), 0) / metrics.weeklyData.length 
        : 0

      setData({
        metrics: {
          totalSessions,
          avgAccuracy: Math.round(avgAccuracy),
          avgTime: Math.round(avgTime),
          completionRate: Math.round(completionRate),
          improvement: Math.round((avgAccuracy - (metrics.historicalAccuracy || 0)) / (metrics.historicalAccuracy || 1) * 100)
        },
        performanceOverTime,
        gamePerformance,
        skillDistribution
      })
    } catch (error) {
      console.error('Erro ao carregar dados de performance:', error)
      setData({
        metrics: {
          totalSessions: 0,
          avgAccuracy: 0,
          avgTime: 0,
          completionRate: 0,
          improvement: 0
        },
        performanceOverTime: { labels: [], datasets: [] },
        gamePerformance: { labels: [], datasets: [] },
        skillDistribution: { labels: [], datasets: [] }
      })
    }
  }

  // Carregar dados quando o componente monta ou timeframe muda
  useEffect(() => {
    const loadData = async () => {
      // O loading já é controlado pelo hook useRealMetrics
      await new Promise(resolve => setTimeout(resolve, 800))
      loadPerformanceData()
    }

    loadData()
  }, [timeframe, metrics])

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        type: 'linear',
        display: true,
        position: 'left',
      },
      y1: {
        type: 'linear',
        display: true,
        position: 'right',
        grid: {
          drawOnChartArea: false,
        },
      },
    },
  }

  // ✅ Controles personalizados para o dashboard
  const dashboardActions = (
    <select
      className={styles.timeframeSelector}
      value={timeframe}
      onChange={(e) => setTimeframe(e.target.value)}
    >
      <option value="7d">7 dias</option>
      <option value="30d">30 dias</option>
      <option value="90d">90 dias</option>
    </select>
  )

  return (
    <DashboardLayout
      title="Dashboard de Performance"
      subtitle="Métricas avançadas de performance e uso do sistema"
      icon="📊"
      loading={loading}
      activeDashboard="performance"
      availableDashboards={['performance', 'ai', 'neuropedagogical', 'multisensory']}
      actions={dashboardActions}
      refreshAction={refresh}
    >

      {/* Métricas de Performance */}
      <div className={styles.metricsGrid}>
        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Total de Sessões</h3>
            <div className={`${styles.metricIcon} ${styles.sessions}`}>🎮</div>
          </div>
          <div className={styles.metricValue}>{data.metrics.totalSessions}</div>
          <div className={`${styles.metricTrend} ${data.metrics.improvement >= 0 ? styles.trendPositive : styles.trendNegative}`}>
            {data.metrics.improvement >= 0 ? '↗️' : '↘️'} {Math.abs(data.metrics.improvement)}% no período
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Precisão Média</h3>
            <div className={`${styles.metricIcon} ${styles.accuracy}`}>🎯</div>
          </div>
          <div className={styles.metricValue}>{data.metrics.avgAccuracy}%</div>
          <div className={`${styles.metricTrend} ${data.metrics.avgAccuracy >= 80 ? styles.trendPositive : styles.trendNegative}`}>
            {data.metrics.avgAccuracy >= 80 ? '↗️ Melhorando' : '↘️ Atenção necessária'}
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Tempo Médio</h3>
            <div className={`${styles.metricIcon} ${styles.time}`}>⏱️</div>
          </div>
          <div className={styles.metricValue}>{data.metrics.avgTime}min</div>
          <div className={`${styles.metricTrend} ${data.metrics.avgTime <= 30 ? styles.trendPositive : styles.trendNegative}`}>
            {data.metrics.avgTime <= 30 ? '↗️ Otimizando' : '↘️ Pode otimizar'}
          </div>
        </div>

        <div className={styles.metricCard}>
          <div className={styles.metricHeader}>
            <h3 className={styles.metricTitle}>Taxa de Conclusão</h3>
            <div className={`${styles.metricIcon} ${styles.completion}`}>✅</div>
          </div>
          <div className={styles.metricValue}>{data.metrics.completionRate}%</div>
          <div className={`${styles.metricTrend} ${data.metrics.completionRate >= 80 ? styles.trendPositive : styles.trendNegative}`}>
            {data.metrics.completionRate >= 80 ? '↗️ Excelente' : '↘️ Pode melhorar'}
          </div>
        </div>
      </div>

      {/* Gráficos */}
      <div className={styles.chartsGrid}>
        <div className={styles.chartCard}>
          <h3 className={styles.chartTitle}>📈 Performance ao Longo do Tempo</h3>
          <div className={styles.chartContainer}>
            {data.performanceOverTime.datasets.length > 0 && (
              <Line data={data.performanceOverTime} options={chartOptions} />
            )}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h3 className={styles.chartTitle}>🎮 Performance por Categoria</h3>
          <div className={styles.chartContainer}>
            {data.gamePerformance.datasets.length > 0 && (
              <Bar data={data.gamePerformance} options={{ 
                ...chartOptions, 
                scales: { y: { beginAtZero: true, max: 100 } } 
              }} />
            )}
          </div>
        </div>

        <div className={styles.chartCard}>
          <h3 className={styles.chartTitle}>🏆 Distribuição de Habilidades</h3>
          <div className={styles.chartContainer}>
            {data.skillDistribution.datasets.length > 0 && (
              <Doughnut data={data.skillDistribution} options={{ 
                ...chartOptions, 
                scales: undefined 
              }} />
            )}
          </div>
        </div>
      </div>

      {/* Seção de Insights */}
      <div className={styles.insightsSection}>
        <h3 className={styles.insightsTitle}>
          💡 Insights de Performance
        </h3>
        <div className={styles.insightsGrid}>
          <div className={styles.insightCard}>
            <h4 className={styles.insightTitle}>Pontos Fortes</h4>
            <div className={styles.insightContent}>
              <p>• Excelente consistência nas atividades de lógica</p>
              <p>• Tempo de resposta melhorando constantemente</p>
              <p>• Alta taxa de conclusão das atividades</p>
            </div>
          </div>

          <div className={styles.insightCard}>
            <h4 className={styles.insightTitle}>Áreas de Melhoria</h4>
            <div className={styles.insightContent}>
              <p>• Coordenação motora pode ser trabalhada mais</p>
              <p>• Variação na performance em atividades de memória</p>
              <p>• Oportunidade de explorar atividades mais desafiadoras</p>
            </div>
          </div>

          <div className={styles.insightCard}>
            <h4 className={styles.insightTitle}>Recomendações</h4>
            <div className={styles.insightContent}>
              <p>• Manter regularidade nas sessões de treino</p>
              <p>• Explorar jogos que combinem múltiplas habilidades</p>
              <p>• Definir metas progressivas para cada categoria</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

export default PerformanceDashboard