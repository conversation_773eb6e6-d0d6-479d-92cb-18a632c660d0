import{j as e}from"./index-BkN74ywZ.js";import{r as a}from"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const o={dashboard:"_dashboard_1kcmw_2","top-right":"_top-right_1kcmw_18","top-left":"_top-left_1kcmw_23","bottom-right":"_bottom-right_1kcmw_28","bottom-left":"_bottom-left_1kcmw_33",active:"_active_1kcmw_39",inactive:"_inactive_1kcmw_43",minimized:"_minimized_1kcmw_48",header:"_header_1kcmw_55",title:"_title_1kcmw_65",icon:"_icon_1kcmw_73",status:"_status_1kcmw_77",statusDot:"_statusDot_1kcmw_85",controls:"_controls_1kcmw_98",toggleInsights:"_toggleInsights_1kcmw_103",minimizeButton:"_minimizeButton_1kcmw_104",expandButton:"_expandButton_1kcmw_105",section:"_section_1kcmw_131",metricsGrid:"_metricsGrid_1kcmw_151",metric:"_metric_1kcmw_151",label:"_label_1kcmw_167",value:"_value_1kcmw_174",progressScore:"_progressScore_1kcmw_181",scoreCircle:"_scoreCircle_1kcmw_187",circularChart:"_circularChart_1kcmw_193",circleBg:"_circleBg_1kcmw_198",circle:"_circle_1kcmw_198",scoreText:"_scoreText_1kcmw_212",scoreDetails:"_scoreDetails_1kcmw_222",skillBar:"_skillBar_1kcmw_226",bar:"_bar_1kcmw_239",barFill:"_barFill_1kcmw_247",insights:"_insights_1kcmw_255",insight:"_insight_1kcmw_255",slideIn:"_slideIn_1kcmw_1",success:"_success_1kcmw_271",warning:"_warning_1kcmw_276",celebration:"_celebration_1kcmw_281",progress:"_progress_1kcmw_181",insightIcon:"_insightIcon_1kcmw_291",insightMessage:"_insightMessage_1kcmw_295",recommendations:"_recommendations_1kcmw_302",recommendation:"_recommendation_1kcmw_302",recommendationIcon:"_recommendationIcon_1kcmw_319",recommendationText:"_recommendationText_1kcmw_323",footer:"_footer_1kcmw_329",stats:"_stats_1kcmw_335",statusIndicator:"_statusIndicator_1kcmw_343",inactiveMessage:"_inactiveMessage_1kcmw_352"},s=({gameType:s,userId:r,feedbackIntegration:i=!1,onFeedbackMessage:n,position:t="top-right",minimized:m=!1})=>{const[l,c]=a.useState(m),[d,b]=a.useState(!0),{isActive:h,analytics:u,shouldActivateAnalytics:v,sessionDuration:p,dataPointsCount:N}=((e={})=>{const{gameType:o,userId:s,enableRealTime:r=!0,feedbackIntegration:i=!1,updateInterval:n=2e3,maxDataPoints:t=100}=e,[m,l]=a.useState(!1),[c,d]=a.useState({performance:{accuracy:0,responseTime:0,streak:0,improvement:0},engagement:{focusLevel:0,interactionRate:0,sessionDuration:0,attentionSpan:0},therapeutic:{progressScore:0,skillDevelopment:{},adaptationLevel:0,recommendedActions:[]},realTimeInsights:[],feedbackMessages:[]}),b=a.useRef([]),h=a.useRef(Date.now()),u=a.useRef(Date.now()),v=a.useRef(null),p=a.useMemo(()=>r&&i&&o&&s,[r,i,o,s]),N=a.useCallback(e=>{if(!p)return;const a=Date.now(),r={...e,timestamp:a,gameType:o,userId:s,sessionTime:a-h.current};b.current.push(r),b.current.length>t&&(b.current=b.current.slice(-t)),(e.critical||e.feedbackTriggered)&&x()},[p,o,s,t]),D=a.useCallback(e=>{if(0===e.length)return{accuracy:0,responseTime:0,streak:0,improvement:0};const a=e.slice(-10),o=a.filter(e=>e.isCorrect).length/a.length*100,s=a.filter(e=>e.responseTime).map(e=>e.responseTime),r=s.length>0?s.reduce((e,a)=>e+a,0)/s.length:0;let i=0;for(let c=a.length-1;c>=0&&a[c].isCorrect;c--)i++;const n=a.slice(0,Math.floor(a.length/2)),t=a.slice(Math.floor(a.length/2)),m=n.length>0?n.filter(e=>e.isCorrect).length/n.length*100:0,l=(t.length>0?t.filter(e=>e.isCorrect).length/t.length*100:0)-m;return{accuracy:Math.round(o),responseTime:Math.round(r),streak:i,improvement:Math.round(l)}},[]),j=a.useCallback(e=>{if(0===e.length)return{focusLevel:0,interactionRate:0,sessionDuration:0,attentionSpan:0};const a=(Date.now()-h.current)/1e3,o=e.length/(a/60),s=e.slice(-20),r=R(s.map(e=>e.responseTime||0)),i=Math.max(0,100-r/100),n=[];for(let l=1;l<e.length;l++)n.push(e[l].timestamp-e[l-1].timestamp);const t=n.length>0?n.reduce((e,a)=>e+a,0)/n.length:0,m=Math.min(100,Math.max(0,100-t/1e3));return{focusLevel:Math.round(i),interactionRate:Math.round(10*o)/10,sessionDuration:Math.round(a),attentionSpan:Math.round(m)}},[]),T=a.useCallback((e,a,o)=>{const s=[],r=[];return a.accuracy<50?(s.push({type:"warning",category:"performance",message:"Precisão baixa detectada. Considere ajustar a dificuldade.",timestamp:Date.now(),priority:"high"}),r.push("Reduzir dificuldade do jogo")):a.accuracy>90&&(s.push({type:"success",category:"performance",message:"Excelente precisão! Pronto para próximo nível.",timestamp:Date.now(),priority:"medium"}),r.push("Aumentar dificuldade do jogo")),o.focusLevel<30&&(s.push({type:"warning",category:"engagement",message:"Nível de foco baixo. Considere uma pausa.",timestamp:Date.now(),priority:"high"}),r.push("Sugerir pausa de 2-3 minutos")),a.streak>=5&&s.push({type:"celebration",category:"motivation",message:`Fantástico! ${a.streak} acertos seguidos!`,timestamp:Date.now(),priority:"low"}),a.improvement>20&&s.push({type:"progress",category:"development",message:`Melhoria significativa de ${a.improvement}% detectada!`,timestamp:Date.now(),priority:"medium"}),{progressScore:Math.round(.4*a.accuracy+.3*o.focusLevel+.2*o.attentionSpan+.1*Math.min(a.improvement+50,100)),skillDevelopment:{attention:o.attentionSpan,focus:o.focusLevel,accuracy:a.accuracy,consistency:100-(a.responseTime>0?Math.min(a.responseTime/50,100):0)},adaptationLevel:Math.round((a.improvement+50)/2),recommendedActions:r,insights:s}},[]),x=a.useCallback(()=>{if(!p||0===b.current.length)return;const e=[...b.current],a=D(e),o=j(e),s=T(e,a,o),r=s.insights.filter(e=>"high"===e.priority||"celebration"===e.type).map(e=>({id:`feedback_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,type:e.type,message:e.message,category:e.category,timestamp:e.timestamp,autoShow:"high"===e.priority,duration:"celebration"===e.type?3e3:5e3}));d(e=>({...e,performance:a,engagement:o,therapeutic:{...s,insights:[...e.therapeutic.insights||[],...s.insights].slice(-20)},realTimeInsights:s.insights.slice(-5),feedbackMessages:[...e.feedbackMessages||[],...r].slice(-10),lastUpdate:Date.now()})),u.current=Date.now()},[p,D,j,T]),R=e=>{if(0===e.length)return 0;const a=e.reduce((e,a)=>e+a,0)/e.length,o=e.reduce((e,o)=>e+Math.pow(o-a,2),0)/e.length;return Math.sqrt(o)},f=a.useCallback(()=>{p&&(l(!0),h.current=Date.now(),b.current=[],v.current&&clearInterval(v.current),v.current=setInterval(()=>{x()},n))},[p,o,s,n,x]),g=a.useCallback(()=>{l(!1),v.current&&(clearInterval(v.current),v.current=null),x()},[x]),_=a.useCallback(()=>{b.current=[],h.current=Date.now(),d({performance:{accuracy:0,responseTime:0,streak:0,improvement:0},engagement:{focusLevel:0,interactionRate:0,sessionDuration:0,attentionSpan:0},therapeutic:{progressScore:0,skillDevelopment:{},adaptationLevel:0,recommendedActions:[]},realTimeInsights:[],feedbackMessages:[]})},[]);return a.useEffect(()=>()=>{v.current&&clearInterval(v.current)},[]),a.useEffect(()=>{p&&!m?f():!p&&m&&g()},[p,m,f,g]),{isActive:m,analytics:c,shouldActivateAnalytics:p,addDataPoint:N,startAnalytics:f,stopAnalytics:g,resetAnalytics:_,analyzeDataImmediate:x,sessionDuration:(Date.now()-h.current)/1e3,dataPointsCount:b.current.length}})({gameType:s,userId:r,enableRealTime:!0,feedbackIntegration:i,updateInterval:2e3});a.useEffect(()=>{if(u.feedbackMessages.length>0&&n){const e=u.feedbackMessages[u.feedbackMessages.length-1];e.autoShow&&n(e)}},[u.feedbackMessages,n]);const D=e=>`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,j=e=>e>=80?"#4CAF50":e>=60?"#FF9800":"#F44336",T=e=>{switch(e){case"success":return"✅";case"warning":return"⚠️";case"celebration":return"🎉";case"progress":return"📈";default:return"ℹ️"}};return v?l?e.jsxDEV("div",{className:`${o.dashboard} ${o.minimized} ${o[t]}`,children:e.jsxDEV("button",{className:o.expandButton,onClick:()=>c(!1),title:"Expandir Dashboard",children:["📊 ",u.performance.accuracy,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:76,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:75,columnNumber:7},void 0):e.jsxDEV("div",{className:`${o.dashboard} ${o[t]} ${h?o.active:""}`,children:[e.jsxDEV("div",{className:o.header,children:[e.jsxDEV("div",{className:o.title,children:[e.jsxDEV("span",{className:o.icon,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:92,columnNumber:11},void 0),e.jsxDEV("span",{children:"Dashboard Tempo Real"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:93,columnNumber:11},void 0),e.jsxDEV("div",{className:o.status,children:[e.jsxDEV("div",{className:`${o.statusDot} ${h?o.active:""}`},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:95,columnNumber:13},void 0),e.jsxDEV("span",{children:h?"Ativo":"Inativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:96,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:94,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:91,columnNumber:9},void 0),e.jsxDEV("div",{className:o.controls,children:[e.jsxDEV("button",{className:o.toggleInsights,onClick:()=>b(!d),title:d?"Ocultar Insights":"Mostrar Insights",children:d?"👁️":"👁️‍🗨️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:100,columnNumber:11},void 0),e.jsxDEV("button",{className:o.minimizeButton,onClick:()=>c(!0),title:"Minimizar Dashboard",children:"➖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:107,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:99,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:90,columnNumber:7},void 0),e.jsxDEV("div",{className:o.section,children:[e.jsxDEV("h4",{children:"🎯 Performance"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:119,columnNumber:9},void 0),e.jsxDEV("div",{className:o.metricsGrid,children:[e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Precisão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:122,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,style:{color:j(u.performance.accuracy)},children:[u.performance.accuracy,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:123,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:121,columnNumber:11},void 0),e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Tempo Resp."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:131,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,children:[u.performance.responseTime,"ms"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:132,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:130,columnNumber:11},void 0),e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Sequência"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:137,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,children:u.performance.streak},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:138,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:136,columnNumber:11},void 0),e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Melhoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:143,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,style:{color:u.performance.improvement>=0?"#4CAF50":"#F44336"},children:[u.performance.improvement>0?"+":"",u.performance.improvement,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:144,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:142,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:120,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:118,columnNumber:7},void 0),e.jsxDEV("div",{className:o.section,children:[e.jsxDEV("h4",{children:"🧠 Engajamento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:156,columnNumber:9},void 0),e.jsxDEV("div",{className:o.metricsGrid,children:[e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Foco"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:159,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,style:{color:j(u.engagement.focusLevel)},children:[u.engagement.focusLevel,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:160,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:158,columnNumber:11},void 0),e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Atenção"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:168,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,style:{color:j(u.engagement.attentionSpan)},children:[u.engagement.attentionSpan,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:169,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:167,columnNumber:11},void 0),e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Interações/min"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:177,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,children:u.engagement.interactionRate},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:178,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:176,columnNumber:11},void 0),e.jsxDEV("div",{className:o.metric,children:[e.jsxDEV("span",{className:o.label,children:"Duração"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:183,columnNumber:13},void 0),e.jsxDEV("span",{className:o.value,children:D(p)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:184,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:182,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:157,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:155,columnNumber:7},void 0),e.jsxDEV("div",{className:o.section,children:[e.jsxDEV("h4",{children:"🏆 Progresso Terapêutico"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:193,columnNumber:9},void 0),e.jsxDEV("div",{className:o.progressScore,children:[e.jsxDEV("div",{className:o.scoreCircle,children:[e.jsxDEV("svg",{viewBox:"0 0 36 36",className:o.circularChart,children:[e.jsxDEV("path",{className:o.circleBg,d:"M18 2.0845\n                  a 15.9155 15.9155 0 0 1 0 31.831\n                  a 15.9155 15.9155 0 0 1 0 -31.831"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:197,columnNumber:15},void 0),e.jsxDEV("path",{className:o.circle,strokeDasharray:`${u.therapeutic.progressScore}, 100`,d:"M18 2.0845\n                  a 15.9155 15.9155 0 0 1 0 31.831\n                  a 15.9155 15.9155 0 0 1 0 -31.831"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:203,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:196,columnNumber:13},void 0),e.jsxDEV("div",{className:o.scoreText,children:u.therapeutic.progressScore},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:211,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:195,columnNumber:11},void 0),e.jsxDEV("div",{className:o.scoreDetails,children:[e.jsxDEV("div",{className:o.skillBar,children:[e.jsxDEV("span",{children:"Atenção"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:217,columnNumber:15},void 0),e.jsxDEV("div",{className:o.bar,children:e.jsxDEV("div",{className:o.barFill,style:{width:`${u.therapeutic.skillDevelopment.attention||0}%`}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:219,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:218,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:216,columnNumber:13},void 0),e.jsxDEV("div",{className:o.skillBar,children:[e.jsxDEV("span",{children:"Foco"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:226,columnNumber:15},void 0),e.jsxDEV("div",{className:o.bar,children:e.jsxDEV("div",{className:o.barFill,style:{width:`${u.therapeutic.skillDevelopment.focus||0}%`}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:228,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:227,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:225,columnNumber:13},void 0),e.jsxDEV("div",{className:o.skillBar,children:[e.jsxDEV("span",{children:"Precisão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:235,columnNumber:15},void 0),e.jsxDEV("div",{className:o.bar,children:e.jsxDEV("div",{className:o.barFill,style:{width:`${u.therapeutic.skillDevelopment.accuracy||0}%`}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:237,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:236,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:234,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:215,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:194,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:192,columnNumber:7},void 0),d&&u.realTimeInsights.length>0&&e.jsxDEV("div",{className:o.section,children:[e.jsxDEV("h4",{children:"💡 Insights Tempo Real"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:250,columnNumber:11},void 0),e.jsxDEV("div",{className:o.insights,children:u.realTimeInsights.slice(-3).map((a,s)=>e.jsxDEV("div",{className:`${o.insight} ${o[a.type]}`,children:[e.jsxDEV("span",{className:o.insightIcon,children:T(a.type)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:257,columnNumber:17},void 0),e.jsxDEV("span",{className:o.insightMessage,children:a.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:260,columnNumber:17},void 0)]},`${a.timestamp}-${s}`,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:253,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:251,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:249,columnNumber:9},void 0),u.therapeutic.recommendedActions.length>0&&e.jsxDEV("div",{className:o.section,children:[e.jsxDEV("h4",{children:"🎯 Ações Recomendadas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:272,columnNumber:11},void 0),e.jsxDEV("div",{className:o.recommendations,children:u.therapeutic.recommendedActions.slice(0,2).map((a,s)=>e.jsxDEV("div",{className:o.recommendation,children:[e.jsxDEV("span",{className:o.recommendationIcon,children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:276,columnNumber:17},void 0),e.jsxDEV("span",{className:o.recommendationText,children:a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:277,columnNumber:17},void 0)]},s,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:275,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:273,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:271,columnNumber:9},void 0),e.jsxDEV("div",{className:o.footer,children:e.jsxDEV("div",{className:o.stats,children:[e.jsxDEV("span",{children:["📊 ",N," pontos"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:287,columnNumber:11},void 0),e.jsxDEV("span",{children:["⏱️ ",D(p)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:288,columnNumber:11},void 0),e.jsxDEV("span",{className:`${o.statusIndicator} ${h?o.active:""}`,children:h?"🟢 Ativo":"🔴 Inativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:289,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:286,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:285,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:88,columnNumber:5},void 0):e.jsxDEV("div",{className:`${o.dashboard} ${o.inactive} ${o[t]}`,children:e.jsxDEV("div",{className:o.inactiveMessage,children:[e.jsxDEV("span",{children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:66,columnNumber:11},void 0),e.jsxDEV("p",{children:"Dashboard em tempo real disponível apenas com feedback integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:67,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:65,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RealTimeDashboard/RealTimeDashboard.jsx",lineNumber:64,columnNumber:7},void 0)};export{s as default};
