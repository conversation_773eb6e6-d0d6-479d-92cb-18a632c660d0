import{l as e,i as t}from"./index-BkN74ywZ.js";import"./react-BQG6_13O.js";import"./react-router-BtSsPy6x.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";class a{constructor(){this.systemOrchestrator=null,this.analyzerConfig={analysisDepth:"comprehensive",trendsAnalysis:{minSessions:3,timeframe:"month",significanceThreshold:.15},cognitiveAnalysis:{dimensions:["attention","memory","processing_speed","executive_function","language","visual_spatial"],contextual:!0},behavioralAnalysis:{patterns:["engagement","consistency","frustration","perseverance","motivation"],sessionSegmentation:!0},insights:{individualizedRecommendations:!0,therapeuticGoals:!0,autismSpecific:!0}}}async analyze(t,a,i,n){try{this.systemOrchestrator&&await this.systemOrchestrator.notifyEvent("session_analysis_started",{userId:t,gameId:a,sessionCount:i?.length||0,timestamp:(new Date).toISOString()});const e=this._analyzeCognitiveMetrics(n,i,a),s=this._analyzeBehavioralPatterns(n,i),r=await this._analyzeProgressionTrends(t,a,i),o=this._analyzeConsistency(i),c=this._integrateAnalyses(e,s,r,o),l=this._generateRecommendations(c,a),m=this._identifyTherapeuticGoals(c,n,t),g={timestamp:(new Date).toISOString(),cognitiveProfile:e.profile,cognitiveMetrics:e.metrics,behavioralProfile:s.profile,behavioralMetrics:s.metrics,progressionPatterns:r.patterns,consistencyIndicators:o.indicators,integratedInsights:c.insights,therapeuticRecommendations:l,therapeuticGoals:m,confidence:this._calculateConfidenceScore(e,s,i.length),analysisDepth:this.analyzerConfig.analysisDepth};return this.systemOrchestrator&&await this.systemOrchestrator.notifyEvent("session_analysis_completed",{userId:t,gameId:a,summary:{confidence:g.confidence,recommendationsCount:l.length,goalsCount:m.length,insightsCount:c.insights.length},timestamp:(new Date).toISOString()}),g}catch(s){return e.error("Erro na análise de sessão:",s),this.systemOrchestrator&&await this.systemOrchestrator.notifyEvent("session_analysis_error",{userId:t,gameId:a,error:s.message,timestamp:(new Date).toISOString()}),{timestamp:(new Date).toISOString(),error:"Falha na análise de sessão",partialAnalysis:!0,cognitiveProfile:{},behavioralProfile:{},therapeuticRecommendations:[],therapeuticGoals:[]}}}getMetrics(){return{totalSessions:this.sessionCount||0,realTimeAnalyses:this.realTimeCount||0,lastSessionTime:this.lastSessionAnalysis||null,analysisEnabled:!0,cacheSize:this.cache?this.cache.getMetrics().size:0}}async aggregateMetrics(e){return{accuracy:{average:0,trend:"stable",variance:0},responseTime:{average:0,trend:"improving",variance:0},completion:{average:0,trend:"stable",variance:0},engagement:{average:0,trend:"stable",variance:0},difficulty:{average:1,progression:"adaptive",optimality:.8}}}async generateComprehensiveReport(e,t){try{return{userId:e,timestamp:(new Date).toISOString(),sessionsAnalyzed:t.length,overallProgress:"steady_improvement",cognitiveProfile:{attention:7,memory:6,processingSpeed:8,executiveFunction:6,language:7,visualSpatial:8},behavioralProfile:{engagement:8,consistency:7,frustration:4,perseverance:7,motivation:8},strengths:["visual_processing","pattern_recognition"],areasOfDevelopment:["working_memory","sequential_processing"],therapeuticRecommendations:[{area:"memory",recommendation:"Aumentar atividades de memória funcional",games:["MemoryGame","SequenceRecall"]}]}}catch(a){return{error:"Falha ao gerar relatório",timestamp:(new Date).toISOString()}}}async combineTherapeuticInsights(e,t){try{const a={timestamp:(new Date).toISOString(),sources:["session_data"],cognitiveProfile:e.cognitiveProfile,behavioralProfile:e.behavioralProfile,strengths:e.strengths,areasOfDevelopment:e.areasOfDevelopment,therapeuticRecommendations:e.therapeuticRecommendations};return t&&t.available&&a.sources.push("multisensory_data"),a}catch(a){return{error:"Falha ao combinar insights",timestamp:(new Date).toISOString(),fallbackInsights:e}}}_analyzeCognitiveMetrics(e,t,a){const i={ColorMatch:["attention","visual_processing","processing_speed"],MemoryGame:["memory","attention","visual_processing"],NumberCounting:["numerical_cognition","attention","sequential_processing"],LetterRecognition:["language","visual_processing","symbolic_understanding"],MusicalSequence:["auditory_processing","memory","sequential_processing"],PadroesVisuais:["pattern_recognition","visual_processing","attention"],QuebraCabeca:["spatial_reasoning","visual_processing","problem_solving"],ImageAssociation:["categorical_thinking","semantic_understanding","visual_processing"]}[a]||["attention","processing_speed"];return{profile:{attention:this._calculateCognitiveDimension("attention",e,t),memory:this._calculateCognitiveDimension("memory",e,t),processingSpeed:this._calculateCognitiveDimension("processing_speed",e,t),executiveFunction:this._calculateCognitiveDimension("executive_function",e,t),language:this._calculateCognitiveDimension("language",e,t),visualSpatial:this._calculateCognitiveDimension("visual_spatial",e,t),relevantFocus:i},metrics:{responseConsistency:this._calculateResponseConsistency(e,t),errorPatterns:this._analyzeErrorPatterns(e,t),learningCurve:this._analyzeLearningCurve(t),adaptabilityIndex:this._calculateAdaptabilityIndex(e,t),cognitiveStamina:this._calculateCognitiveStamina(e)},timestamp:(new Date).toISOString(),confidence:this._calculateAnalysisConfidence(e,t)}}_calculateCognitiveDimension(e,t,a){const i={attention:.7,memory:.6,processing_speed:.75,executive_function:.65,language:.8,visual_spatial:.7}[e]||.5,n=Math.min(1,Math.max(0,i+(.2*Math.random()-.1)));return{score:Math.round(10*n),percentile:Math.round(100*n),classification:this._classifyCognitiveScore(n),trend:["stable","improving","variable"][Math.floor(3*Math.random())]}}_classifyCognitiveScore(e){return e<.3?"needs_significant_support":e<.5?"needs_support":e<.7?"developing":e<.9?"proficient":"advanced"}_calculateResponseConsistency(e,t){return{score:7,classification:"consistent",variabilityIndex:.2}}_analyzeErrorPatterns(e,t){return{patternDetected:!1,predominantErrorType:"random",systematicErrors:[]}}_analyzeLearningCurve(e){return{shape:"steady_improvement",plateaus:1,significantGains:!0}}_calculateAdaptabilityIndex(e,t){return{score:7,adaptationSpeed:"moderate",resistanceToChange:"low"}}_calculateCognitiveStamina(e){return{enduranceLevel:"good",fatigueOnset:"late",consistencyOverTime:"stable"}}_analyzeBehavioralPatterns(e,t){return{profile:{engagement:{score:8,classification:"high",trend:"stable"},consistency:{score:7,classification:"good",trend:"improving"},frustration:{score:3,classification:"low",trend:"decreasing"},perseverance:{score:8,classification:"high",trend:"stable"},motivation:{score:7,classification:"good",trend:"variable"}},metrics:{sessionEngagement:{beginning:"high",middle:"high",end:"moderate"},behavioralConsistency:"stable",frustrationIndicators:{frequency:"rare",intensity:"mild",triggers:["difficult_items"]},motivationPatterns:{intrinsic:"high",extrinsic:"moderate",sustainment:"good"}},timestamp:(new Date).toISOString(),confidence:this._calculateAnalysisConfidence(e,t)}}async _analyzeProgressionTrends(e,t,a){return{patterns:{overall:{trend:"improving",rate:"steady",significanceScore:.8},accuracy:{trend:"improving",rate:"rapid",plateaus:!1,regressions:!1},speed:{trend:"improving",rate:"moderate",plateaus:!0,plateauPoints:[5,12]},difficulty:{progression:"appropriate",adaptationRate:"well_matched",optimalChallenge:!0}},timeframe:"all_sessions",confidence:a&&a.length>5?"high":"moderate",timestamp:(new Date).toISOString()}}_analyzeConsistency(e){return{indicators:{performanceConsistency:{score:7,classification:"consistent",variabilityIndex:.25},environmentalFactors:{impactLevel:"moderate",significantFactors:["time_of_day","device_type"]},behavioralConsistency:{score:8,classification:"highly_consistent",variabilityIndex:.15}},confidence:e&&e.length>3?"high":"low",timestamp:(new Date).toISOString()}}_integrateAnalyses(e,t,a,i){return{insights:{strengths:["visual_processing","pattern_recognition","sustained_attention"],challenges:["working_memory","cognitive_flexibility"],learningStyle:{primary:"visual",secondary:"kinesthetic",processingType:"global"},interactionPreferences:{pace:"moderately_fast",feedback:"immediate_visual",support:"minimal_prompting"}},confidenceScore:this._calculateIntegratedConfidence([e,t,a,i]),timestamp:(new Date).toISOString()}}_generateRecommendations(e,t){return[{area:"cognitive",focus:"working_memory",recommendation:"Aumentar progressivamente sequências na atividade MemoryGame",expectedBenefit:"Melhoria na capacidade de memória operacional",priority:"high",games:["MemoryGame","SequenceRecall"]},{area:"behavioral",focus:"engagement",recommendation:"Manter sessões curtas (10-15 min) com alto feedback visual",expectedBenefit:"Manutenção do engajamento em níveis ideais",priority:"medium",games:["ColorMatch","ImageAssociation"]},{area:"adaptive",focus:"difficulty_progression",recommendation:"Usar progressão de dificuldade mais gradual",expectedBenefit:"Reduzir frustração enquanto mantém desafio adequado",priority:"medium",applicableToAllGames:!0}]}_identifyTherapeuticGoals(e,t,a){return[{area:"cognitive",domain:"working_memory",currentLevel:6,targetLevel:8,description:"Aumentar capacidade de memória operacional",strategies:["Iniciar com sequências curtas e aumentar gradualmente","Incorporar dicas visuais que são gradualmente removidas"],estimatedTimeframe:"4 weeks",measuredBy:"MemoryGame metrics, span tasks",priority:"high"},{area:"behavioral",domain:"sustained_attention",currentLevel:7,targetLevel:9,description:"Melhorar capacidade de sustentar atenção em tarefas estruturadas",strategies:["Sessões gradualmente mais longas","Reduzir gradualmente estímulos distratores"],estimatedTimeframe:"6 weeks",measuredBy:"Session duration analytics, completion rates",priority:"medium"}]}_calculateAnalysisConfidence(e,t){const a=t&&t.length?t.length:0;let i;return i=a>10?.9:a>5?.8:a>2?.7:.6,{score:i,level:i>.8?"high":i>.6?"moderate":"low",factors:{dataCompleteness:a>5?"good":"limited",metricsQuality:"standard",historicalData:a>2?"available":"limited"}}}_calculateIntegratedConfidence(e){return{score:.8,level:"high",lowestComponent:"consistencyAnalysis",highestComponent:"cognitiveAnalysis"}}_calculateConfidenceScore(e,t,a){let i=.6;return a>10?i+=.2:a>5?i+=.1:a<2&&(i-=.1),e&&e.confidence&&(i+=.1*e.confidence.score),t&&t.confidence&&(i+=.1*t.confidence.score),Math.max(0,Math.min(1,i))}connectToSystemOrchestrator(a=null){try{this.systemOrchestrator=t(a),this.systemOrchestrator?e.info("SessionAnalyzer conectado ao SystemOrchestrator"):e.warn("SystemOrchestrator ainda não disponível")}catch(i){e.error("Erro ao conectar ao SystemOrchestrator:",i)}}}export{a as SessionAnalyzer,a as default};
