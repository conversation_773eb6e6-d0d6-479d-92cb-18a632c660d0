/**
 * 🔧 POSTGRESQL COMPATIBILITY CONFIGURATION
 * Configurações de compatibilidade para PostgreSQL
 * Portal Betina V3
 */

// Configurações globais para PostgreSQL
if (typeof global !== 'undefined') {
  // Configurar timezone padrão
  process.env.TZ = process.env.TZ || 'America/Sao_Paulo';
  
  // Configurar encoding padrão
  process.env.PGCLIENTENCODING = process.env.PGCLIENTENCODING || 'UTF8';
  
  // Configurar SSL se necessário
  if (process.env.DB_SSL_ENABLED === 'true') {
    process.env.PGSSLMODE = process.env.PGSSLMODE || 'require';
  }
}

// Configurações de compatibilidade para diferentes ambientes
const compatibilityConfig = {
  // Configurações para desenvolvimento
  development: {
    ssl: false,
    connectionTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    max: 10,
    statement_timeout: 30000,
    query_timeout: 30000
  },
  
  // Configurações para produção
  production: {
    ssl: {
      rejectUnauthorized: false
    },
    connectionTimeoutMillis: 10000,
    idleTimeoutMillis: 60000,
    max: 20,
    statement_timeout: 60000,
    query_timeout: 60000
  },
  
  // Configurações para testes
  test: {
    ssl: false,
    connectionTimeoutMillis: 3000,
    idleTimeoutMillis: 10000,
    max: 5,
    statement_timeout: 10000,
    query_timeout: 10000
  }
};

// Aplicar configurações baseadas no ambiente
const currentEnv = process.env.NODE_ENV || 'development';
const config = compatibilityConfig[currentEnv] || compatibilityConfig.development;

// Exportar configurações
export default config;

// Configurações específicas para tipos de dados PostgreSQL
export const pgTypes = {
  // Configurar parsing de tipos específicos
  TIMESTAMP: 1114,
  TIMESTAMPTZ: 1184,
  JSON: 114,
  JSONB: 3802,
  UUID: 2950,
  NUMERIC: 1700,
  BIGINT: 20
};

// Funções de compatibilidade
export const pgUtils = {
  /**
   * Escapar strings para PostgreSQL
   */
  escapeString: (str) => {
    if (typeof str !== 'string') return str;
    return str.replace(/'/g, "''");
  },
  
  /**
   * Formatar timestamp para PostgreSQL
   */
  formatTimestamp: (date) => {
    if (!date) return null;
    if (typeof date === 'string') date = new Date(date);
    return date.toISOString();
  },
  
  /**
   * Converter JSON para PostgreSQL JSONB
   */
  toJsonb: (obj) => {
    if (obj === null || obj === undefined) return null;
    return JSON.stringify(obj);
  },
  
  /**
   * Converter JSONB para objeto JavaScript
   */
  fromJsonb: (jsonbStr) => {
    if (!jsonbStr) return null;
    try {
      return JSON.parse(jsonbStr);
    } catch (error) {
      console.warn('Erro ao parsear JSONB:', error);
      return null;
    }
  }
};

console.log('✅ PostgreSQL compatibility configuration loaded');
