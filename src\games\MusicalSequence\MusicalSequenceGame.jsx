/**
 * 🎵 MUSICAL SEQUENCE V3 - JOGO DE SEQUÊNCIA MUSICAL COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import styles from './MusicalSequence.module.css';
import { MusicalSequenceConfig } from './MusicalSequenceConfig';
import { MusicalSequenceMetrics } from './MusicalSequenceMetrics';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { MusicalSequenceCollectorsHub } from './collectors/index.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - MUSICAL SEQUENCE
const ACTIVITY_TYPES = {
  SEQUENCE_REPRODUCTION: {
    id: 'sequence_reproduction',
    name: 'Reprodução de Sequência',
    icon: '🔄',
    description: 'Reproduza a sequência musical tocada',
    component: 'SequenceReproductionActivity'
  },
  RHYTHM_PATTERNS: {
    id: 'rhythm_patterns',
    name: 'Padrões Rítmicos',
    icon: '🥁',
    description: 'Identifique e reproduza padrões rítmicos',
    component: 'RhythmPatternsActivity'
  },
  MELODY_COMPLETION: {
    id: 'melody_completion',
    name: 'Completar Melodia',
    icon: '🎼',
    description: 'Complete melodias inacabadas',
    component: 'MelodyCompletionActivity'
  },
  INSTRUMENT_RECOGNITION: {
    id: 'instrument_recognition',
    name: 'Reconhecimento de Instrumentos',
    icon: '🎺',
    description: 'Identifique diferentes instrumentos musicais',
    component: 'InstrumentRecognitionActivity'
  },
  MUSICAL_MEMORY: {
    id: 'musical_memory',
    name: 'Memória Musical',
    icon: '🧠',
    description: 'Lembre-se de sequências musicais complexas',
    component: 'MusicalMemoryActivity'
  },
  CREATIVE_COMPOSITION: {
    id: 'creative_composition',
    name: 'Composição Criativa',
    icon: '🎨',
    description: 'Crie suas próprias sequências musicais',
    component: 'CreativeCompositionActivity'
  }
};

const MusicalSequenceGame = ({ onBack, onComplete }) => {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎵 Inicializar coletores avançados de sequência musical
  const [collectorsHub] = useState(() => new MusicalSequenceCollectorsHub());

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id,
    activityCycle: [
      ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id,
      ACTIVITY_TYPES.RHYTHM_PATTERNS.id,
      ACTIVITY_TYPES.MELODY_COMPLETION.id,
      ACTIVITY_TYPES.INSTRUMENT_RECOGNITION.id,
      ACTIVITY_TYPES.MUSICAL_MEMORY.id,
      ACTIVITY_TYPES.CREATIVE_COMPOSITION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      sequenceReproduction: {
        sequence: [],
        userSequence: [],
        isPlaying: false
      },
      rhythmPatterns: {
        pattern: [],
        userPattern: [],
        tempo: 120
      },
      melodyCompletion: {
        melody: [],
        missingNotes: [],
        userCompletion: []
      },
      instrumentRecognition: {
        currentInstrument: null,
        options: [],
        selectedInstrument: null
      },
      musicalMemory: {
        sequences: [],
        currentSequence: 0,
        userRecall: []
      },
      creativeComposition: {
        userComposition: [],
        availableNotes: [],
        isRecording: false
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('musical_sequence');

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration('musical_sequence', collectorsHub);

  // 🎯 Hook orquestrador terapêutico
  const {
    initializeSession: initTherapeutic,
    recordActivity: recordTherapeuticActivity,
    generateReport: generateTherapeuticReport,
    getRecommendations: getTherapeuticRecommendations,
    finalizeSession: finalizeTherapeutic
  } = useTherapeuticOrchestrator();

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => !prev);
    if (!ttsActive) {
      speak('TTS ativado');
    }
  }, [ttsActive, speak]);

  // 🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS

  // Inicializar métricas
  useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = MusicalSequenceMetrics;
    }
  }, []);

  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'musical_sequence',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // Inicializar sessões dos hooks
      await initMultisensory();
      await initTherapeutic();

      // Gerar primeira atividade
      generateNewRound();

      speak('Jogo iniciado! Vamos começar com sequências musicais.');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, initMultisensory, initTherapeutic, user, speak]);

  // Função para trocar atividade
  const changeActivity = useCallback((activityId) => {
    if (gameState.activityRoundCount < 4 && gameState.currentActivity !== activityId) {
      speak(`Complete mais ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro.`);
      return;
    }

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      activityRoundCount: 0,
      activityIndex: prev.activityCycle.indexOf(activityId)
    }));

    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);

    generateNewRound();
  }, [gameState.activityRoundCount, gameState.currentActivity, speak]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback(() => {
    const currentActivity = gameState.currentActivity;

    setGameState(prev => ({
      ...prev,
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ''
    }));

    // Gerar dados específicos da atividade
    switch (currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id:
        generateSequenceReproduction();
        break;
      case ACTIVITY_TYPES.RHYTHM_PATTERNS.id:
        generateRhythmPatterns();
        break;
      case ACTIVITY_TYPES.MELODY_COMPLETION.id:
        generateMelodyCompletion();
        break;
      case ACTIVITY_TYPES.INSTRUMENT_RECOGNITION.id:
        generateInstrumentRecognition();
        break;
      case ACTIVITY_TYPES.MUSICAL_MEMORY.id:
        generateMusicalMemory();
        break;
      case ACTIVITY_TYPES.CREATIVE_COMPOSITION.id:
        generateCreativeComposition();
        break;
      default:
        generateSequenceReproduction();
    }
  }, [gameState.currentActivity]);

  // Funções de geração de atividades (placeholder)
  const generateSequenceReproduction = useCallback(() => {
    // Implementar lógica específica
  }, []);

  const generateRhythmPatterns = useCallback(() => {
    // Implementar lógica específica
  }, []);

  const generateMelodyCompletion = useCallback(() => {
    // Implementar lógica específica
  }, []);

  const generateInstrumentRecognition = useCallback(() => {
    // Implementar lógica específica
  }, []);

  const generateMusicalMemory = useCallback(() => {
    // Implementar lógica específica
  }, []);

  const generateCreativeComposition = useCallback(() => {
    // Implementar lógica específica
  }, []);

  // Função para alternar TTS (compatibilidade)
  const toggleTTSOld = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('musicalSequence_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Estados principais
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [difficulty, setDifficulty] = useState('easy');
  const [sequence, setSequence] = useState([]);
  const [playerSequence, setPlayerSequence] = useState([]);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [currentRound, setCurrentRound] = useState(1);
  const [score, setScore] = useState(0);
  const [streak, setStreak] = useState(0);
  const [totalAttempts, setTotalAttempts] = useState(0);
  const [correctAttempts, setCorrectAttempts] = useState(0);
  const [activeInstrument, setActiveInstrument] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [feedback, setFeedback] = useState({ show: false, type: '', message: '' });
  const [gameStartTime, setGameStartTime] = useState(null);
  const [maxRoundsPerLevel] = useState(3);
  const audioRef = useRef({});
  const audioContextRef = useRef(null);

  // Sistema de coleta de dados próprio do MusicalSequence
  const collectorsHubRef = useRef(null);
  const [metricsSession, setMetricsSession] = useState({
    sessionId: null,
    isActive: false,
    insights: {}
  });

  // Função para enviar métricas usando o sistema próprio
  const sendMetrics = useCallback(async (metricsData) => {
    try {
      // Usar MusicalSequenceMetrics que já gerencia o hub de coletores
      MusicalSequenceMetrics.recordAdvancedInteraction({
        ...metricsData,
        sessionId: metricsSession.sessionId,
        timestamp: Date.now()
      });
      return true;
    } catch (error) {
      console.error('Erro ao enviar métricas:', error);
      return false;
    }
  }, [metricsSession.sessionId]);
  
  const getInsights = useCallback(async () => {
    try {
      const hub = MusicalSequenceMetrics.getCollectorsHub();
      if (hub) {
        return hub.getConsolidatedInsights?.() || {};
      }
      return {};
    } catch (error) {
      console.error('Erro ao obter insights:', error);
      return {};
    }
  }, []);

  // Propriedade de conveniência para manter compatibilidade
  const orchestratorReady = metricsSession.isActive;

  const instruments = [
    { id: 'piano', name: 'Piano', emoji: '🎹', color: '#FF6B6B', sound: 'piano' },
    { id: 'guitar', name: 'Violão', emoji: '🎸', color: '#4ECDC4', sound: 'guitar' },
    { id: 'drum', name: 'Bateria', emoji: '🥁', color: '#45B7D1', sound: 'drum' },
    { id: 'flute', name: 'Flauta', emoji: '🎵', color: '#96CEB4', sound: 'flute' },
    { id: 'violin', name: 'Violino', emoji: '🎻', color: '#A8E6CF', sound: 'violin' },
    { id: 'sax', name: 'Saxofone', emoji: '🎷', color: '#FFD93D', sound: 'sax' }
  ];

  const explainGame = useCallback(() => {
    speak('Jogo de Sequência Musical. Escute a sequência de instrumentos e reproduza-a na ordem correta. Desenvolva sua memória auditiva e percepção musical!', {
      rate: 0.8
    });
  }, [speak]);

  const getAccuracy = useCallback(() => {
    if (totalAttempts === 0) return 100;
    return Math.round((correctAttempts / totalAttempts) * 100);
  }, [totalAttempts, correctAttempts]);

  // Função para inicializar AudioContext
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current || audioContextRef.current.state === 'closed') {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }
  }, []);

  // Função para tocar um som específico
  const playSound = useCallback(async (note, duration = 500) => {
    if (!audioContextRef.current) {
      console.warn('AudioContext não iniciado. Iniciando automaticamente...');
      await initAudioContext();
    }

    try {
      const audioContext = audioContextRef.current;
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      const frequencies = {
        'C': 261.63, 'D': 293.66, 'E': 329.63, 'F': 349.23,
        'G': 392.00, 'A': 440.00, 'B': 493.88
      };
      
      oscillator.frequency.setValueAtTime(frequencies[note] || 440, audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
      
      return new Promise(resolve => {
        oscillator.onended = resolve;
      });
    } catch (error) {
      console.error('Erro ao tocar som:', error);
    }
  }, [initAudioContext]);

  // Função para tocar sequência com array
  const playSequenceWithArray = useCallback(async (sequenceArray) => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    console.log('🎵 Reproduzindo sequência:', sequenceArray);
    
    for (let i = 0; i < sequenceArray.length; i++) {
      const note = sequenceArray[i];
      setActiveInstrument(note);
      
      await playSound(note, 600);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      setActiveInstrument(null);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    setIsPlaying(false);
    setGameState('listening');
    
    // TTS automático após reproduzir a sequência
    if (ttsEnabled) {
      setTimeout(() => {
        speak(`Sequência reproduzida! Agora é sua vez de repetir ${sequenceArray.length} notas.`, {
          rate: 0.9,
          onEnd: () => console.log('Instrução pós-sequência anunciada')
        });
      }, 500);
    }
  }, [isPlaying, setActiveInstrument, playSound, setIsPlaying, setGameState, ttsEnabled, speak]);

  // ✅ DECLARAR generateNewSequence ANTES DOS useEffects QUE A UTILIZAM
  const generateNewSequence = useCallback(async () => {
    console.log('generateNewSequence called with difficulty:', difficulty);
    const difficultyData = MusicalSequenceConfig.difficulties.find(d => d.id === difficulty);
    console.log('difficultyData:', difficultyData);
    
    if (!difficultyData) {
      console.error('❌ Difficulty data not found for:', difficulty);
      console.log('Available difficulties:', MusicalSequenceConfig.difficulties);
      return;
    }
    
    const sequenceLength = Math.min(
      difficultyData.sequenceLength + Math.floor((currentLevel - 1) / 3),
      8
    );
    console.log('sequenceLength:', sequenceLength);
    
    const newSequence = [];
    for (let i = 0; i < sequenceLength; i++) {
      const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
      newSequence.push(randomInstrument.id);
    }    
    
    console.log('Generated sequence:', newSequence);
    setSequence(newSequence);
    setPlayerSequence([]);
    setGameState('waiting');
    setFeedback({ show: false, type: '', message: '' });
    
    // Coleta avançada de dados da nova sequência
    try {
      const sequenceGenerationData = {
        timestamp: Date.now(),
        sequenceLength: newSequence.length,
        difficulty,
        level: currentLevel,
        round: currentRound,
        sequence: newSequence,
        sessionDuration: Date.now() - gameStartTime,
        gameState: 'sequence_generated'
      };
      
      await collectorsHub.processInteraction(sequenceGenerationData);
      
      // Registrar com backend
      if (recordInteraction) {
        recordInteraction({
          type: 'sequence_generation',
          data: sequenceGenerationData
        });
      }
    } catch (error) {
      console.warn('⚠️ Erro ao coletar dados da sequência:', error);
    }
  }, [difficulty, currentLevel, currentRound, gameStartTime, collectorsHub, recordInteraction]);

  useEffect(() => {
    // Inicializar AudioContext apenas quando necessário
    
    instruments.forEach(instrument => {
      audioRef.current[instrument.id] = {
        play: () => {
          try {
            // Inicializar AudioContext se necessário
            initAudioContext();
            
            // Verificar se o contexto de áudio ainda está ativo
            if (audioContextRef.current.state === 'closed') {
              console.warn('AudioContext fechado, não é possível reproduzir som');
              return;
            }
            
            // Resumir contexto se estiver suspenso
            if (audioContextRef.current.state === 'suspended') {
              audioContextRef.current.resume();
            }
            
            const oscillator = audioContextRef.current.createOscillator();
            const gainNode = audioContextRef.current.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContextRef.current.destination);
            
            const frequencies = {
              piano: 523.25, // C5
              guitar: 329.63, // E4
              drum: 146.83,  // D3
              flute: 440.00, // A4
              violin: 659.25, // E5
              sax: 293.66    // D4
            };
            
            oscillator.frequency.setValueAtTime(frequencies[instrument.id] || 440, audioContextRef.current.currentTime);
            oscillator.type = instrument.id === 'drum' ? 'square' : 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContextRef.current.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + 0.5);
            
            oscillator.start(audioContextRef.current.currentTime);
            oscillator.stop(audioContextRef.current.currentTime + 0.5);
          } catch (error) {
            console.error('Error playing sound:', error);
          }
        }
      };
    });

    return () => {
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [initAudioContext]);  // Auto-start sequence when game begins
  // Auto-start sequence when game begins
  useEffect(() => {
    if (!showStartScreen && gameState === 'waiting' && difficulty && sequence.length === 0) {
      console.log('✅ Auto-starting sequence after game start...', {
        showStartScreen,
        gameState,
        difficulty,
        sequenceLength: sequence.length
      });
      setTimeout(() => {
        generateNewSequence();
      }, 1000);
    }
  }, [showStartScreen, gameState, difficulty, sequence, generateNewSequence]);

  // Separate useEffect to handle sequence auto-play when sequence is updated but not playing
  useEffect(() => {
    if (sequence.length > 0 && gameState === 'waiting' && !isPlaying) {
      console.log('✅ Auto-playing sequence after generation...');
      setTimeout(() => {
        playSequenceWithArray(sequence);
      }, 500);
    }
  }, [sequence, gameState, isPlaying, playSequenceWithArray]);

  // Função para tocar som de instrumento
  const playSoundInstrument = useCallback(async (instrumentId) => {
    try {
      if (audioRef.current[instrumentId]) {
        audioRef.current[instrumentId].play();
      }
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  }, []);

  // Função para tocar a sequência gerada
  const playSequence = useCallback(async () => {
    console.log('playSequence called with sequence:', sequence);
    setIsPlaying(true);
    setGameState('playing');
    
    // Anunciar início da sequência
    speak(`Agora ouça atentamente a sequência de ${sequence.length} instrumentos.`, {
      rate: 1.0
    });
    
    // Aguardar o TTS terminar antes de tocar a sequência
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    for (let i = 0; i < sequence.length; i++) {
      console.log(`Playing instrument ${i}:`, sequence[i]);
      setActiveInstrument(sequence[i]);
      await playSound(sequence[i]);
      await new Promise(resolve => setTimeout(resolve, 800));
      setActiveInstrument(null);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    console.log('Sequence finished, changing to listening mode');
    setIsPlaying(false);
    setGameState('listening');
    
    // Anunciar fim da sequência
    speak('Agora é sua vez! Toque os instrumentos na ordem correta.', {
      rate: 1.0
    });
  }, [sequence, speak, playSound, setIsPlaying, setGameState, setActiveInstrument]);

  const handleInstrumentClick = useCallback(async (instrumentId) => {
    console.log('handleInstrumentClick called:', { instrumentId, gameState, isPlaying, playerSequenceLength: playerSequence.length });
    
    if (gameState !== 'listening' || isPlaying) {
      console.log('Click ignorado - jogo não está em modo listening');
      return;
    }
    
    const nextIndex = playerSequence.length;
    const expectedInstrument = sequence[nextIndex];
    const startTime = Date.now();
    
    console.log('Expected:', expectedInstrument, 'Clicked:', instrumentId);
    
    setActiveInstrument(instrumentId);
    await playSound(instrumentId);
    
    setTimeout(() => setActiveInstrument(null), 300);
    
    const newPlayerSequence = [...playerSequence, instrumentId];
    setPlayerSequence(newPlayerSequence);
    setTotalAttempts(prev => prev + 1);
    
    const isCorrect = instrumentId === expectedInstrument;
    const responseTime = Date.now() - startTime;
    
    // Coleta avançada de dados da interação
    try {
      const interactionData = {
        type: 'instrument_click',
        action: 'sequence_attempt',
        instrumentClicked: instrumentId,
        expectedInstrument: expectedInstrument,
        isCorrect: isCorrect,
        sequencePosition: nextIndex,
        totalSequenceLength: sequence.length,
        currentSequence: sequence,
        playerSequence: newPlayerSequence,
        responseTime: responseTime,
        difficulty: difficulty,
        level: currentLevel,
        round: currentRound,
        streak: streak,
        gameState: gameState,
        timestamp: Date.now(),
        // Dados específicos para análise de memória auditiva
        memoryLoad: sequence.length,
        sequenceProgress: (nextIndex + 1) / sequence.length,
        // Dados para análise de padrões musicais
        instrumentFrequency: newPlayerSequence.filter(inst => inst === instrumentId).length,
        lastInstruments: newPlayerSequence.slice(-3),
        // Dados para análise de execução
        attemptNumber: totalAttempts + 1,
        sessionTime: Date.now() - gameStartTime
      };
      
      // Processar com coletores avançados
      MusicalSequenceMetrics.recordAdvancedInteraction(interactionData);
      
      // Enviar para orquestrador central se disponível
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: 'musical_sequence',
          sessionId: `musical_${gameStartTime}`,
          metrics: interactionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Erro na coleta avançada:', error);
    }
    
    if (!isCorrect) {
      console.log('Wrong instrument!');
      setStreak(0);
      setFeedback({
        show: true,
        type: 'error',
        message: 'Ops! Instrumento incorreto. Vamos tentar novamente!'
      });
      
      // Feedback sonoro TTS para erro
      speak('Ops! Instrumento incorreto. Vamos tentar novamente!', {
        rate: 0.9,
        pitch: 0.8
      });
      
      setTimeout(() => {
        setFeedback({ show: false, type: '', message: '' });
        setPlayerSequence([]);
        playSequence();
      }, 2000);
      return;
    }
    
    // Correct instrument
    console.log('Correct instrument!');
    setCorrectAttempts(prev => prev + 1);
      // Check if sequence is complete
    if (newPlayerSequence.length === sequence.length) {
    console.log('Sequence completed!');
    const roundScore = sequence.length * 10;
    setScore(prev => prev + roundScore);
    const newStreak = streak + 1;
    setStreak(newStreak);
    
    // Coleta avançada de dados da sequência completa
    try {
      const sequenceCompletionData = {
        type: 'sequence_completion',
        action: 'sequence_completed',
        targetSequence: sequence,
        playerSequence: newPlayerSequence,
        isCorrect: true,
        completionTime: Date.now() - gameStartTime,
        difficulty: difficulty,
        level: currentLevel,
        round: currentRound,
        score: score + roundScore,
        newStreak: newStreak,
        totalAttempts: totalAttempts + 1,
        correctAttempts: correctAttempts + 1,
        // Dados específicos para análise de aprendizado
        sequenceLength: sequence.length,
        accuracy: ((correctAttempts + 1) / (totalAttempts + 1)) * 100,
        improvementIndicator: newStreak > streak,
        timestamp: Date.now()
      };
      
      MusicalSequenceMetrics.recordAdvancedInteraction(sequenceCompletionData);
      
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: 'musical_sequence',
          sessionId: `musical_${gameStartTime}`,
          metrics: sequenceCompletionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Erro na coleta de dados de sequência completa:', error);
    }
    
    setFeedback({
      show: true,
      type: 'success',
      message: '🎉 Excelente! Sequência correta!'
    });
    
    // Feedback sonoro TTS para sucesso
    speak('Excelente! Sequência correta! Parabéns!', {
      rate: 1.0,
      pitch: 1.2
    });
    
    setTimeout(() => {
      setFeedback({ show: false, type: '', message: '' });

      if (newStreak % 3 === 0) {
        setCurrentLevel(prev => prev + 1);
      }      setPlayerSequence([]);
      generateNewSequence();
    }, 2000);
    }
  }, [gameState, isPlaying, playerSequence, sequence, totalAttempts, setActiveInstrument, playSound, setPlayerSequence, setTotalAttempts, difficulty, currentLevel, currentRound, streak, gameStartTime, correctAttempts, score, setStreak, setFeedback, speak, setCorrectAttempts, setScore, setCurrentLevel, generateNewSequence, orchestratorReady, sendMetrics]);

  const handleTTSClick = useCallback(() => {
    speak("Sequência Musical. Escute a sequência de instrumentos musicais e repita tocando na mesma ordem. Use os botões da sequência para reproduzir a ordem que você ouviu.");
  }, [speak]);

  const handleBackToMenu = useCallback(() => {
    // Gerar relatório final e recomendações
    try {
      const gameEndData = {
        type: 'game_session',
        action: 'session_end',
        sessionDuration: Date.now() - gameStartTime,
        totalScore: score,
        finalLevel: currentLevel,
        finalRound: currentRound,
        totalAttempts: totalAttempts,
        correctAttempts: correctAttempts,
        finalAccuracy: totalAttempts > 0 ? (correctAttempts / totalAttempts) * 100 : 0,
        maxStreak: streak,
        difficulty: difficulty,
        timestamp: Date.now()
      };
      
      MusicalSequenceMetrics.recordAdvancedInteraction(gameEndData);
      
      // Gerar relatório final dos coletores
      const collectorsHub = MusicalSequenceMetrics.getCollectorsHub();
      if (collectorsHub) {
        const finalReport = collectorsHub.generateDetailedReport();
        const recommendations = collectorsHub.generateRecommendations();
        
        console.log('🎵 Relatório Final da Sessão:', finalReport);
        console.log('🎯 Recomendações Geradas:', recommendations);
        
        // Enviar relatório final para o orquestrador
        if (orchestratorReady && sendMetrics) {
          sendMetrics({
            gameType: 'musical_sequence',
            sessionId: `musical_${gameStartTime}`,
            metrics: {
              ...gameEndData,
              finalReport: finalReport,
              recommendations: recommendations
            },
            timestamp: Date.now()
          });
        }
        
        // Finalizar sessão dos coletores
        collectorsHub.endSession();
      }
    } catch (error) {
      console.error('Erro ao finalizar sessão:', error);
    }
    
    setShowStartScreen(true);
    onBack();
  }, [gameStartTime, score, currentLevel, currentRound, totalAttempts, correctAttempts, streak, difficulty, orchestratorReady, sendMetrics, onBack]);

  return (
    <div 
      className={`${styles.musicalSequenceGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="Sequência Musical"
          gameSubtitle="Desenvolva sua memória auditiva e percepção musical"
          gameInstruction="Escute a sequência de instrumentos musicais e reproduza-a na ordem correta. Treine sua memória auditiva!"
          gameIcon="🎵"
          difficulties={[
            {
              id: 'easy',
              name: 'Fácil',
              description: 'Sequências de 3 instrumentos - Ideal para iniciantes',
              icon: '😊',
              preview: (
                <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
                  <div style={{ fontSize: '20px' }}>🎹</div>
                  <div style={{ fontSize: '20px' }}>🎸</div>
                  <div style={{ fontSize: '20px' }}>🥁</div>
                </div>
              )
            },
            {
              id: 'medium',
              name: 'Médio',
              description: 'Sequências de 4 instrumentos - Desafio equilibrado',
              icon: '🎯',
              preview: (
                <div style={{ display: 'flex', gap: '3px', justifyContent: 'center' }}>
                  <div style={{ fontSize: '16px' }}>🎹</div>
                  <div style={{ fontSize: '16px' }}>🎸</div>
                  <div style={{ fontSize: '16px' }}>🥁</div>
                  <div style={{ fontSize: '16px' }}>🎵</div>
                </div>
              )
            },
            {
              id: 'hard',
              name: 'Avançado',
              description: 'Sequências de 5 instrumentos - Para especialistas',
              icon: '🚀',
              preview: (
                <div style={{ display: 'flex', gap: '2px', justifyContent: 'center' }}>
                  <div style={{ fontSize: '14px' }}>🎹</div>
                  <div style={{ fontSize: '14px' }}>🎸</div>
                  <div style={{ fontSize: '14px' }}>🥁</div>
                  <div style={{ fontSize: '14px' }}>🎵</div>
                  <div style={{ fontSize: '14px' }}>🎻</div>
                </div>
              )
            }
          ]}
          customContent={
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '16px',
              padding: '1.5rem',
              marginBottom: '1rem'
            }}>
              <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
                🎼 Este jogo desenvolve:
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
                gap: '1rem'
              }}>
                <div style={{ textAlign: 'center', color: 'white' }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>👂</div>
                  <div>Memória Auditiva</div>
                </div>
                <div style={{ textAlign: 'center', color: 'white' }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎶</div>
                  <div>Percepção Musical</div>
                </div>
                <div style={{ textAlign: 'center', color: 'white' }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧠</div>
                  <div>Concentração</div>
                </div>
                <div style={{ textAlign: 'center', color: 'white' }}>
                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎯</div>
                  <div>Sequenciamento</div>
                </div>
              </div>
            </div>
          }
          onStart={(difficulty) => startGame(difficulty)}
          onBack={onBack}
        />
      ) : (
        <div className={styles.gameContent}>
          <div className={styles.gameHeader}>
            <h1 className={styles.gameTitle}>
              🎵 Sequência Musical
              <div className={styles.activitySubtitle}>
                Nível {currentLevel} - Memorize e repita a sequência
              </div>
            </h1>
            <button 
              className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
              onClick={toggleTTS}
              title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
              aria-label={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            >
              {ttsActive ? '🔊' : '🔇'}
            </button>
          </div>

          {/* 🎯 SISTEMA DE ATIVIDADES - PADRÃO MEMORYGAME EXATO */}
          {gameState.status === 'playing' && (
            <div className={styles.activityMenu}>
              {Object.values(ACTIVITY_TYPES).map((activity) => {
                const isCurrentActivity = gameState.currentActivity === activity.id;
                const canSwitch = gameState.activityRoundCount >= 4 || isCurrentActivity;

                return (
                  <button
                    key={activity.id}
                    className={`${styles.activityButton} ${
                      isCurrentActivity ? styles.active : ''
                    }`}
                    onClick={() => changeActivity(activity.id)}
                    title={
                      !canSwitch
                        ? `Complete ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro`
                        : activity.description
                    }
                    disabled={!canSwitch}
                  >
                    <span className={styles.activityIcon}>{activity.icon}</span>
                    <span className={styles.activityName}>{activity.name}</span>
                    {isCurrentActivity && (
                      <span className={styles.activeIndicator}>●</span>
                    )}
                  </button>
                );
              })}
            </div>
          )}

          <div className={styles.gameStats}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.round}</div>
              <div className={styles.statLabel}>Rodada</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.score}</div>
              <div className={styles.statLabel}>Pontos</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.accuracy}%</div>
              <div className={styles.statLabel}>Precisão</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.activityRoundCount}/4+</div>
              <div className={styles.statLabel}>Progresso</div>
            </div>
          </div>

          <div className={styles.instructions}>
            <p className={styles.instructionsText}>
              {gameState === 'playing' 
                ? '🎵 Escute a sequência musical...' 
                : gameState === 'listening'
                  ? '🎯 Sua vez! Toque os instrumentos na ordem correta'
                  : 'Aguarde...'}
            </p>
          </div>          {sequence.length > 0 && (
            <div className={styles.sequenceProgress}>
              <h3 className={styles.progressTitle}>
                Sequência ({sequence.length} instrumentos):
              </h3>
              <div className={styles.progressGrid}>
                {sequence.map((instrumentId, index) => {
                  const instrument = instruments.find(i => i.id === instrumentId);
                  const isCompleted = index < playerSequence.length;
                  const isActive = activeInstrument === instrumentId;
                  
                  return (
                    <div
                      key={index}
                      className={`${styles.progressSlot} ${
                        isCompleted ? styles.filled : styles.empty
                      } ${isActive ? styles.active : ''}`}
                      style={{ 
                        backgroundColor: isCompleted ? instrument?.color : 'rgba(255, 255, 255, 0.1)',
                        borderColor: instrument?.color
                      }}
                    >
                      <span className={styles.slotNumber}>{index + 1}</span>
                      {instrument?.emoji}
                    </div>
                  );
                })}
              </div>
            </div>
          )}          <div className={styles.instrumentsSection}>
            <h3 className={styles.instrumentsTitle}>Escolha os instrumentos na ordem correta:</h3>
            <div className={styles.instrumentsGrid}>
              {instruments.map(instrument => (
                <button
                  key={instrument.id}
                  className={`${styles.instrumentButton} ${
                    activeInstrument === instrument.id ? styles.playing : ''
                  } ${gameState !== 'listening' || isPlaying ? styles.disabled : ''}`}
                  style={{ 
                    backgroundColor: instrument.color,
                    borderColor: instrument.color 
                  }}
                  onClick={() => handleInstrumentClick(instrument.id)}
                  disabled={gameState !== 'listening' || isPlaying}
                  aria-label={`Tocar ${instrument.name}`}
                >
                  <div className={styles.instrumentEmoji}>{instrument.emoji}</div>
                  <div className={styles.instrumentName}>{instrument.name}</div>
                </button>
              ))}
            </div>
          </div>

          <div className={styles.gameControls}>            <button 
              className={styles.controlButton}
              onClick={() => playSequence()}
              disabled={isPlaying || gameState === 'playing' || sequence.length === 0}
              aria-label="Repetir sequência"
            >
              🔄 Repetir Sequência
            </button>
            
            <button 
              className={styles.controlButton}
              onClick={generateNewSequence}
              disabled={isPlaying || gameState === 'playing'}
              aria-label="Nova sequência"
            >
              ➡️ Nova Sequência
            </button>
            
            <button 
              className={`${styles.controlButton} ${styles.backButton}`}
              onClick={handleBackToMenu}
              aria-label="Voltar ao menu inicial"
            >
              🏠 Menu Inicial
            </button>
          </div>

          {feedback.show && (
            <div className={`${styles.feedbackMessage} ${styles[feedback.type]}`}>
              {feedback.message}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MusicalSequenceGame;
