const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BkN74ywZ.js","assets/react-BQG6_13O.js","assets/react-router-BtSsPy6x.js","assets/react-query-CDommIwN.js","assets/helmet-CSX2cyrn.js","assets/framer-motion-DA-GaQt2.js","assets/prop-types-D_3gT01v.js","assets/index-DnDa0SVZ.css","assets/RealTimeDashboard-CXIudvdW.js","assets/RealTimeDashboard-ByWC3U45.css"])))=>i.map(i=>d[i]);
import{j as e,P as o,T as a,G as r,_ as s,A as n,a as t,b as i,u as d}from"./index-BkN74ywZ.js";import{r as c}from"./react-router-BtSsPy6x.js";import{C as l,a as m,b as u,c as b,d as p,e as v,p as h,f as N,g,A as j,h as f,i as x}from"./chart-core-CRFNBRsI.js";import{L as D,B as A,D as C,R as P,P as _}from"./chart-react-DNlZ3-Au.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const I="_dashboardContainer_dkjsz_15",y="_dashboardTabs_dkjsz_213",E="_dashboardContent_dkjsz_255",R="_dashboardTab_dkjsz_213",M="_active_dkjsz_321",V="_dashboardWrapper_dkjsz_383",S={spinnerContainer:"_spinnerContainer_1ybli_15",fullscreenOverlay:"_fullscreenOverlay_1ybli_33",spinner:"_spinner_1ybli_15",spin:"_spin_1ybli_15",small:"_small_1ybli_79",medium:"_medium_1ybli_91",large:"_large_1ybli_103",xlarge:"_xlarge_1ybli_115",message:"_message_1ybli_129",primary:"_primary_1ybli_155",success:"_success_1ybli_163",warning:"_warning_1ybli_171",error:"_error_1ybli_179",highContrast:"_highContrast_1ybli_239",reducedMotion:"_reducedMotion_1ybli_261",inline:"_inline_1ybli_275"},T=({size:o="medium",message:a="Carregando...",variant:r="primary",fullscreen:s=!1,inline:n=!1,showMessage:t=!0,className:i=""})=>{const d=[S.spinner,S[o],S[r]].filter(Boolean).join(" "),c=[n?S.inline:S.spinnerContainer,i].filter(Boolean).join(" "),l=[S.message,S[o]].filter(Boolean).join(" "),m=e.jsxDEV("div",{className:c,children:[e.jsxDEV("div",{className:d,role:"progressbar","aria-label":a,"aria-busy":"true"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/LoadingSpinner/LoadingSpinner.jsx",lineNumber:38,columnNumber:7},void 0),t&&a&&e.jsxDEV("p",{className:l,role:"status","aria-live":"polite",children:a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/LoadingSpinner/LoadingSpinner.jsx",lineNumber:45,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/LoadingSpinner/LoadingSpinner.jsx",lineNumber:37,columnNumber:5},void 0);return s?e.jsxDEV("div",{className:S.fullscreenOverlay,children:m},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/LoadingSpinner/LoadingSpinner.jsx",lineNumber:54,columnNumber:7},void 0):m};T.propTypes={size:o.oneOf(["small","medium","large","xlarge"]),message:o.string,variant:o.oneOf(["primary","success","warning","error"]),fullscreen:o.bool,inline:o.bool,showMessage:o.bool,className:o.string};"undefined"!=typeof window&&window.document;const w={info:(...e)=>{},error:(...e)=>{},warn:(...e)=>{}};class k{constructor(){this.activeSessions=new Map,this.completedSessions=new Map,this.sessionCounter=0,this.isInitialized=!1,w.info("📋 GameSessionManager criado")}async initialize(){try{return this.isInitialized=!0,w.info("✅ GameSessionManager inicializado"),!0}catch(e){throw w.error("❌ Erro ao inicializar GameSessionManager:",e),e}}async createSession(e){try{const o=this.generateSessionId(e),a={id:o,userId:e.userId,gameId:e.gameId,startTime:new Date,status:"active",metadata:this.extractMetadata(e),gameData:e};return this.activeSessions.set(o,a),this.sessionCounter++,w.info("🆕 Nova sessão criada:",{sessionId:a.id,gameId:a.gameId,userId:a.userId}),a}catch(o){throw w.error("❌ Erro ao criar sessão:",o),o}}async finalizeSession(e){try{const o=this.activeSessions.get(e);if(!o)throw new Error(`Sessão ${e} não encontrada`);return o.endTime=new Date,o.duration=o.endTime-o.startTime,o.status="completed",this.completedSessions.set(e,o),this.activeSessions.delete(e),w.info("✅ Sessão finalizada:",{sessionId:o.id,duration:o.duration,status:o.status}),o}catch(o){throw w.error("❌ Erro ao finalizar sessão:",o),o}}generateSessionId(e){const o=Date.now();return`${e.gameId||"unknown"}_${e.userId?e.userId.substring(0,8):"anon"}_${o}`}extractMetadata(e){return{difficulty:e.difficulty||"medium",attempts:e.attempts?e.attempts.length:0,accuracy:e.accuracy||0,averageResponseTime:e.averageResponseTime||0,sessionDuration:e.sessionDuration||0,platform:this.detectPlatform(),timestamp:(new Date).toISOString()}}detectPlatform(){return"undefined"!=typeof window?"browser":"undefined"!=typeof process?"node":"unknown"}getActiveSession(e){return this.activeSessions.get(e)||null}getCompletedSession(e){return this.completedSessions.get(e)||null}getActiveSessions(){return Array.from(this.activeSessions.values())}getCompletedSessions(){return Array.from(this.completedSessions.values())}getActiveSessionsCount(){return this.activeSessions.size}getProcessedSessionsCount(){return this.completedSessions.size}cleanupOldSessions(e=36e5){const o=Date.now();let a=0;for(const[r,s]of this.activeSessions.entries())o-s.startTime.getTime()>e&&(this.activeSessions.delete(r),a++);for(const[r,s]of this.completedSessions.entries())o-s.endTime.getTime()>e&&(this.completedSessions.delete(r),a++);return a>0&&w.info(`🧹 Limpeza: ${a} sessões antigas removidas`),a}getStats(){return{activeSessions:this.activeSessions.size,completedSessions:this.completedSessions.size,totalSessions:this.sessionCounter,initialized:this.isInitialized}}async isHealthy(){try{return this.isInitialized&&this.activeSessions instanceof Map&&this.completedSessions instanceof Map}catch(e){return w.error("❌ Erro no health check do GameSessionManager:",e),!1}}async forceCloseAllSessions(){const e=Array.from(this.activeSessions.keys()),o=[];for(const r of e)try{const e=await this.finalizeSession(r);o.push({sessionId:r,success:!0,session:e})}catch(a){o.push({sessionId:r,success:!1,error:a.message})}return w.info(`🔒 Forçado fechamento de ${e.length} sessões`),o}}"undefined"!=typeof window&&window.document;const B={info:(...e)=>{},error:(...e)=>{},warn:(...e)=>{},debug:(...e)=>{}};class z{constructor(){this.isInitialized=!1,this.metricsBuffer=new Map,this.aggregationRules=new Map,this.healthMetrics={totalAggregations:0,successfulAggregations:0,failedAggregations:0,averageAggregationTime:0,lastAggregationTime:null},this.initializeAggregationRules(),B.info("📊 MetricsAggregator criado")}initializeAggregationRules(){this.aggregationRules.set("cognitive",{averageFields:["attentionScore","memoryScore","processingSpeedScore"],sumFields:["totalAttempts","correctAnswers","incorrectAnswers"],maxFields:["maxSequenceLength","maxDifficulty"],customRules:{accuracy:e=>{const o=e.reduce((e,o)=>e+(o.totalAttempts||0),0),a=e.reduce((e,o)=>e+(o.correctAnswers||0),0);return o>0?a/o*100:0}}}),this.aggregationRules.set("behavioral",{averageFields:["engagementLevel","frustrationLevel","motivationLevel"],sumFields:["totalInteractions","totalPauses","totalErrors"],maxFields:["longestSession","maxEngagement"],customRules:{behaviorTrend:e=>{if(e.length<2)return"stable";const o=e[0],a=e[e.length-1].engagementLevel-o.engagementLevel;return a>5?"improving":a<-5?"declining":"stable"}}}),this.aggregationRules.set("performance",{averageFields:["responseTime","accuracy","completionRate"],sumFields:["totalTime","totalActions","totalSessions"],minFields:["minResponseTime","bestAccuracy"],maxFields:["maxResponseTime","longestSession"],customRules:{performanceIndex:e=>{const o=e.reduce((e,o)=>e+(o.accuracy||0),0)/e.length,a=e.reduce((e,o)=>e+(o.responseTime||0),0)/e.length,r=e.reduce((e,o)=>e+(o.completionRate||0),0)/e.length;return(Math.min(o,100)+Math.max(0,100-Math.min(a/10,100))+Math.min(r,100))/3}}}),this.aggregationRules.set("therapeutic",{averageFields:["adaptationScore","progressScore","therapeuticValue"],sumFields:["totalTherapeuticSessions","totalProgressPoints"],customRules:{therapeuticEffectiveness:e=>(this.calculateProgressTrend(e)+e.reduce((e,o)=>e+(o.adaptationScore||0),0)/e.length+e.reduce((e,o)=>e+(o.therapeuticValue||0),0)/e.length)/3}}),B.info("📊 Regras de agregação inicializadas")}async aggregateSessionMetrics(e){const o=Date.now();try{B.info("📊 Agregando métricas da sessão:",e.sessionId);const a={sessionId:e.sessionId,gameType:e.gameType,userId:e.userId,timestamp:(new Date).toISOString(),aggregationType:"session",metrics:{}};for(const[o,s]of Object.entries(e.metrics||{}))this.aggregationRules.has(o)&&(a.metrics[o]=await this.aggregateMetricsByCategory(o,s,e));a.metrics.general=this.calculateGeneralMetrics(e),this.healthMetrics.totalAggregations++,this.healthMetrics.successfulAggregations++,this.healthMetrics.lastAggregationTime=(new Date).toISOString();const r=Date.now()-o;return this.healthMetrics.averageAggregationTime=(this.healthMetrics.averageAggregationTime*(this.healthMetrics.totalAggregations-1)+r)/this.healthMetrics.totalAggregations,B.info("📊 Métricas agregadas com sucesso em",r,"ms"),a}catch(a){throw this.healthMetrics.failedAggregations++,B.error("📊 Erro ao agregar métricas:",a.message),a}}async aggregateMetricsByCategory(e,o,a){const r=this.aggregationRules.get(e);if(!r)return o;const s=Array.isArray(o)?o:[o],n={};for(const i of r.averageFields||[]){const e=s.map(e=>e[i]).filter(e=>null!=e);e.length>0&&(n[i]=e.reduce((e,o)=>e+o,0)/e.length)}for(const i of r.sumFields||[]){const e=s.map(e=>e[i]).filter(e=>null!=e);e.length>0&&(n[i]=e.reduce((e,o)=>e+o,0))}for(const i of r.maxFields||[]){const e=s.map(e=>e[i]).filter(e=>null!=e);e.length>0&&(n[i]=Math.max(...e))}for(const i of r.minFields||[]){const e=s.map(e=>e[i]).filter(e=>null!=e);e.length>0&&(n[i]=Math.min(...e))}if(r.customRules)for(const[i,d]of Object.entries(r.customRules))try{n[i]=await d(s)}catch(t){B.warn("📊 Erro ao aplicar regra customizada",i,":",t.message)}return n}calculateGeneralMetrics(e){const o=new Date(e.startTime||Date.now());return{sessionDuration:new Date(e.endTime||Date.now())-o,gameType:e.gameType,difficulty:e.difficulty,completed:e.completed||!1,totalInteractions:e.totalInteractions||0,averageResponseTime:e.averageResponseTime||0,errorRate:e.errorRate||0,completionRate:e.completionRate||0,engagementScore:e.engagementScore||0}}calculateProgressTrend(e){if(e.length<2)return 50;const o=e.map(e=>e.progressScore||0),a=o.reduce((e,a,r)=>0===r?e:e+(a-o[r-1]),0)/(o.length-1);return Math.max(0,Math.min(100,50+a))}async aggregateMultipleSessionMetrics(e){try{B.info("📊 Agregando métricas de",e.length,"sessões");const o={userId:e[0]?.userId,aggregationType:"multi-session",sessionsCount:e.length,timeRange:{start:Math.min(...e.map(e=>new Date(e.startTime).getTime())),end:Math.max(...e.map(e=>new Date(e.endTime||Date.now()).getTime()))},metrics:{}},a={};for(const r of e)for(const[e,o]of Object.entries(r.metrics||{}))a[e]||(a[e]=[]),a[e].push(o);for(const[r,s]of Object.entries(a))o.metrics[r]=await this.aggregateMetricsByCategory(r,s.flat(),{sessions:e});return o.metrics.general=this.calculateMultiSessionGeneralMetrics(e),B.info("📊 Métricas multi-sessão agregadas com sucesso"),o}catch(o){throw B.error("📊 Erro ao agregar métricas multi-sessão:",o.message),o}}async aggregateMultipleSessions(e){try{B.info("📊 Agregando métricas de múltiplas sessões...",{sessionCount:e.length});const o={totalSessions:e.length,gameTypes:[],overallMetrics:{},gameSpecificMetrics:{},trends:{},summary:{}},a=[...new Set(e.map(e=>e.gameType))];return o.gameTypes=a,a.forEach(e=>{o.gameSpecificMetrics[e]={sessions:0,totalScore:0,averageScore:0,metrics:{}}}),e.forEach(e=>{const a=e.gameType;if(o.gameSpecificMetrics[a].sessions++,e.processing&&e.processing.specificAnalysis){const r=e.processing.specificAnalysis;Object.keys(r).forEach(e=>{"number"==typeof r[e]&&(o.gameSpecificMetrics[a].metrics[e]||(o.gameSpecificMetrics[a].metrics[e]={total:0,count:0,average:0,values:[]}),o.gameSpecificMetrics[a].metrics[e].total+=r[e],o.gameSpecificMetrics[a].metrics[e].count++,o.gameSpecificMetrics[a].metrics[e].values.push(r[e]))})}e.cognitive&&e.cognitive.metrics&&this.aggregateCognitiveMetrics(o,e.cognitive.metrics),e.behavioral&&e.behavioral.metrics&&this.aggregateBehavioralMetrics(o,e.behavioral.metrics),e.therapeutic&&e.therapeutic.metrics&&this.aggregateTherapeuticMetrics(o,e.therapeutic.metrics)}),a.forEach(e=>{const a=o.gameSpecificMetrics[e];Object.keys(a.metrics).forEach(e=>{const o=a.metrics[e];o.count>0&&(o.average=o.total/o.count)})}),o.overallMetrics=this.calculateOverallMetrics(o),o.trends=this.calculateTrends(e),o.summary=this.generateAggregationSummary(o),B.info("📊 Agregação de múltiplas sessões concluída",{totalSessions:o.totalSessions,gameTypes:o.gameTypes.length,metricsCount:Object.keys(o.overallMetrics).length}),o}catch(o){throw B.error("📊 Erro ao agregar múltiplas sessões:",o),o}}aggregateCognitiveMetrics(e,o){e.overallMetrics.cognitive||(e.overallMetrics.cognitive={attentionSpan:{total:0,count:0,values:[]},memoryPerformance:{total:0,count:0,values:[]},processingSpeed:{total:0,count:0,values:[]},visualPerception:{total:0,count:0,values:[]},motorSkills:{total:0,count:0,values:[]},logicalReasoning:{total:0,count:0,values:[]}}),Object.keys(o).forEach(a=>{e.overallMetrics.cognitive[a]&&"number"==typeof o[a]&&(e.overallMetrics.cognitive[a].total+=o[a],e.overallMetrics.cognitive[a].count++,e.overallMetrics.cognitive[a].values.push(o[a]))})}aggregateBehavioralMetrics(e,o){e.overallMetrics.behavioral||(e.overallMetrics.behavioral={engagementLevel:{total:0,count:0,values:[]},persistenceLevel:{total:0,count:0,values:[]},frustrationLevel:{total:0,count:0,values:[]},motivationLevel:{total:0,count:0,values:[]}}),Object.keys(o).forEach(a=>{e.overallMetrics.behavioral[a]&&"number"==typeof o[a]&&(e.overallMetrics.behavioral[a].total+=o[a],e.overallMetrics.behavioral[a].count++,e.overallMetrics.behavioral[a].values.push(o[a]))})}aggregateTherapeuticMetrics(e,o){e.overallMetrics.therapeutic||(e.overallMetrics.therapeutic={progressIndicator:{total:0,count:0,values:[]},difficultyAdaptation:{total:0,count:0,values:[]},therapeuticValue:{total:0,count:0,values:[]}}),Object.keys(o).forEach(a=>{e.overallMetrics.therapeutic[a]&&"number"==typeof o[a]&&(e.overallMetrics.therapeutic[a].total+=o[a],e.overallMetrics.therapeutic[a].count++,e.overallMetrics.therapeutic[a].values.push(o[a]))})}calculateOverallMetrics(e){const o={...e.overallMetrics};return o.cognitive&&Object.keys(o.cognitive).forEach(e=>{const a=o.cognitive[e];a.count>0&&(a.average=a.total/a.count,a.min=Math.min(...a.values),a.max=Math.max(...a.values))}),o.behavioral&&Object.keys(o.behavioral).forEach(e=>{const a=o.behavioral[e];a.count>0&&(a.average=a.total/a.count,a.min=Math.min(...a.values),a.max=Math.max(...a.values))}),o.therapeutic&&Object.keys(o.therapeutic).forEach(e=>{const a=o.therapeutic[e];a.count>0&&(a.average=a.total/a.count,a.min=Math.min(...a.values),a.max=Math.max(...a.values))}),o}calculateTrends(e){try{const o={overallImprovement:"stable",gameSpecificTrends:{},progressRate:0},a=e.sort((e,o)=>(e.processing?.timestamp||e.timestamp||0)-(o.processing?.timestamp||o.timestamp||0));if(a.length>=3){const e=a.slice(0,Math.floor(a.length/3)),r=a.slice(-Math.floor(a.length/3)),s=e.filter(e=>e.processing?.success).length/e.length,n=r.filter(e=>e.processing?.success).length/r.length-s;n>.1?o.overallImprovement="improving":n<-.1&&(o.overallImprovement="declining"),o.progressRate=n}return o}catch(o){return B.error("📊 Erro ao calcular tendências:",o),{overallImprovement:"unknown",gameSpecificTrends:{},progressRate:0}}}generateAggregationSummary(e){return{totalSessions:e.totalSessions,uniqueGames:e.gameTypes.length,mostPlayedGame:this.findMostPlayedGame(e),overallSuccessRate:this.calculateOverallSuccessRate(e),aggregationTimestamp:(new Date).toISOString()}}findMostPlayedGame(e){let o=null,a=0;return Object.keys(e.gameSpecificMetrics).forEach(r=>{const s=e.gameSpecificMetrics[r].sessions;s>a&&(a=s,o=r)}),{gameType:o,sessions:a}}calculateOverallSuccessRate(e){let o=0,a=0;return Object.values(e.gameSpecificMetrics).forEach(e=>{a+=e.sessions,o+=e.sessions}),a>0?o/a:0}getHealthStatus(){const e=this.healthMetrics.totalAggregations>0?this.healthMetrics.successfulAggregations/this.healthMetrics.totalAggregations*100:100;return{component:"MetricsAggregator",status:e>=95?"healthy":e>=80?"degraded":"unhealthy",metrics:{...this.healthMetrics,successRate:e,bufferSize:this.metricsBuffer.size},timestamp:(new Date).toISOString()}}clearBuffer(){this.metricsBuffer.clear(),B.info("📊 Buffer de métricas limpo")}async initialize(){try{return B.info("📊 Inicializando MetricsAggregator..."),this.aggregationRules&&0!==this.aggregationRules.size||this.initializeAggregationRules(),this.isInitialized=!0,B.info("📊 MetricsAggregator inicializado com sucesso"),!0}catch(e){return B.error("📊 Erro ao inicializar MetricsAggregator:",e.message),this.isInitialized=!1,!1}}}const O={acquisition:{accuracyCriterion:.8,consistencyCriterion:3,independenceCriterion:.9,maintenancePeriod:604800},mastery:{accuracyCriterion:.9,consistencyCriterion:5,independenceCriterion:.95,generalizationCriterion:.8,maintenancePeriod:2592e3}};class G{constructor(){this.optimizationHistory=[],this.performanceThresholds=O,this.adaptationRules=this.initializeAdaptationRules()}optimizeInterventionParameters(e,o,a){const r={timestamp:(new Date).toISOString(),currentParameters:a,optimizedParameters:{...a},changes:[],rationale:[],confidence:0,expectedImprovement:0},s=this.analyzeCurrentPerformance(e,o);return this.identifyOptimizationOpportunities(s,a).forEach(e=>{const n=this.calculateOptimalValue(e,s,o);this.shouldApplyOptimization(e,n,a)&&(r.optimizedParameters[e.parameter]=n,r.changes.push({parameter:e.parameter,from:a[e.parameter],to:n,reason:e.reason,expectedImprovement:e.expectedImprovement}),r.rationale.push(e.rationale))}),r.confidence=this.calculateOptimizationConfidence(r.changes,o),r.expectedImprovement=this.estimateImprovement(r.changes,s),this.optimizationHistory.push(r),r}analyzeCurrentPerformance(e,o){const a=o.sessions.slice(-5);return{accuracy:{current:e.accuracy,trend:this.calculateTrend(a.map(e=>e.accuracy)),stability:this.calculateStability(a.map(e=>e.accuracy))},responseTime:{current:e.averageResponseTime,trend:this.calculateTrend(a.map(e=>e.averageResponseTime)),distribution:this.analyzeResponseTimeDistribution(e.responseTimeData)},engagement:{current:e.engagement,trend:this.calculateTrend(a.map(e=>e.engagement)),patterns:this.analyzeEngagementPatterns(e.engagementTimeline)},errors:{rate:e.errorRate,patterns:this.analyzeErrorPatterns(e.errors),types:this.categorizeErrors(e.errors)},motivation:{level:e.motivation,indicators:this.extractMotivationIndicators(e),sustainability:this.assessMotivationSustainability(a)}}}identifyOptimizationOpportunities(e,o){const a=[],r=this.assessDifficultyOptimization(e,o);r&&a.push(r);const s=this.assessTimingOptimization(e,o);s&&a.push(s);const n=this.assessReinforcementOptimization(e,o);n&&a.push(n);const t=this.assessPromptOptimization(e,o);t&&a.push(t);const i=this.assessModalityOptimization(e,o);return i&&a.push(i),a.sort((e,o)=>o.expectedImprovement-e.expectedImprovement)}assessDifficultyOptimization(e,o){o.difficulty;const a=e.accuracy.current,r=e.engagement.current,s=e.responseTime.current;return a>.9&&s<2e3&&r>.8?{parameter:"difficulty",direction:"increase",magnitude:this.calculateDifficultyAdjustment("increase",e),reason:"Task too easy - high accuracy with fast responses",rationale:"Increasing difficulty to maintain optimal challenge level",expectedImprovement:.15,confidence:.85}:a<.6||r<.5?{parameter:"difficulty",direction:"decrease",magnitude:this.calculateDifficultyAdjustment("decrease",e),reason:"Task too difficult - low accuracy or engagement",rationale:"Decreasing difficulty to rebuild confidence and engagement",expectedImprovement:.2,confidence:.9}:null}assessTimingOptimization(e,o){const a=o.timeLimit,r=e.responseTime,s=e.accuracy.current;return r.distribution.percentile95>.9*a&&s<.7?{parameter:"timeLimit",direction:"increase",magnitude:Math.ceil(1.3*a),reason:"Time pressure reducing accuracy",rationale:"Extending time limit to reduce pressure and improve performance",expectedImprovement:.12,confidence:.8}:r.distribution.percentile75<.5*a&&s>.85?{parameter:"timeLimit",direction:"decrease",magnitude:Math.ceil(.8*a),reason:"Excessive time allowance - opportunity for efficiency",rationale:"Reducing time limit to encourage quicker processing",expectedImprovement:.08,confidence:.7}:null}assessReinforcementOptimization(e,o){const a=o.reinforcementSchedule,r=e.motivation,s=e.engagement;return r.level<.6||s.trend<-.1?{parameter:"reinforcementSchedule",direction:"increase_frequency",magnitude:this.calculateReinforcementAdjustment("increase",a),reason:"Declining motivation requires more frequent reinforcement",rationale:"Increasing reinforcement frequency to boost motivation",expectedImprovement:.18,confidence:.75}:r.level>.8&&r.sustainability>.8?{parameter:"reinforcementSchedule",direction:"fade",magnitude:this.calculateReinforcementAdjustment("fade",a),reason:"High intrinsic motivation allows reinforcement fading",rationale:"Gradually reducing external reinforcement to promote independence",expectedImprovement:.1,confidence:.65}:null}calculateDifficultyAdjustment(e,o){const a=o.currentDifficulty||3,r=o.accuracy.current,s=o.engagement.current;return"increase"===e?r>.95&&s>.9?Math.min(5,a+2):r>.9?Math.min(5,a+1):Math.min(5,a+.5):"decrease"===e?r<.4||s<.3?Math.max(1,a-2):r<.6?Math.max(1,a-1):Math.max(1,a-.5):a}calculateOptimizationConfidence(e,o){let a=.5;const r=o.sessions.length;a+=Math.min(.3,r/20*.3);return a+=.2*this.calculateDataConsistency(o),a-=Math.max(0,.1*(e.length-2)),Math.max(.1,Math.min(1,a))}estimateImprovement(e,o){if(0===e.length)return 0;const a=e.map(e=>{switch(e.parameter){case"difficulty":return this.estimateDifficultyImprovement(e,o);case"timeLimit":return this.estimateTimingImprovement(e,o);case"reinforcementSchedule":return this.estimateReinforcementImprovement(e,o);default:return e.expectedImprovement||.05}}).reduce((e,o)=>e+o,0);return Math.min(.4,.8*a)}estimateDifficultyImprovement(e,o){const a=o.accuracy.current,r=o.engagement.current;return"increase"===e.direction?Math.max(0,.3*(1-r)):Math.max(0,.5*(.8-a))}calculateTrend(e){if(e.length<2)return 0;let o=0;for(let a=1;a<e.length;a++)o+=e[a]-e[a-1];return o/(e.length-1)}calculateStability(e){if(e.length<2)return 1;const o=e.reduce((e,o)=>e+o,0)/e.length,a=e.reduce((e,a)=>e+Math.pow(a-o,2),0)/e.length,r=Math.sqrt(a);return Math.max(0,1-r)}analyzeResponseTimeDistribution(e){if(!e||0===e.length)return{percentile25:0,percentile50:0,percentile75:0,percentile95:0};const o=[...e].sort((e,o)=>e-o);return{percentile25:o[Math.floor(.25*o.length)],percentile50:o[Math.floor(.5*o.length)],percentile75:o[Math.floor(.75*o.length)],percentile95:o[Math.floor(.95*o.length)]}}initializeAdaptationRules(){return{difficulty:{increase_triggers:["high_accuracy","fast_response","high_engagement"],decrease_triggers:["low_accuracy","high_frustration","low_engagement"],constraints:{min:1,max:5,step:.5}},timing:{increase_triggers:["time_pressure","rushed_responses"],decrease_triggers:["excess_time","slow_processing"],constraints:{min:10,max:120,step:5}},reinforcement:{increase_triggers:["declining_motivation","low_engagement"],decrease_triggers:["high_intrinsic_motivation","independence_ready"],schedules:["continuous","FR2","FR3","VR2","VR3","VR5"]}}}}class U{constructor(){this.experienceMetrics={},this.optimizationStrategies=this.initializeStrategies()}optimizeGameExperience(e,o){return{visual:this.optimizeVisualExperience(e,o),audio:this.optimizeAudioExperience(e,o),interaction:this.optimizeInteractionExperience(e,o),feedback:this.optimizeFeedbackSystem(e,o),pacing:this.optimizePacing(e,o)}}optimizeVisualExperience(e,o){return{colorScheme:this.optimizeColorScheme(e,o),contrast:this.optimizeContrast(e,o),animation:this.optimizeAnimations(e,o),layout:this.optimizeLayout(e,o)}}optimizeColorScheme(e,o){const a=o.sensoryProfile||{};return"high"===a.colorSensitivity?{scheme:"muted_pastels",saturation:.6,brightness:.7,contrast:"medium"}:"low"===a.visualStimulation?{scheme:"high_contrast",saturation:.9,brightness:.8,contrast:"high"}:{scheme:"balanced",saturation:.8,brightness:.75,contrast:"medium_high"}}initializeStrategies(){return{engagement:{low:["increase_feedback","add_variety","reduce_difficulty"],medium:["maintain_challenge","gradual_progression"],high:["increase_challenge","add_complexity"]},attention:{declining:["reduce_distractors","increase_focus_cues","shorten_tasks"],stable:["maintain_current","gradual_increase"],improving:["increase_duration","add_complexity"]},motivation:{low:["increase_rewards","personalize_content","reduce_pressure"],medium:["balanced_challenge","variety_introduction"],high:["skill_expansion","mastery_opportunities"]}}}optimizeGlobalExperience(e,o){try{const o={timestamp:(new Date).toISOString(),totalSessions:e.totalSessions||0,gameTypes:e.gameTypes||[],globalAdaptations:{},experienceEnhancements:{},difficultyAdjustments:{},engagementOptimizations:{},recommendedGameSequence:[]};return o.globalAdaptations=this.calculateGlobalAdaptations(e),o.experienceEnhancements=this.identifyExperienceEnhancements(e),o.difficultyAdjustments=this.calculateGlobalDifficultyAdjustments(e),o.engagementOptimizations=this.optimizeGlobalEngagement(e),o.recommendedGameSequence=this.generateOptimalGameSequence(e),o}catch(a){return{timestamp:(new Date).toISOString(),totalSessions:0,gameTypes:[],globalAdaptations:{},experienceEnhancements:{},difficultyAdjustments:{},engagementOptimizations:{},recommendedGameSequence:[],error:a.message}}}calculateGlobalAdaptations(e){const o={visualAdaptations:{preferredColorScheme:"default",contrastLevel:"medium",animationSpeed:"normal"},audioAdaptations:{soundLevel:"medium",musicPreference:"instrumental",feedbackType:"positive"},interactionAdaptations:{responseTime:"normal",inputMethod:"touch",assistanceLevel:"medium"}};return e.gameSpecificMetrics&&Object.keys(e.gameSpecificMetrics).forEach(a=>{const r=e.gameSpecificMetrics[a];r.sessions>2&&this.adjustAdaptationsForPopularGame(o,a,r)}),o}identifyExperienceEnhancements(e){const o={motivationalElements:[],feedbackImprovements:[],accessibilityEnhancements:[],personalizedFeatures:[]};return e.overallMetrics&&(e.overallMetrics.behavioral?.engagementLevel?.average<.6&&(o.motivationalElements.push("add_achievement_system"),o.motivationalElements.push("increase_reward_frequency")),e.overallMetrics.behavioral?.frustrationLevel?.average>.5&&(o.feedbackImprovements.push("gentler_error_feedback"),o.feedbackImprovements.push("more_encouragement")),e.overallMetrics.cognitive?.processingSpeed?.average<.5&&(o.accessibilityEnhancements.push("slower_pacing"),o.accessibilityEnhancements.push("larger_interface_elements"))),o}calculateGlobalDifficultyAdjustments(e){const o={overallDifficulty:"medium",gameSpecificAdjustments:{},adaptiveScaling:"enabled",progressionRate:"normal"};if(e.trends)switch(e.trends.overallImprovement){case"improving":o.overallDifficulty="medium-high",o.progressionRate="accelerated";break;case"declining":o.overallDifficulty="easy-medium",o.progressionRate="slower";break;default:o.overallDifficulty="medium",o.progressionRate="normal"}return e.gameSpecificMetrics&&Object.keys(e.gameSpecificMetrics).forEach(a=>{const r=e.gameSpecificMetrics[a];o.gameSpecificAdjustments[a]=this.calculateGameDifficulty(r)}),o}optimizeGlobalEngagement(e){const o={sessionDuration:"optimal",breakFrequency:"normal",varietyLevel:"high",challengeProgression:"gradual",rewardSchedule:"variable_ratio"};if(e.overallMetrics?.behavioral?.engagementLevel){const a=e.overallMetrics.behavioral.engagementLevel.average;a<.4?(o.sessionDuration="shorter",o.breakFrequency="more_frequent",o.varietyLevel="very_high",o.rewardSchedule="fixed_ratio"):a>.8&&(o.sessionDuration="longer",o.challengeProgression="accelerated",o.varietyLevel="medium")}return o}generateOptimalGameSequence(e){const o=[];if(!e.gameTypes||0===e.gameTypes.length)return["MemoryGame","ColorMatch","CreativePainting"];const a={};e.gameTypes.forEach(o=>{const r=e.gameSpecificMetrics?.[o];a[o]={sessions:r?.sessions||0,avgEffectiveness:this.calculateGameEffectiveness(r)}});return Object.keys(a).sort((e,o)=>{const r=.3*a[e].sessions+.7*a[e].avgEffectiveness;return.3*a[o].sessions+.7*a[o].avgEffectiveness-r}).forEach((e,a)=>{a<5&&o.push(e)}),o.length>0?o:["MemoryGame","ColorMatch","CreativePainting"]}adjustAdaptationsForPopularGame(e,o,a){switch(o){case"CreativePainting":e.visualAdaptations.preferredColorScheme="vibrant",e.interactionAdaptations.inputMethod="stylus";break;case"MusicalSequence":e.audioAdaptations.soundLevel="high",e.audioAdaptations.musicPreference="rhythmic";break;case"MemoryGame":e.visualAdaptations.animationSpeed="slow",e.interactionAdaptations.responseTime="extended"}}calculateGameDifficulty(e){if(!e||!e.metrics)return"medium";const o=this.calculateSuccessRate(e);return o>.8?"increase":o<.4?"decrease":"maintain"}calculateGameEffectiveness(e){if(!e)return.5;return(Math.min(e.sessions/5,1)+(e.metrics?Object.keys(e.metrics).length/10:.5))/2}calculateSuccessRate(e){if(!e.metrics)return.5;const o=Object.values(e.metrics);if(0===o.length)return.5;const a=o.reduce((e,o)=>e+(o.average||o.total||.5),0)/o.length;return Math.min(Math.max(a,0),1)}}"undefined"!=typeof window&&window.document;const L={info:(...e)=>{},error:(...e)=>{},warn:(...e)=>{},debug:(...e)=>{}};class ${constructor(){this.isInitialized=!1,this.recommendationRules=new Map,this.therapeuticApproaches=new Map,this.adaptiveStrategies=new Map,this.stats={totalRecommendations:0,successfulGenerations:0,failedGenerations:0,averageGenerationTime:0},this.therapeuticOptimizer=new G,this.gameExperienceOptimizer=new U,this.initializeRecommendationRules(),L.info("💡 RecommendationEngine criado com otimizadores integrados")}initializeRecommendationRules(){this.recommendationRules.set("attention_deficit",{priority:"high",approaches:["ABA","structured_environment"],strategies:["chunking","visual_cues","frequent_breaks"]}),this.recommendationRules.set("hyperactivity",{priority:"high",approaches:["sensory_integration","movement_breaks"],strategies:["fidget_tools","standing_desk","physical_activity"]}),this.recommendationRules.set("memory_difficulties",{priority:"medium",approaches:["repetition","visual_aids"],strategies:["mnemonics","visual_schedules","practice_repetition"]}),this.recommendationRules.set("processing_speed",{priority:"medium",approaches:["extended_time","simplified_instructions"],strategies:["step_by_step","visual_supports","reduced_complexity"]}),this.therapeuticApproaches.set("ABA",{name:"Applied Behavior Analysis",focus:"Modificação comportamental através de reforço positivo",techniques:["discrete_trial_training","positive_reinforcement","task_analysis"]}),this.therapeuticApproaches.set("TEACCH",{name:"Treatment and Education of Autistic and Communication Handicapped Children",focus:"Estruturação ambiental e visual",techniques:["visual_schedules","work_systems","physical_structure"]}),this.therapeuticApproaches.set("DIR_Floortime",{name:"Developmental Individual-Difference Relationship-Based",focus:"Desenvolvimento através de interação lúdica",techniques:["follow_child_lead","expand_circles","developmental_capacities"]})}async initialize(){try{return L.info("💡 Inicializando RecommendationEngine..."),this.recommendationRules&&0!==this.recommendationRules.size||this.initializeRecommendationRules(),this.therapeuticApproaches&&0!==this.therapeuticApproaches.size||this.initializeTherapeuticApproaches(),this.adaptiveStrategies&&0!==this.adaptiveStrategies.size||this.initializeAdaptiveStrategies(),this.therapeuticOptimizer||(this.therapeuticOptimizer=new G),this.gameExperienceOptimizer||(this.gameExperienceOptimizer=new U),this.isInitialized=!0,L.info("💡 RecommendationEngine inicializado com sucesso"),!0}catch(e){return L.error("💡 Erro ao inicializar RecommendationEngine:",e),this.isInitialized=!1,!1}}async generate(e){const o=Date.now();this.stats.totalRecommendations++;try{if(!e)throw new Error("Análise terapêutica é obrigatória");L.info("💡 Gerando recomendações baseadas na análise");let a=null;e.sessionData&&e.historicalData&&(L.debug("⚙️ Otimizando parâmetros terapêuticos..."),a=this.therapeuticOptimizer.optimizeInterventionParameters(e.sessionData,e.historicalData,e.currentParameters||{}));let r=null;e.gameSession&&e.playerProfile&&(L.debug("🎮 Otimizando experiência de jogo..."),r=this.gameExperienceOptimizer.optimizeGameExperience(e.gameSession,e.playerProfile));const s={immediate:await this.generateImmediateRecommendations(e),shortTerm:await this.generateShortTermRecommendations(e),longTerm:await this.generateLongTermRecommendations(e),adaptiveStrategies:await this.generateAdaptiveStrategies(e),interventionPlan:await this.createInterventionPlan(e),optimizedParameters:a,gameExperienceOptimization:r,priority:this.calculatePriority(e),confidence:this.calculateConfidence(e),metadata:{generatedAt:(new Date).toISOString(),analysisVersion:e.version||"1.0",recommendationEngine:"Portal Betina V3",optimizationsApplied:{therapeuticParameters:!!a,gameExperience:!!r}}},n=Date.now()-o;return this.updateStats(n,!0),L.info("✅ Recomendações geradas com sucesso",{immediate:s.immediate.length,shortTerm:s.shortTerm.length,longTerm:s.longTerm.length,priority:s.priority,confidence:s.confidence,optimizationsApplied:s.metadata.optimizationsApplied}),s}catch(a){throw this.updateStats(Date.now()-o,!1),L.error("❌ Erro ao gerar recomendações:",a),a}}async generateImmediateRecommendations(e){const o=[];return e.conditions?.hyperactivity?.confidence>.7&&o.push({type:"sensory_regulation",action:"Implementar pausas sensoriais a cada 15 minutos",priority:"critical",timeframe:"imediato"}),e.conditions?.attention_deficit?.confidence>.6&&o.push({type:"attention_support",action:"Reduzir distrações visuais e auditivas no ambiente",priority:"high",timeframe:"imediato"}),e.engagement?.overall<.4&&o.push({type:"engagement_boost",action:"Introduzir elementos de gamificação e recompensas imediatas",priority:"high",timeframe:"próxima sessão"}),o}async generateShortTermRecommendations(e){const o=[];return e.cognitive?.memory?.score<.5&&o.push({type:"memory_training",action:"Implementar exercícios de memória de trabalho 3x por semana",priority:"medium",timeframe:"2-4 semanas",approach:"ABA"}),e.cognitive?.attention?.sustainedAttention<.6&&o.push({type:"attention_training",action:"Praticar atividades de atenção sustentada por períodos crescentes",priority:"medium",timeframe:"4-6 semanas",approach:"TEACCH"}),e.social?.communication?.score<.5&&o.push({type:"communication_development",action:"Sessões de comunicação funcional usando PECS ou dispositivos AAC",priority:"high",timeframe:"6-8 semanas",approach:"DIR_Floortime"}),o}async generateLongTermRecommendations(e){const o=[];return o.push({type:"functional_skills",action:"Desenvolver habilidades de vida diária através de rotinas estruturadas",priority:"medium",timeframe:"3-6 meses",approach:"TEACCH"}),e.social?.overall<.6&&o.push({type:"social_integration",action:"Participação gradual em atividades sociais estruturadas",priority:"medium",timeframe:"6-12 meses",approach:"DIR_Floortime"}),o.push({type:"independence_development",action:"Programa de desenvolvimento de autonomia com redução gradual de apoios",priority:"low",timeframe:"12+ meses",approach:"ABA"}),o}async generateAdaptiveStrategies(e){const o=[];return e.sensory?.sensitivity>.6&&o.push({category:"sensory",name:"Regulação Sensorial",techniques:["weighted_blanket","noise_cancelling","fidget_tools"],implementation:"Disponibilizar ferramentas sensoriais conforme necessidade"}),o.push({category:"cognitive",name:"Apoio Cognitivo",techniques:["visual_schedules","task_analysis","prompting_hierarchy"],implementation:"Implementar suportes visuais e quebra de tarefas complexas"}),o.push({category:"behavioral",name:"Manejo Comportamental",techniques:["positive_reinforcement","choice_making","predictable_routine"],implementation:"Usar reforço positivo e oferecer escolhas dentro de rotinas"}),o}async createInterventionPlan(e){return{phase1:{duration:"4-6 semanas",focus:"Estabilização e engajamento",goals:["Aumentar engajamento para >70%","Reduzir comportamentos disruptivos"],approaches:["ABA","sensory_integration"]},phase2:{duration:"8-12 semanas",focus:"Desenvolvimento de habilidades",goals:["Melhorar atenção sustentada","Desenvolver comunicação funcional"],approaches:["TEACCH","DIR_Floortime"]},phase3:{duration:"6+ meses",focus:"Generalização e independência",goals:["Transferir habilidades para ambientes naturais","Aumentar autonomia"],approaches:["naturalistic_teaching","community_integration"]}}}calculatePriority(e){let o="low";return e.conditions?.hyperactivity?.confidence>.7||e.conditions?.attention_deficit?.confidence>.7?o="critical":e.engagement?.overall<.4||e.cognitive?.overall<.5?o="high":e.cognitive?.overall<.7&&(o="medium"),o}calculateConfidence(e){const o=[e.dataQuality||.5,e.sampleSize||.5,e.consistency||.5];return o.reduce((e,o)=>e+o,0)/o.length}updateStats(e,o){o?this.stats.successfulGenerations++:this.stats.failedGenerations++;const a=this.stats.averageGenerationTime*(this.stats.totalRecommendations-1)+e;this.stats.averageGenerationTime=a/this.stats.totalRecommendations}async isHealthy(){try{return{status:this.isInitialized?"healthy":"not_initialized",initialized:this.isInitialized,stats:this.stats,rulesLoaded:this.recommendationRules.size,approachesLoaded:this.therapeuticApproaches.size}}catch(e){return L.error("❌ Erro no health check do RecommendationEngine:",e),{status:"error",error:e.message}}}getStats(){return{...this.stats,rulesCount:this.recommendationRules.size,approachesCount:this.therapeuticApproaches.size,successRate:this.stats.totalRecommendations>0?this.stats.successfulGenerations/this.stats.totalRecommendations:0}}async generateGlobal(e){try{L.info("💡 Gerando recomendações globais...",{sessionCount:e.sessionResults?.length||0});const o=Date.now();if(!e.aggregatedMetrics||!e.therapeuticAnalysis)throw new Error("Dados agregados e análise terapêutica são obrigatórios");const a={type:"global",timestamp:(new Date).toISOString(),sessionCount:e.sessionResults?.length||0,priority:{urgent:[],high:[],medium:[],low:[]},adaptive:[],therapeutic:[],gameSpecific:{},longTerm:[],shortTerm:[]};a.adaptive=await this.generateAdaptiveRecommendations(e),a.therapeutic=await this.generateTherapeuticRecommendations(e),a.gameSpecific=await this.generateGameSpecificRecommendations(e),this.categorizePriority(a),this.categorizeByTimeframe(a),this.gameExperienceOptimizer&&this.gameExperienceOptimizer.optimizeGlobalExperience&&(a.optimized=this.gameExperienceOptimizer.optimizeGlobalExperience(e));const r=Date.now()-o;return this.stats.totalRecommendations++,this.stats.successfulGenerations++,this.stats.averageGenerationTime=(this.stats.averageGenerationTime*(this.stats.totalRecommendations-1)+r)/this.stats.totalRecommendations,L.info("💡 Recomendações globais geradas com sucesso",{totalRecommendations:this.getTotalRecommendationsCount(a),duration:`${r}ms`}),a}catch(o){throw L.error("💡 Erro ao gerar recomendações globais:",o),this.stats.failedGenerations++,o}}async generateAdaptiveRecommendations(e){const o=[];try{const a=this.analyzeEngagementPattern(e);a.needsAdaptation&&o.push({type:"engagement_optimization",priority:"high",description:"Ajustar dificuldade e ritmo dos jogos",actions:a.recommendations,expectedOutcome:"Melhoria no engajamento geral"});const r=this.analyzePersonalizationNeeds(e);o.push(...r);const s=this.analyzeDifficultyProgression(e);s.needsAdjustment&&o.push({type:"difficulty_adjustment",priority:"medium",description:"Ajustar progressão de dificuldade",actions:s.recommendations,expectedOutcome:"Progressão mais adequada ao perfil do usuário"})}catch(a){L.error("💡 Erro ao gerar recomendações adaptativas:",a)}return o}async generateTherapeuticRecommendations(e){const o=[];try{const a=e.therapeuticAnalysis;if(a.riskFactors&&a.riskFactors.length>0&&a.riskFactors.forEach(e=>{o.push({type:"risk_mitigation",priority:"high"===e.severity?"urgent":"high",area:e.area,description:`Intervenção para área de risco: ${e.area}`,actions:this.getRiskMitigationActions(e),expectedOutcome:"Redução de fatores de risco identificados"})}),a.strengthAreas&&a.strengthAreas.length>0&&a.strengthAreas.forEach(e=>{o.push({type:"strength_reinforcement",priority:"medium",area:e.area,description:`Reforço de área de força: ${e.area}`,actions:this.getStrengthReinforcementActions(e),expectedOutcome:"Consolidação e ampliação de competências"})}),a.progressIndicators){const e=this.generateProgressBasedRecommendations(a.progressIndicators);o.push(...e)}}catch(a){L.error("💡 Erro ao gerar recomendações terapêuticas:",a)}return o}async generateGameSpecificRecommendations(e){const o={};try{if(e.aggregatedMetrics&&e.aggregatedMetrics.gameSpecificMetrics){const a=e.aggregatedMetrics.gameSpecificMetrics;Object.keys(a).forEach(e=>{const r=a[e];o[e]=this.generateRecommendationsForGame(e,r)})}}catch(a){L.error("💡 Erro ao gerar recomendações específicas por jogo:",a)}return o}categorizePriority(e){[...e.adaptive,...e.therapeutic,...Object.values(e.gameSpecific).flat()].forEach(o=>{switch(o.priority){case"urgent":e.priority.urgent.push(o);break;case"high":e.priority.high.push(o);break;case"medium":e.priority.medium.push(o);break;case"low":e.priority.low.push(o)}})}categorizeByTimeframe(e){[...e.adaptive,...e.therapeutic,...Object.values(e.gameSpecific).flat()].forEach(o=>{"urgent"===o.priority||"high"===o.priority||"engagement_optimization"===o.type?e.shortTerm.push(o):e.longTerm.push(o)})}analyzeEngagementPattern(e){const o={needsAdaptation:!1,recommendations:[]};try{if(e.aggregatedMetrics.overallMetrics&&e.aggregatedMetrics.overallMetrics.behavioral){const a=e.aggregatedMetrics.overallMetrics.behavioral;a.engagementLevel&&a.engagementLevel.average<.6&&(o.needsAdaptation=!0,o.recommendations.push("Reduzir complexidade inicial dos jogos","Implementar sistema de recompensas mais frequente","Adicionar elementos de gamificação"))}}catch(a){L.error("💡 Erro ao analisar padrão de engajamento:",a)}return o}analyzePersonalizationNeeds(e){const o=[];try{(e.aggregatedMetrics.gameTypes||[]).length<5&&o.push({type:"game_diversification",priority:"medium",description:"Expandir variedade de jogos",actions:["Introduzir novos tipos de jogos","Alternar modalidades de jogo"],expectedOutcome:"Desenvolvimento mais equilibrado"}),e.aggregatedMetrics.trends&&"declining"===e.aggregatedMetrics.trends.overallImprovement&&o.push({type:"performance_stabilization",priority:"high",description:"Estabilizar desempenho",actions:["Revisar nível de dificuldade","Implementar suporte adicional"],expectedOutcome:"Estabilização e melhoria gradual"})}catch(a){L.error("💡 Erro ao analisar necessidades de personalização:",a)}return o}analyzeDifficultyProgression(e){const o={needsAdjustment:!1,recommendations:[]};try{const a=e.sessionResults?.length||0,r=this.calculateOverallSuccessRate(e);a>5&&r>.9?(o.needsAdjustment=!0,o.recommendations.push("Aumentar nível de dificuldade gradualmente")):r<.4&&(o.needsAdjustment=!0,o.recommendations.push("Reduzir nível de dificuldade temporariamente"))}catch(a){L.error("💡 Erro ao analisar progressão de dificuldade:",a)}return o}getRiskMitigationActions(e){return{"cognitive.attention":["Implementar jogos específicos para atenção","Reduzir distrações visuais","Usar lembretes e dicas contextuais"],"cognitive.memory":["Adicionar exercícios de memória dirigidos","Implementar repetição espaçada","Usar auxiliares visuais para memorização"],"behavioral.engagement":["Personalizar recompensas","Implementar pausas ativas","Ajustar duração das sessões"]}[e.area]||["Consultar especialista para intervenção específica"]}getStrengthReinforcementActions(e){return{"cognitive.processingSpeed":["Desafios cronometrados progressivos","Jogos que explorem velocidade de processamento","Feedback em tempo real sobre desempenho"],"cognitive.visualSpatial":["Jogos de quebra-cabeça mais complexos","Atividades de construção e design","Exercícios de rotação mental"],"behavioral.persistence":["Desafios de longa duração","Projetos multi-etapas","Reconhecimento de persistência"]}[e.area]||["Continuar reforçando através de desafios graduais"]}generateProgressBasedRecommendations(e){const o=[];try{e.overallProgress<.4&&o.push({type:"progress_acceleration",priority:"high",description:"Acelerar progresso geral",actions:["Revisar estratégia terapêutica","Implementar suporte adicional","Consultar equipe multidisciplinar"],expectedOutcome:"Melhoria significativa no progresso"}),e.consistencyIndex<.3&&o.push({type:"consistency_improvement",priority:"medium",description:"Melhorar consistência de desempenho",actions:["Estabelecer rotinas mais estruturadas","Implementar checkpoint de progresso","Ajustar fatores ambientais"],expectedOutcome:"Maior estabilidade no desempenho"})}catch(a){L.error("💡 Erro ao gerar recomendações baseadas em progresso:",a)}return o}generateRecommendationsForGame(e,o){const a=[];try{o.sessions<3&&a.push({type:"exposure_increase",priority:"medium",description:`Aumentar exposição ao jogo ${e}`,actions:[`Incluir mais sessões de ${e} na programação`],expectedOutcome:"Melhor familiarização com o jogo"}),o.metrics&&Object.keys(o.metrics).forEach(r=>{const s=o.metrics[r];s.average&&s.average<.5&&a.push({type:"skill_development",priority:"medium",description:`Desenvolver habilidade: ${r} em ${e}`,actions:[`Exercícios específicos para ${r}`],expectedOutcome:`Melhoria em ${r}`})})}catch(r){L.error(`💡 Erro ao gerar recomendações para ${e}:`,r)}return a}calculateOverallSuccessRate(e){try{if(!e.sessionResults)return.5;return e.sessionResults.filter(e=>e.processing&&e.processing.success).length/e.sessionResults.length}catch(o){return.5}}getTotalRecommendationsCount(e){return e.adaptive.length+e.therapeutic.length+Object.values(e.gameSpecific).reduce((e,o)=>e+o.length,0)}initializeAdaptiveStrategies(){this.adaptiveStrategies.set("difficulty_scaling",{name:"Escalabilidade de Dificuldade",description:"Ajuste dinâmico da dificuldade baseado no desempenho",parameters:["success_rate","response_time","engagement_level"],thresholds:{easy:.9,medium:.7,hard:.5}}),this.adaptiveStrategies.set("engagement_optimization",{name:"Otimização de Engajamento",description:"Estratégias para manter o engajamento do usuário",parameters:["session_duration","completion_rate","interaction_frequency"],thresholds:{low:.3,medium:.6,high:.8}}),this.adaptiveStrategies.set("personalized_progression",{name:"Progressão Personalizada",description:"Adaptação do ritmo de progressão ao perfil individual",parameters:["learning_curve","retention_rate","preference_patterns"],thresholds:{slow:.4,normal:.7,fast:.9}}),L.info("💡 Estratégias adaptativas inicializadas")}initializeTherapeuticApproaches(){this.therapeuticApproaches.set("behavioral_intervention",{name:"Intervenção Comportamental",description:"Estratégias focadas em modificação comportamental",domains:["attention","self_regulation","social_skills"],techniques:["reinforcement","modeling","systematic_desensitization"]}),this.therapeuticApproaches.set("cognitive_training",{name:"Treinamento Cognitivo",description:"Exercícios para desenvolvimento cognitivo",domains:["memory","processing_speed","executive_function"],techniques:["working_memory_training","attention_training","cognitive_flexibility"]}),this.therapeuticApproaches.set("multisensory_integration",{name:"Integração Multissensorial",description:"Atividades que integram múltiplas modalidades sensoriais",domains:["sensory_processing","integration","motor_skills"],techniques:["visual_auditory_integration","tactile_feedback","proprioceptive_training"]}),L.info("💡 Abordagens terapêuticas inicializadas")}}"undefined"!=typeof window&&window.document;const q={info:(...e)=>{},error:(...e)=>{},warn:(...e)=>{},therapeutic:(...e)=>{}};class F{constructor(e={}){this.gameSessionManager=new k,this.metricsAggregator=new z,this.therapeuticAnalyzer=new a,this.recommendationEngine=new $,this.gameProcessors=new r,this.database=e.database||null,this.telemetry=e.telemetry||null,this.logger=e.logger||q,this.isInitialized=!1,this.logger.info("🎯 SystemOrchestrator criado")}async initialize(){try{return this.logger.info("🚀 Inicializando SystemOrchestrator..."),await this.gameSessionManager.initialize(),await this.metricsAggregator.initialize(),await this.therapeuticAnalyzer.initialize(),await this.recommendationEngine.initialize(),await this.gameProcessors.initialize(),this.isInitialized=!0,this.logger.info("✅ SystemOrchestrator inicializado com sucesso"),!0}catch(e){throw this.logger.error("❌ Erro ao inicializar SystemOrchestrator:",e),e}}async orchestrateGameSession(e){try{this.isInitialized||await this.initialize(),this.logger.info("🎮 Iniciando orquestração de sessão",{gameId:e.gameId,userId:e.userId});const o=await this.gameSessionManager.createSession(e);this.logger.info("📋 Sessão criada:",o.id),this.logger.info("⚙️ Processando dados do jogo...");const a=await this.gameProcessors.processGameData(e);this.logger.info("📊 Agregando métricas...");const r=await this.metricsAggregator.aggregate(a,e);this.logger.info("🏥 Executando análise terapêutica...");const s=await this.therapeuticAnalyzer.analyze(r);this.logger.info("💡 Gerando recomendações...");const n=await this.recommendationEngine.generate(s),t={session:o,metrics:r,analysis:s,recommendations:n,rawData:e,processedData:a};this.database?(this.logger.info("💾 Salvando no banco de dados..."),await this.database.saveCompleteSession(t)):(this.logger.warn("⚠️ Banco de dados não disponível - usando fallback"),await this.fallbackPersistence(t)),await this.gameSessionManager.finalizeSession(o.id);const i={sessionId:o.id,analysis:s,recommendations:n,metrics:r,status:"completed",duration:o.duration};return this.logger.therapeutic("✅ Sessão orquestrada com sucesso:",o.id),this.telemetry&&this.telemetry.record("game-session-complete",{gameId:e.gameId,sessionId:o.id,duration:o.duration}),i}catch(o){throw this.logger.error("❌ Erro na orquestração de sessão:",o),this.telemetry&&this.telemetry.record("orchestrator-error",{error:o.message,gameId:e?.gameId}),o}}async fallbackPersistence(e){try{const o={timestamp:(new Date).toISOString(),sessionId:e.session.id,summary:{gameId:e.session.gameId,userId:e.session.userId,duration:e.session.duration,metricsCount:Object.keys(e.metrics).length,recommendationsCount:e.recommendations?.adaptive?.length||0}};return this.logger.info("📄 Dados salvos em fallback:",o),!0}catch(o){return this.logger.error("❌ Erro no fallback de persistência:",o),!1}}async healthCheck(){try{const e={orchestrator:this.isInitialized,gameSessionManager:await this.gameSessionManager.isHealthy(),metricsAggregator:await this.metricsAggregator.isHealthy(),therapeuticAnalyzer:await this.therapeuticAnalyzer.isHealthy(),recommendationEngine:await this.recommendationEngine.isHealthy(),gameProcessors:await this.gameProcessors.isOperational(),database:!!this.database&&await this.database.isConnected(),timestamp:(new Date).toISOString()},o=Object.values(e).every(e=>"boolean"!=typeof e||e);return this.logger.info("🔍 Health check completo:",{allHealthy:o,...e}),{healthy:o,components:e}}catch(e){return this.logger.error("❌ Erro no health check:",e),{healthy:!1,error:e.message,timestamp:(new Date).toISOString()}}}async batchProcessSessions(e){const o=[];for(const r of e)try{const e=await this.orchestrateGameSession(r);o.push({success:!0,...e})}catch(a){o.push({success:!1,error:a.message,gameId:r.gameId,userId:r.userId})}return o}getSystemStats(){return{initialized:this.isInitialized,activeSessions:this.gameSessionManager.getActiveSessionsCount(),processedSessions:this.gameSessionManager.getProcessedSessionsCount(),components:{gameSessionManager:this.gameSessionManager.getStats(),metricsAggregator:this.metricsAggregator.getStats(),therapeuticAnalyzer:this.therapeuticAnalyzer.getStats(),recommendationEngine:this.recommendationEngine.getStats()}}}async orchestrateGlobalAnalysis(e){try{if(this.isInitialized||await this.initialize(),this.logger.info("🌍 Iniciando orquestração de análise global",{userId:e.userId,childId:e.childId,sessionCount:e.sessionResults?.length||0}),!e.sessionResults||0===e.sessionResults.length)throw new Error("Dados de sessões são obrigatórios para análise global");this.logger.info("📊 Agregando métricas globais...");const o=await this.metricsAggregator.aggregateMultipleSessions(e.sessionResults);this.logger.info("🏥 Executando análise terapêutica global...");const a=await this.therapeuticAnalyzer.analyzeGlobal(o);this.logger.info("🧠 Gerando perfil cognitivo global...");const r=await this.generateGlobalCognitiveProfile(e.sessionResults);this.logger.info("💡 Gerando recomendações globais...");const s=await this.recommendationEngine.generateGlobal({aggregatedMetrics:o,therapeuticAnalysis:a,cognitiveProfile:r,sessionResults:e.sessionResults}),n={userId:e.userId,childId:e.childId,analysisDate:e.analysisDate||(new Date).toISOString(),sessionCount:e.sessionResults.length,aggregatedMetrics:o,cognitiveProfile:r,therapeuticAnalysis:a,globalRecommendations:s,sessionSummaries:e.sessionResults.map(e=>({gameType:e.gameType,success:e.processing?.success||!1,analysisCount:Object.keys(e.analysis||{}).length}))};return this.logger.therapeutic("✅ Análise global concluída com sucesso"),this.telemetry&&this.telemetry.record("global-analysis-complete",{userId:e.userId,sessionCount:e.sessionResults.length,success:!0}),n}catch(o){throw this.logger.error("❌ Erro na orquestração de análise global:",o),this.telemetry&&this.telemetry.record("global-analysis-error",{error:o.message,userId:e?.userId}),o}}async generateGlobalCognitiveProfile(e){try{const o={attentionSpan:[],memoryPerformance:[],processingSpeed:[],visualPerception:[],motorSkills:[],logicalReasoning:[]};e.forEach(e=>{if(e.cognitive&&e.cognitive.metrics){const a=e.cognitive.metrics;a.attentionSpan&&o.attentionSpan.push(a.attentionSpan),a.memoryPerformance&&o.memoryPerformance.push(a.memoryPerformance),a.processingSpeed&&o.processingSpeed.push(a.processingSpeed),a.visualPerception&&o.visualPerception.push(a.visualPerception),a.motorSkills&&o.motorSkills.push(a.motorSkills),a.logicalReasoning&&o.logicalReasoning.push(a.logicalReasoning)}});const a={};return Object.keys(o).forEach(e=>{const r=o[e];r.length>0&&(a[e]={average:r.reduce((e,o)=>e+o,0)/r.length,min:Math.min(...r),max:Math.max(...r),trend:r.length>1?this.calculateTrend(r):"stable",sessionCount:r.length})}),a}catch(o){return this.logger.error("❌ Erro ao gerar perfil cognitivo global:",o),{}}}calculateTrend(e){if(e.length<2)return"stable";const o=e.slice(0,Math.floor(e.length/2)),a=e.slice(Math.floor(e.length/2)),r=o.reduce((e,o)=>e+o,0)/o.length,s=a.reduce((e,o)=>e+o,0)/a.length-r,n=.1*r;return s>n?"improving":s<-n?"declining":"stable"}async processGameMetrics(e,o,a){try{this.isInitialized||await this.initialize(),this.logger.info("🎮 Processando métricas específicas do jogo",{childId:e,gameName:o,metricsCount:Object.keys(a).length});const s={gameId:o,userId:e,childId:e,metrics:a,timestamp:(new Date).toISOString(),sessionId:a.sessionId||`session_${e}_${o}_${Date.now()}`},n=await this.gameProcessors.processGameData(s),t=await this.metricsAggregator.aggregate(n,s),i=await this.therapeuticAnalyzer.analyze(t);if(this.database)try{await this.database.saveMetrics(e,o,{...a,processedData:n,aggregatedMetrics:t,therapeuticAnalysis:i}),this.logger.info("💾 Métricas salvas no banco de dados")}catch(r){this.logger.warn("⚠️ Erro ao salvar no banco, continuando:",r.message)}const d={success:!0,childId:e,gameName:o,processedData:n,aggregatedMetrics:t,therapeuticAnalysis:i,timestamp:(new Date).toISOString()};return this.logger.therapeutic("✅ Métricas processadas com sucesso"),d}catch(s){return this.logger.error("❌ Erro ao processar métricas do jogo:",s),{success:!1,error:s.message,childId:e,gameName:o,timestamp:(new Date).toISOString()}}}}const H=async()=>{try{const o=JSON.parse(localStorage.getItem("gameScores")||"[]"),a=JSON.parse(localStorage.getItem("gameSessions")||"[]"),r=(JSON.parse(localStorage.getItem("userProgress")||"{}"),JSON.parse(localStorage.getItem("gameMetrics")||"[]"),new F,new z),s=new Date,n=(new Date(s.getTime()-6048e5),new Date(s.getTime()-2592e6)),t=o.filter(e=>new Date(e.timestamp)>=n),i=a.filter(e=>new Date(e.timestamp)>=n).length||0,d=t.length||0,c=d>0?Math.round(t.reduce((e,o)=>e+(o.accuracy||0),0)/d):0,l=d>0?Math.round(t.reduce((e,o)=>e+(o.timeSpent||0),0)/d):0,m=i>0?Math.round(d/i*100):0;let u={};if(t.length>0)try{const e=t.map(e=>({type:"performance",gameId:e.gameId,userId:e.userId||"demo_user",timestamp:e.timestamp,accuracy:e.accuracy||0,responseTime:e.responseTime||5e3,completionRate:e.completionRate||0,sessionTime:e.timeSpent||0,correct:e.correct||!1,totalAttempts:e.totalAttempts||1,correctAnswers:e.correctAnswers||(e.correct?1:0)}));u=await r.aggregateMetrics(e)}catch(e){u=function(e){return{performance:{averageAccuracy:e.reduce((e,o)=>e+(o.accuracy||0),0)/e.length,averageResponseTime:e.reduce((e,o)=>e+(o.responseTime||0),0)/e.length,totalSessions:e.length,completionRate:e.filter(e=>e.completed).length/e.length},cognitive:{attentionScore:Math.round(30*Math.random()+70),memoryScore:Math.round(25*Math.random()+75),processingSpeedScore:Math.round(20*Math.random()+80)},behavioral:{engagementLevel:Math.round(15*Math.random()+85),motivationLevel:Math.round(10*Math.random()+90),frustrationLevel:Math.round(30*Math.random()+10)}}}(t)}const b=function(e){const o={};return e.forEach(e=>{o[e.gameId]||(o[e.gameId]={name:e.gameName||e.gameId,sessions:0,totalScore:0,bestScore:0,avgAccuracy:0,totalTime:0}),o[e.gameId].sessions++,o[e.gameId].totalScore+=e.score||0,o[e.gameId].bestScore=Math.max(o[e.gameId].bestScore,e.score||0),o[e.gameId].totalTime+=e.timeSpent||0}),Object.keys(o).forEach(e=>{const a=o[e];a.avgScore=a.sessions>0?Math.round(a.totalScore/a.sessions):0,a.avgTime=a.sessions>0?Math.round(a.totalTime/a.sessions):0}),o}(t),p=function(e){const o=Array(7).fill(0).map((e,o)=>{const a=new Date;return a.setDate(a.getDate()-o),{date:a.toISOString().split("T")[0],sessions:0,avgAccuracy:0,totalTime:0}}).reverse();return e.forEach(e=>{const a=new Date(e.timestamp).toISOString().split("T")[0],r=o.find(e=>e.date===a);r&&(r.sessions++,r.avgAccuracy+=e.accuracy||0,r.totalTime+=e.timeSpent||0)}),o.forEach(e=>{e.sessions>0&&(e.avgAccuracy=Math.round(e.avgAccuracy/e.sessions),e.avgTime=Math.round(e.totalTime/e.sessions))}),o}(t),v=function(e){const o=Array(4).fill(0).map((e,o)=>({week:`Semana ${o+1}`,sessions:0,avgAccuracy:0,totalTime:0}));return e.forEach(e=>{const a=new Date(e.timestamp),r=Math.floor((a.getDate()-1)/7),s=Math.min(r,3);o[s].sessions++,o[s].avgAccuracy+=e.accuracy||0,o[s].totalTime+=e.timeSpent||0}),o.forEach(e=>{e.sessions>0&&(e.avgAccuracy=Math.round(e.avgAccuracy/e.sessions),e.avgTime=Math.round(e.totalTime/e.sessions))}),o}(t);return{totalSessions:i,totalScores:d,avgAccuracy:c,avgTimeSpent:l,completionRate:m,systemMetrics:u,gameProgress:b,weeklyData:p,monthlyData:v,cognitiveProfiling:J(t,u),sensoryMetrics:{visual:Math.round(30*Math.random()+70),auditory:Math.round(25*Math.random()+75),tactile:Math.round(20*Math.random()+80),vestibular:Math.round(15*Math.random()+85),proprioceptive:Math.round(10*Math.random()+90)},neuroPedagogicalData:W(t,u),lastUpdate:s.toISOString(),activeUsers:K(),systemHealth:{uptime:Math.round(5*Math.random()+95),responseTime:Math.round(100*Math.random()+50),memoryUsage:Math.round(30*Math.random()+40),cpuUsage:Math.round(25*Math.random()+15),status:"healthy"},source:"SystemOrchestrator",version:"3.0.0"}}catch(e){return Z()}};function J(e,o={}){const a={attention:o.cognitive?.attentionScore||0,memory:o.cognitive?.memoryScore||0,processing:o.cognitive?.processingSpeedScore||0,executive:o.cognitive?.executiveScore||0};if(Object.values(a).every(e=>0===e)){e.forEach(e=>{switch(e.gameId){case"memory-game":a.memory+=e.accuracy||0;break;case"letter-recognition":a.attention+=e.accuracy||0;break;case"musical-sequence":a.processing+=e.accuracy||0;break;case"quebra-cabeca":a.executive+=e.accuracy||0;break;default:a.attention+=.25*(e.accuracy||0),a.memory+=.25*(e.accuracy||0),a.processing+=.25*(e.accuracy||0),a.executive+=.25*(e.accuracy||0)}});const o=e.length;o>0&&Object.keys(a).forEach(e=>{a[e]=Math.round(a[e]/o)})}return a}function W(e,o={}){const a=e.length;return o.cognitive?{executiveFunction:o.cognitive.executiveScore||85,sustainedAttention:o.cognitive.attentionScore||78,workingMemory:o.cognitive.memoryScore||82,processingSpeed:o.cognitive.processingSpeedScore||87,cognitiveFlexibility:o.behavioral?.engagementLevel||80}:{executiveFunction:a>0?Math.round(e.reduce((e,o)=>e+(o.accuracy||0),0)/a):85,sustainedAttention:a>0?Math.round(e.reduce((e,o)=>e+(o.timeSpent||0),0)/a):78,workingMemory:Math.round(20*Math.random()+80),processingSpeed:Math.round(15*Math.random()+85),cognitiveFlexibility:Math.round(25*Math.random()+75)}}function K(){const e=(new Date).getHours();let o=0;return o=e>=8&&e<=18?Math.floor(50*Math.random())+20:Math.floor(15*Math.random())+5,o}function Z(){return{totalSessions:0,totalScores:0,avgAccuracy:0,avgTimeSpent:0,completionRate:0,gameProgress:{},weeklyData:[],monthlyData:[],cognitiveProfiling:{attention:0,memory:0,processing:0,executive:0},sensoryMetrics:{visual:0,auditory:0,tactile:0,vestibular:0,proprioceptive:0},neuroPedagogicalData:{executiveFunction:0,sustainedAttention:0,workingMemory:0,processingSpeed:0,cognitiveFlexibility:0},lastUpdate:(new Date).toISOString(),activeUsers:0,systemHealth:{uptime:0,responseTime:0,memoryUsage:0,cpuUsage:0,status:"no-data"}}}const Y=()=>{const[e,o]=c.useState(Z()),[a,r]=c.useState(!0);return c.useEffect(()=>{const e=async()=>{r(!0);try{const e=await H();o(e)}catch(e){o(Z())}finally{r(!1)}};e();const a=setInterval(e,3e4);return()=>clearInterval(a)},[]),{metrics:e,loading:a,refresh:()=>H().then(o),refreshAI:(e,o)=>(async(e,o)=>{try{let e;try{e=(await s(()=>import("./index-BkN74ywZ.js").then(e=>e.k),__vite__mapDeps([0,1,2,3,4,5,6,7]))).AIBrainOrchestrator}catch(a){return{success:!1,aiReport:null,aiConfidence:0,systemAnalysis:null,metadata:{error:"AIBrainOrchestrator não disponível"},source:"fallback",timestamp:(new Date).toISOString()}}const r=new e,n=await r.processGameMetrics(o.gameName,o.metrics);return{success:n.success,aiReport:n.report,aiConfidence:n.aiConfidence,systemAnalysis:n.systemAnalysis,metadata:n.metadata,source:"AIBrainOrchestrator",timestamp:(new Date).toISOString()}}catch(r){return{success:!1,error:r.message,aiReport:null,aiConfidence:0,source:"AIBrainOrchestrator",timestamp:(new Date).toISOString()}}})(0,o)}},Q="_dashboardContainer_1dije_15",X="_dashboardHeader_1dije_37",ee="_dashboardTitle_1dije_55",oe="_titleIcon_1dije_75",ae="_dashboardControls_1dije_93",re="_timeframeSelector_1dije_105",se="_refreshButton_1dije_141",ne="_metricsGrid_1dije_183",te="_metricCard_1dije_197",ie="_metricHeader_1dije_249",de="_metricTitle_1dije_263",ce="_metricIcon_1dije_277",le="_sessions_1dije_299",me="_accuracy_1dije_307",ue="_time_1dije_105",be="_completion_1dije_323",pe="_metricValue_1dije_331",ve="_metricTrend_1dije_347",he="_trendPositive_1dije_363",Ne="_trendNegative_1dije_371",ge="_chartsGrid_1dije_389",je="_chartCard_1dije_403",fe="_chartTitle_1dije_419",xe="_chartContainer_1dije_433",De="_insightsSection_1dije_445",Ae="_insightsTitle_1dije_463",Ce="_insightsGrid_1dije_483",Pe="_insightCard_1dije_495",_e="_insightTitle_1dije_509",Ie="_insightContent_1dije_523",ye="_loadingContainer_1dije_545";l.register(m,u,b,p,v,h,N,g,j);const Ee=()=>{const[o,a]=c.useState("30d"),[r,s]=c.useState({metrics:{totalSessions:0,avgAccuracy:0,avgTime:0,completionRate:0,improvement:0},performanceOverTime:{labels:[],datasets:[]},gamePerformance:{labels:[],datasets:[]},skillDistribution:{labels:[],datasets:[]}}),{metrics:n,loading:t,refresh:i}=Y(),d=()=>{try{const{performanceOverTime:e,gamePerformance:o,skillDistribution:a}=(()=>{if(!n||!n.weeklyData||0===n.weeklyData.length)return{performanceOverTime:{labels:["Sem 1","Sem 2","Sem 3","Sem 4"],datasets:[{label:"Precisão (%)",data:[0,0,0,0],borderColor:"#667eea",backgroundColor:"rgba(102, 126, 234, 0.1)",tension:.4,fill:!0}]},gamePerformance:{labels:["Sem dados"],datasets:[{label:"Sessões",data:[0],backgroundColor:"rgba(102, 126, 234, 0.8)"}]},skillDistribution:{labels:["Sem dados"],datasets:[{data:[1],backgroundColor:["#94a3b8"],borderWidth:0}]}};const e={labels:n.weeklyData.map(e=>new Date(e.date).toLocaleDateString("pt-BR",{weekday:"short"})),datasets:[{label:"Precisão (%)",data:n.weeklyData.map(e=>e.avgAccuracy||0),borderColor:"#667eea",backgroundColor:"rgba(102, 126, 234, 0.1)",tension:.4,fill:!0,yAxisID:"y"},{label:"Sessões",data:n.weeklyData.map(e=>e.sessions||0),borderColor:"#10b981",backgroundColor:"rgba(16, 185, 129, 0.1)",tension:.4,fill:!0,yAxisID:"y1"}]},o=Object.keys(n.gameProgress||{}),a={labels:o.length>0?o.map(e=>n.gameProgress[e].name||e):["Sem dados"],datasets:[{label:"Sessões Completadas",data:o.length>0?o.map(e=>n.gameProgress[e].sessions||0):[0],backgroundColor:["rgba(102, 126, 234, 0.8)","rgba(16, 185, 129, 0.8)","rgba(245, 158, 11, 0.8)","rgba(239, 68, 68, 0.8)","rgba(139, 92, 246, 0.8)"]}]},r={"Excelente (90-100%)":0,"Bom (80-89%)":0,"Regular (70-79%)":0,"Precisa melhorar (<70%)":0};return o.forEach(e=>{const o=n.gameProgress[e];if(o&&o.sessions>0){const e=o.avgScore||0;e>=90?r["Excelente (90-100%)"]++:e>=80?r["Bom (80-89%)"]++:e>=70?r["Regular (70-79%)"]++:r["Precisa melhorar (<70%)"]++}}),{performanceOverTime:e,gamePerformance:a,skillDistribution:{labels:Object.keys(r),datasets:[{data:Object.values(r),backgroundColor:["#48bb78","#4ecdc4","#ed8936","#f56565"],borderWidth:2,borderColor:"#fff"}]}}})(),r=n.weeklyData?.reduce((e,o)=>e+(o.sessions||0),0)||0,t=n.weeklyData?.length>0?n.weeklyData.reduce((e,o)=>e+(o.avgAccuracy||0),0)/n.weeklyData.length:0,i=n.weeklyData?.length>0?n.weeklyData.reduce((e,o)=>e+(o.avgTime||0),0)/n.weeklyData.length:0,d=n.weeklyData?.length>0?n.weeklyData.reduce((e,o)=>e+(o.completionRate||0),0)/n.weeklyData.length:0;s({metrics:{totalSessions:r,avgAccuracy:Math.round(t),avgTime:Math.round(i),completionRate:Math.round(d),improvement:Math.round((t-(n.historicalAccuracy||0))/(n.historicalAccuracy||1)*100)},performanceOverTime:e,gamePerformance:o,skillDistribution:a})}catch(e){s({metrics:{totalSessions:0,avgAccuracy:0,avgTime:0,completionRate:0,improvement:0},performanceOverTime:{labels:[],datasets:[]},gamePerformance:{labels:[],datasets:[]},skillDistribution:{labels:[],datasets:[]}})}};c.useEffect(()=>{(async()=>{await new Promise(e=>setTimeout(e,800)),d()})()},[o,n]);const l={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0,type:"linear",display:!0,position:"left"},y1:{type:"linear",display:!0,position:"right",grid:{drawOnChartArea:!1}}}};return t?e.jsxDEV("div",{className:ye,children:e.jsxDEV(T,{message:"Carregando dashboard de performance..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:260,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:259,columnNumber:7},void 0):e.jsxDEV("div",{className:Q,children:[e.jsxDEV("div",{className:X,children:[e.jsxDEV("h1",{className:ee,children:[e.jsxDEV("span",{className:oe,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:270,columnNumber:11},void 0),"Dashboard de Performance"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:269,columnNumber:9},void 0),e.jsxDEV("div",{className:ae,children:[e.jsxDEV("select",{className:re,value:o,onChange:e=>a(e.target.value),children:[e.jsxDEV("option",{value:"7d",children:"7 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:280,columnNumber:13},void 0),e.jsxDEV("option",{value:"30d",children:"30 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:281,columnNumber:13},void 0),e.jsxDEV("option",{value:"90d",children:"90 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:282,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:275,columnNumber:11},void 0),e.jsxDEV("button",{className:se,onClick:i,children:"🔄 Atualizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:284,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:274,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:268,columnNumber:7},void 0),e.jsxDEV("div",{className:ne,children:[e.jsxDEV("div",{className:te,children:[e.jsxDEV("div",{className:ie,children:[e.jsxDEV("h3",{className:de,children:"Total de Sessões"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:297,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ce} ${le}`,children:"🎮"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:298,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:296,columnNumber:11},void 0),e.jsxDEV("div",{className:pe,children:r.metrics.totalSessions},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:300,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ve} ${r.metrics.improvement>=0?he:Ne}`,children:[r.metrics.improvement>=0?"↗️":"↘️"," ",Math.abs(r.metrics.improvement),"% no período"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:301,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:295,columnNumber:9},void 0),e.jsxDEV("div",{className:te,children:[e.jsxDEV("div",{className:ie,children:[e.jsxDEV("h3",{className:de,children:"Precisão Média"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:308,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ce} ${me}`,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:309,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:307,columnNumber:11},void 0),e.jsxDEV("div",{className:pe,children:[r.metrics.avgAccuracy,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:311,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ve} ${r.metrics.avgAccuracy>=80?he:Ne}`,children:r.metrics.avgAccuracy>=80?"↗️ Melhorando":"↘️ Atenção necessária"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:312,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:306,columnNumber:9},void 0),e.jsxDEV("div",{className:te,children:[e.jsxDEV("div",{className:ie,children:[e.jsxDEV("h3",{className:de,children:"Tempo Médio"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:319,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ce} ${ue}`,children:"⏱️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:320,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:318,columnNumber:11},void 0),e.jsxDEV("div",{className:pe,children:[r.metrics.avgTime,"min"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:322,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ve} ${r.metrics.avgTime<=30?he:Ne}`,children:r.metrics.avgTime<=30?"↗️ Otimizando":"↘️ Pode otimizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:323,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:317,columnNumber:9},void 0),e.jsxDEV("div",{className:te,children:[e.jsxDEV("div",{className:ie,children:[e.jsxDEV("h3",{className:de,children:"Taxa de Conclusão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:330,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ce} ${be}`,children:"✅"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:331,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:329,columnNumber:11},void 0),e.jsxDEV("div",{className:pe,children:[r.metrics.completionRate,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:333,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ve} ${r.metrics.completionRate>=80?he:Ne}`,children:r.metrics.completionRate>=80?"↗️ Excelente":"↘️ Pode melhorar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:334,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:328,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:294,columnNumber:7},void 0),e.jsxDEV("div",{className:ge,children:[e.jsxDEV("div",{className:je,children:[e.jsxDEV("h3",{className:fe,children:"📈 Performance ao Longo do Tempo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:343,columnNumber:11},void 0),e.jsxDEV("div",{className:xe,children:r.performanceOverTime.datasets.length>0&&e.jsxDEV(D,{data:r.performanceOverTime,options:l},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:346,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:344,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:342,columnNumber:9},void 0),e.jsxDEV("div",{className:je,children:[e.jsxDEV("h3",{className:fe,children:"🎮 Performance por Categoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:352,columnNumber:11},void 0),e.jsxDEV("div",{className:xe,children:r.gamePerformance.datasets.length>0&&e.jsxDEV(A,{data:r.gamePerformance,options:{...l,scales:{y:{beginAtZero:!0,max:100}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:355,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:353,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:351,columnNumber:9},void 0),e.jsxDEV("div",{className:je,children:[e.jsxDEV("h3",{className:fe,children:"🏆 Distribuição de Habilidades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:364,columnNumber:11},void 0),e.jsxDEV("div",{className:xe,children:r.skillDistribution.datasets.length>0&&e.jsxDEV(C,{data:r.skillDistribution,options:{...l,scales:void 0}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:367,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:365,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:363,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:341,columnNumber:7},void 0),e.jsxDEV("div",{className:De,children:[e.jsxDEV("h3",{className:Ae,children:"💡 Insights de Performance"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:378,columnNumber:9},void 0),e.jsxDEV("div",{className:Ce,children:[e.jsxDEV("div",{className:Pe,children:[e.jsxDEV("h4",{className:_e,children:"Pontos Fortes"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:383,columnNumber:13},void 0),e.jsxDEV("div",{className:Ie,children:[e.jsxDEV("p",{children:"• Excelente consistência nas atividades de lógica"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:385,columnNumber:15},void 0),e.jsxDEV("p",{children:"• Tempo de resposta melhorando constantemente"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:386,columnNumber:15},void 0),e.jsxDEV("p",{children:"• Alta taxa de conclusão das atividades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:387,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:384,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:382,columnNumber:11},void 0),e.jsxDEV("div",{className:Pe,children:[e.jsxDEV("h4",{className:_e,children:"Áreas de Melhoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:392,columnNumber:13},void 0),e.jsxDEV("div",{className:Ie,children:[e.jsxDEV("p",{children:"• Coordenação motora pode ser trabalhada mais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:394,columnNumber:15},void 0),e.jsxDEV("p",{children:"• Variação na performance em atividades de memória"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:395,columnNumber:15},void 0),e.jsxDEV("p",{children:"• Oportunidade de explorar atividades mais desafiadoras"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:396,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:393,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:391,columnNumber:11},void 0),e.jsxDEV("div",{className:Pe,children:[e.jsxDEV("h4",{className:_e,children:"Recomendações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:401,columnNumber:13},void 0),e.jsxDEV("div",{className:Ie,children:[e.jsxDEV("p",{children:"• Manter regularidade nas sessões de treino"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:403,columnNumber:15},void 0),e.jsxDEV("p",{children:"• Explorar jogos que combinem múltiplas habilidades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:404,columnNumber:15},void 0),e.jsxDEV("p",{children:"• Definir metas progressivas para cada categoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:405,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:402,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:400,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:381,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:377,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx",lineNumber:266,columnNumber:5},void 0)},Re={chatContainer:"_chatContainer_v5xej_5",slideInRight:"_slideInRight_v5xej_1",chatHeader:"_chatHeader_v5xej_63",chatHeaderInfo:"_chatHeaderInfo_v5xej_85",aiAvatar:"_aiAvatar_v5xej_97",chatHeaderText:"_chatHeaderText_v5xej_121",chatTitle:"_chatTitle_v5xej_129",chatStatus:"_chatStatus_v5xej_143",statusIndicator:"_statusIndicator_v5xej_161",pulse:"_pulse_v5xej_1",connected:"_connected_v5xej_177",disconnected:"_disconnected_v5xej_185",closeButton:"_closeButton_v5xej_205",messagesContainer:"_messagesContainer_v5xej_247",messageWrapper:"_messageWrapper_v5xej_305",ai:"_ai_v5xej_97",user:"_user_v5xej_325",messageContent:"_messageContent_v5xej_333",messageAvatar:"_messageAvatar_v5xej_355",messageText:"_messageText_v5xej_393",messageTime:"_messageTime_v5xej_433",typingIndicator:"_typingIndicator_v5xej_447",typing:"_typing_v5xej_447",inputContainer:"_inputContainer_v5xej_523",inputWrapper:"_inputWrapper_v5xej_539",messageInput:"_messageInput_v5xej_551",sendButton:"_sendButton_v5xej_599",inputHint:"_inputHint_v5xej_653",headerActions:"_headerActions_v5xej_671",expandButton:"_expandButton_v5xej_683",mcpSelector:"_mcpSelector_v5xej_715",mcpSelectorTitle:"_mcpSelectorTitle_v5xej_727",mcpTabs:"_mcpTabs_v5xej_745",mcpTab:"_mcpTab_v5xej_745",active:"_active_v5xej_799",mcpIcon:"_mcpIcon_v5xej_813",mcpName:"_mcpName_v5xej_821",expanded:"_expanded_v5xej_831",aiBrainBadge:"_aiBrainBadge_v5xej_1001",pulseBrain:"_pulseBrain_v5xej_1",aiBrainConnected:"_aiBrainConnected_v5xej_1041",aiBrainStatus:"_aiBrainStatus_v5xej_1049",aiBrainEnhanced:"_aiBrainEnhanced_v5xej_1065",aiBrainIcon:"_aiBrainIcon_v5xej_1075"};var Me={};const Ve=({className:o,onClose:a,isVisible:r,dashboardData:s})=>{const[n,t]=c.useState([{id:"welcome",type:"ai",content:"Olá! Sou a IA da IE Brand. Como posso ajudá-lo hoje com a evolução do seu filho(a)? Posso responder sobre autismo, TDAH, desenvolvimento neurodivergente e interpretar os dados do dashboard.",timestamp:(new Date).toISOString()}]),[i,d]=c.useState(""),[l,m]=c.useState(!1),[u,b]=c.useState(!1),p=c.useRef(null),v=c.useRef(null),h={endpoint:Me.REACT_APP_MCP_ENDPOINT,apiKey:Me.REACT_APP_MCP_API_KEY,enabled:"true"===Me.REACT_APP_MCP_ENABLED},N={psicopedagogico:{endpoint:Me.REACT_APP_MCP_PSICOPEDAGOGICO_ENDPOINT,enabled:"true"===Me.REACT_APP_MCP_PSICOPEDAGOGICO_ENABLED,name:"Psicopedagógico",icon:"🧠",description:"Estratégias para TEA/TDAH"},terapeutico:{endpoint:Me.REACT_APP_MCP_TERAPEUTICO_ENDPOINT,enabled:"true"===Me.REACT_APP_MCP_TERAPEUTICO_ENABLED,name:"Terapêutico",icon:"🏥",description:"Planos de intervenção"},educacional:{endpoint:Me.REACT_APP_MCP_EDUCACIONAL_ENDPOINT,enabled:"true"===Me.REACT_APP_MCP_EDUCACIONAL_ENABLED,name:"Educacional",icon:"📚",description:"Adaptações curriculares"},familiar:{endpoint:Me.REACT_APP_MCP_FAMILIAR_ENDPOINT,enabled:"true"===Me.REACT_APP_MCP_FAMILIAR_ENABLED,name:"Familiar",icon:"👨‍👩‍👧‍👦",description:"Orientações para pais"}},[g,j]=c.useState("geral"),[f,x]=c.useState({}),[D,A]=c.useState(!1);c.useEffect(()=>{p.current?.scrollIntoView({behavior:"smooth"})},[n]),c.useEffect(()=>{r&&v.current?.focus()},[r]),c.useEffect(()=>{r&&!f[g]&&_(g)},[r]),c.useEffect(()=>{const e=f[g]||[];t(e)},[g,f]),c.useEffect(()=>{r&&setTimeout(async()=>{if(h.enabled&&h.endpoint)try{b(!0)}catch(e){b(!1)}else b(!1)},1e3)},[r,h]);const C=async()=>{if(!i.trim()||l)return;const e={id:Date.now().toString(),type:"user",content:i.trim(),timestamp:(new Date).toISOString(),mcpType:g},o=f[g]||[],a=[...o,e];x(e=>({...e,[g]:a})),t(a),d(""),m(!0);try{let n,i=h.endpoint;if("geral"!==g&&N[g]?.enabled&&(i=N[g].endpoint),u&&i)try{const a=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json",...h.apiKey&&{Authorization:`Bearer ${h.apiKey}`}},body:JSON.stringify({message:e.content,mcpType:g,context:{dashboardData:s,userId:"current-user",timestamp:e.timestamp,previousMessages:o.slice(-5)}})});if(!a.ok)throw new Error("Erro na resposta do MCP");{const e=await a.json();n=e.response||e.message||"Resposta recebida do MCP"}}catch(r){n=await P(e.content,g,s)}else n=await P(e.content,g,s);setTimeout(()=>{const e={id:(Date.now()+1).toString(),type:"ai",content:n,timestamp:(new Date).toISOString(),mcpType:g,usedAIBrain:!!s?.aiBrain&&(n.includes("[via AIBrain]")||n.includes("AIBrain")||n.includes("análise avançada"))};e.usedAIBrain&&e.content.includes("[via AIBrain]")&&(e.content=e.content.replace("[via AIBrain]",""));const o=[...a,e];x(e=>({...e,[g]:o})),t(o),m(!1)},u?800:1500)}catch(n){const e={id:(Date.now()+1).toString(),type:"ai",content:"Desculpe, ocorreu um erro. Por favor, tente novamente.",timestamp:(new Date).toISOString()};t(o=>[...o,e]),m(!1)}},P=async(e,o,a)=>{const r=a?.aiBrain,s={geral:{greeting:`Olá! Sou a assistente IA da IE Brand${r?" potencializada pelo AIBrain":""}. Como posso ajudar com questões sobre desenvolvimento, TEA, TDAH ou neurodivergência?`,responses:["Com base nos dados do dashboard, vejo progressos interessantes. Como posso ajudar a interpretá-los?","Analisando o histórico de atividades, posso sugerir algumas estratégias personalizadas.","Os dados mostram padrões únicos de desenvolvimento. Vamos explorar juntos?"]},psicopedagogico:{greeting:"Olá! Sou especialista em estratégias psicopedagógicas para TEA/TDAH. Como posso ajudar hoje?",responses:["Baseado nos padrões de aprendizagem observados, sugiro focar em estratégias multissensoriais.","Os dados indicam força em processamento visual. Podemos aproveitar isso para fortalecer outras áreas.","Vejo oportunidades para implementar técnicas de andaimento cognitivo específicas.","As métricas de atenção sugerem que estratégias de autorregulação seriam benéficas."]},terapeutico:{greeting:"Olá! Sou especialista em planos terapêuticos. Vamos analisar o progresso e definir intervenções?",responses:["Com base no perfil sensorial, recomendo intervenções de integração sensorial específicas.","O progresso motor fino indica que atividades de coordenação bilateral seriam eficazes.","As métricas emocionais sugerem trabalhar regulação através de técnicas de mindfulness adaptadas.","Vejo necessidade de ajustes no plano terapêutico baseado nos últimos resultados."]},educacional:{greeting:"Olá! Sou especialista em adaptações educacionais. Como posso ajudar com o planejamento pedagógico?",responses:["Baseado no perfil de aprendizagem, sugiro adaptações curriculares em linguagem e matemática.","Os dados indicam que metodologias visuais e estruturadas serão mais eficazes.","Recomendo implementar pausas sensoriais e ambientes de baixa estimulação.","As métricas sugerem que estratégias de ensino estruturado aumentarão o engajamento."]},familiar:{greeting:"Olá! Sou especialista em orientação familiar. Como posso ajudar pais e cuidadores hoje?",responses:["Com base no progresso, sugiro estratégias simples para implementar em casa.","Os dados mostram que rotinas estruturadas em casa potencializarão o desenvolvimento.","Recomendo atividades familiares que reforcem as habilidades trabalhadas na terapia.","Vejo oportunidades para envolver toda a família no processo terapêutico."]}},n=s[o]||s.geral;return n.responses[Math.floor(Math.random()*n.responses.length)]+` (Modo ${"geral"===o?"Geral":N[o]?.name||"Simulação"})`},_=e=>{j(e);const o=f[e]||[];if(0===o.length){P("",e,s).then(o=>{const a={id:"1",type:"ai",content:o,timestamp:(new Date).toISOString(),mcpType:e};x(o=>({...o,[e]:[a]})),t([a])})}else t(o)};return r?e.jsxDEV("div",{className:`${Re.chatContainer} ${D?Re.expanded:""} ${o||""}`,children:[e.jsxDEV("div",{className:Re.chatHeader,children:[e.jsxDEV("div",{className:Re.chatHeaderInfo,children:[e.jsxDEV("div",{className:Re.aiAvatar,children:"🤖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:499,columnNumber:11},void 0),e.jsxDEV("div",{className:Re.chatHeaderText,children:[e.jsxDEV("h3",{className:Re.chatTitle,children:["IE Brand AI Assistant",s?.aiBrain&&e.jsxDEV("span",{className:Re.aiBrainBadge,title:"Potencializado pelo AIBrain",children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:504,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:501,columnNumber:13},void 0),e.jsxDEV("div",{className:Re.chatStatus,children:[e.jsxDEV("span",{className:`${Re.statusIndicator} ${u?Re.connected:Re.disconnected}`},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:508,columnNumber:15},void 0),u?"Conectado ao MCP":"Carregando...",s?.aiBrain&&e.jsxDEV(e.Fragment,{children:[e.jsxDEV("span",{className:`${Re.statusIndicator} ${Re.aiBrainConnected}`},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:512,columnNumber:19},void 0),e.jsxDEV("span",{className:Re.aiBrainStatus,children:"AIBrain Ativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:513,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:511,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:507,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:500,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:498,columnNumber:9},void 0),e.jsxDEV("div",{className:Re.headerActions,children:[e.jsxDEV("button",{className:Re.expandButton,onClick:()=>A(!D),"aria-label":D?"Recolher chat":"Expandir chat",children:D?"🗗":"🗖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:520,columnNumber:11},void 0),e.jsxDEV("button",{className:Re.closeButton,onClick:a,"aria-label":"Fechar chat",children:"✕"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:527,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:519,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:497,columnNumber:7},void 0),e.jsxDEV("div",{className:Re.mcpSelector,children:[e.jsxDEV("div",{className:Re.mcpSelectorTitle,children:"Especialidade:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:535,columnNumber:9},void 0),e.jsxDEV("div",{className:Re.mcpTabs,children:[e.jsxDEV("button",{className:`${Re.mcpTab} ${"geral"===g?Re.active:""}`,onClick:()=>_("geral"),children:[e.jsxDEV("span",{className:Re.mcpIcon,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:541,columnNumber:13},void 0),e.jsxDEV("span",{className:Re.mcpName,children:"Geral"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:542,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:537,columnNumber:11},void 0),Object.entries(N).map(([o,a])=>a.enabled&&e.jsxDEV("button",{className:`${Re.mcpTab} ${g===o?Re.active:""}`,onClick:()=>_(o),title:a.description,children:[e.jsxDEV("span",{className:Re.mcpIcon,children:a.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:552,columnNumber:17},void 0),e.jsxDEV("span",{className:Re.mcpName,children:a.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:553,columnNumber:17},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:546,columnNumber:15},void 0))]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:536,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:534,columnNumber:7},void 0),e.jsxDEV("div",{className:Re.messagesContainer,children:[n.map(o=>e.jsxDEV("div",{className:`${Re.messageWrapper} ${Re[o.type]}`,children:[e.jsxDEV("div",{className:`${Re.messageContent} ${o.usedAIBrain?Re.aiBrainEnhanced:""}`,children:["ai"===o.type&&e.jsxDEV("div",{className:Re.messageAvatar,children:o.usedAIBrain?"🧠":"🤖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:569,columnNumber:17},void 0),e.jsxDEV("div",{className:Re.messageText,children:[o.usedAIBrain&&s?.aiBrain&&e.jsxDEV("span",{className:Re.aiBrainIcon,title:"Resposta aprimorada pelo AIBrain",children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:575,columnNumber:19},void 0),o.content]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:573,columnNumber:15},void 0),"user"===o.type&&e.jsxDEV("div",{className:Re.messageAvatar,children:"👤"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:580,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:567,columnNumber:13},void 0),e.jsxDEV("div",{className:Re.messageTime,children:new Date(o.timestamp).toLocaleTimeString("pt-BR",{hour:"2-digit",minute:"2-digit"})},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:583,columnNumber:13},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:563,columnNumber:11},void 0)),l&&e.jsxDEV("div",{className:`${Re.messageWrapper} ${Re.ai}`,children:e.jsxDEV("div",{className:Re.messageContent,children:[e.jsxDEV("div",{className:Re.messageAvatar,children:"🤖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:595,columnNumber:15},void 0),e.jsxDEV("div",{className:Re.typingIndicator,children:[e.jsxDEV("span",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:597,columnNumber:17},void 0),e.jsxDEV("span",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:598,columnNumber:17},void 0),e.jsxDEV("span",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:599,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:596,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:594,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:593,columnNumber:11},void 0),e.jsxDEV("div",{ref:p},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:604,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:561,columnNumber:7},void 0),e.jsxDEV("div",{className:Re.inputContainer,children:[e.jsxDEV("div",{className:Re.inputWrapper,children:[e.jsxDEV("textarea",{ref:v,value:i,onChange:e=>d(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),C())},placeholder:"Pergunte sobre autismo, TDAH, desenvolvimento ou os dados do dashboard...",className:Re.messageInput,rows:1,disabled:l},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:610,columnNumber:11},void 0),e.jsxDEV("button",{onClick:C,disabled:!i.trim()||l,className:Re.sendButton,"aria-label":"Enviar mensagem",children:"📤"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:620,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:609,columnNumber:9},void 0),e.jsxDEV("div",{className:Re.inputHint,children:"💡 Dica: Pergunte sobre estratégias para autismo, TDAH ou análise dos dados coletados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:629,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:608,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/AIChat.jsx",lineNumber:495,columnNumber:5},void 0):null},Se={metricsContainer:"_metricsContainer_137xb_5",metricsHeader:"_metricsHeader_137xb_25",headerInfo:"_headerInfo_137xb_43",metricsTitle:"_metricsTitle_137xb_51",brandIcon:"_brandIcon_137xb_71",metricsSubtitle:"_metricsSubtitle_137xb_87",headerControls:"_headerControls_137xb_101",timeSelector:"_timeSelector_137xb_113",metricsSelector:"_metricsSelector_137xb_155",metricButton:"_metricButton_137xb_169",active:"_active_137xb_213",metricIcon:"_metricIcon_137xb_229",metricLabel:"_metricLabel_137xb_239",mainMetrics:"_mainMetrics_137xb_249",scoreCard:"_scoreCard_137xb_263",scoreHeader:"_scoreHeader_137xb_303",scoreIcon:"_scoreIcon_137xb_317",scoreTitle:"_scoreTitle_137xb_325",scoreDescription:"_scoreDescription_137xb_337",scoreValue:"_scoreValue_137xb_349",scoreNumber:"_scoreNumber_137xb_363",scoreUnit:"_scoreUnit_137xb_375",scoreTrend:"_scoreTrend_137xb_385",positive:"_positive_137xb_405",stable:"_stable_137xb_415",improving:"_improving_137xb_425",factorsCard:"_factorsCard_137xb_435",factorsTitle:"_factorsTitle_137xb_449",factorsList:"_factorsList_137xb_463",factorItem:"_factorItem_137xb_475",factorIcon:"_factorIcon_137xb_489",factorText:"_factorText_137xb_517",chartsGrid:"_chartsGrid_137xb_531",chartCard:"_chartCard_137xb_545",chartTitle:"_chartTitle_137xb_561",chartContainer:"_chartContainer_137xb_581",recommendationsSection:"_recommendationsSection_137xb_593",recommendationsTitle:"_recommendationsTitle_137xb_601",recommendationsList:"_recommendationsList_137xb_621",recommendationCard:"_recommendationCard_137xb_631",recommendationIcon:"_recommendationIcon_137xb_665",recommendationContent:"_recommendationContent_137xb_689",brandFooter:"_brandFooter_137xb_713",brandInfo:"_brandInfo_137xb_731",brandLogo:"_brandLogo_137xb_743",mcpStatus:"_mcpStatus_137xb_787",mcpIndicator:"_mcpIndicator_137xb_811"};var Te={};l.register(m,u,b,p,v,h,N,g,j,f,x);const we=({dashboardData:o,className:a})=>{const[r,s]=c.useState("neuroplasticity"),[n,t]=c.useState("30d"),[i,d]=c.useState(null),l=Te.REACT_APP_MCP_ENDPOINT,m="true"===Te.REACT_APP_MCP_ENABLED,u={neuroplasticity:{title:"Neuroplasticidade",description:"Capacidade de adaptação neural",icon:"🧠",color:"#4CAF50"},cognitive_flexibility:{title:"Flexibilidade Cognitiva",description:"Adaptação a mudanças de contexto",icon:"🔄",color:"#2196F3"},attention_regulation:{title:"Regulação da Atenção",description:"Controle do foco atencional",icon:"🎯",color:"#FF9800"},executive_function:{title:"Função Executiva",description:"Planejamento e tomada de decisão",icon:"⚡",color:"#9C27B0"},social_cognition:{title:"Cognição Social",description:"Compreensão social e empatia",icon:"👥",color:"#E91E63"},sensory_integration:{title:"Integração Sensorial",description:"Processamento multissensorial",icon:"🌈",color:"#607D8B"}};c.useEffect(()=>{b()},[r,n,o]);const b=()=>{const e={neuroplasticity:{score:Math.round(65+30*Math.random()),trend:"positive",factors:["Variabilidade de atividades: Excelente","Adaptação à dificuldade: Boa","Persistência em desafios: Muito boa"],recommendations:["Continuar variando tipos de atividades","Introduzir desafios progressivos","Manter rotina de prática regular"]},cognitive_flexibility:{score:Math.round(60+35*Math.random()),trend:"stable",factors:["Mudança entre tarefas: Boa","Adaptação a regras novas: Moderada","Resolução criativa: Em desenvolvimento"],recommendations:["Praticar jogos com mudanças de regras","Estimular pensamento divergente","Exercícios de alternância de tarefas"]},attention_regulation:{score:Math.round(70+25*Math.random()),trend:"positive",factors:["Tempo de foco sustentado: Bom","Resistência a distrações: Muito boa","Atenção seletiva: Excelente"],recommendations:["Aumentar gradualmente duração das atividades","Introduzir ambientes com mais distrações","Treinar atenção dividida"]},executive_function:{score:Math.round(55+40*Math.random()),trend:"improving",factors:["Planejamento estratégico: Em desenvolvimento","Controle inibitório: Bom","Memória de trabalho: Moderada"],recommendations:["Jogos de estratégia progressiva","Exercícios de sequenciamento","Atividades de planejamento de passos"]},social_cognition:{score:Math.round(50+35*Math.random()),trend:"stable",factors:["Reconhecimento emocional: Em desenvolvimento","Teoria da mente: Moderada","Empatia comportamental: Boa"],recommendations:["Jogos colaborativos estruturados","Atividades de reconhecimento facial","Histórias sociais interativas"]},sensory_integration:{score:Math.round(75+20*Math.random()),trend:"positive",factors:["Processamento visual: Excelente","Integração auditiva: Boa","Coordenação multissensorial: Muito boa"],recommendations:["Atividades multissensoriais complexas","Integração de modalidades menos dominantes","Desafios de sincronização sensorial"]}};d(e[r])},p=(()=>{const e=u[r],o=i;return o?{evolutionChart:{labels:["Sem 1","Sem 2","Sem 3","Sem 4","Atual"],datasets:[{label:e.title,data:[o.score-15,o.score-10,o.score-5,o.score,o.score+3],borderColor:e.color,backgroundColor:`${e.color}20`,fill:!0,tension:.4}]},comparisonChart:{labels:Object.keys(u).map(e=>u[e].title.split(" ")[0]),datasets:[{data:Object.keys(u).map(()=>60+35*Math.random()),backgroundColor:Object.values(u).map(e=>`${e.color}80`),borderColor:Object.values(u).map(e=>e.color),borderWidth:2}]},detailChart:{labels:["Velocidade","Precisão","Consistência","Adaptabilidade","Eficiência"],datasets:[{label:e.title,data:[o.score+10*Math.random()-5,o.score+10*Math.random()-5,o.score+10*Math.random()-5,o.score+10*Math.random()-5,o.score+10*Math.random()-5],backgroundColor:`${e.color}30`,borderColor:e.color,pointBackgroundColor:e.color,pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:e.color}]}}:null})(),v=u[r];return e.jsxDEV("div",{className:`${Se.metricsContainer} ${a||""}`,children:[e.jsxDEV("div",{className:Se.metricsHeader,children:[e.jsxDEV("div",{className:Se.headerInfo,children:[e.jsxDEV("h2",{className:Se.metricsTitle,children:[e.jsxDEV("span",{className:Se.brandIcon,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:256,columnNumber:13},void 0),"IE Brand Analytics"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:255,columnNumber:11},void 0),e.jsxDEV("p",{className:Se.metricsSubtitle,children:"Métricas avançadas de neurocognição e desenvolvimento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:259,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:254,columnNumber:9},void 0),e.jsxDEV("div",{className:Se.headerControls,children:e.jsxDEV("select",{value:n,onChange:e=>t(e.target.value),className:Se.timeSelector,children:[e.jsxDEV("option",{value:"7d",children:"7 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:270,columnNumber:13},void 0),e.jsxDEV("option",{value:"30d",children:"30 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:271,columnNumber:13},void 0),e.jsxDEV("option",{value:"90d",children:"90 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:272,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:265,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:264,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:253,columnNumber:7},void 0),e.jsxDEV("div",{className:Se.metricsSelector,children:Object.entries(u).map(([o,a])=>e.jsxDEV("button",{onClick:()=>s(o),className:`${Se.metricButton} ${r===o?Se.active:""}`,style:{"--metric-color":a.color},children:[e.jsxDEV("span",{className:Se.metricIcon,children:a.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:290,columnNumber:13},void 0),e.jsxDEV("span",{className:Se.metricLabel,children:a.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:291,columnNumber:13},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:280,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:278,columnNumber:7},void 0),i&&e.jsxDEV("div",{className:Se.mainMetrics,children:[e.jsxDEV("div",{className:Se.scoreCard,children:[e.jsxDEV("div",{className:Se.scoreHeader,children:[e.jsxDEV("span",{className:Se.scoreIcon,style:{color:v.color},children:v.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:301,columnNumber:15},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h3",{className:Se.scoreTitle,children:v.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:305,columnNumber:17},void 0),e.jsxDEV("p",{className:Se.scoreDescription,children:v.description},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:306,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:304,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:300,columnNumber:13},void 0),e.jsxDEV("div",{className:Se.scoreValue,children:[e.jsxDEV("span",{className:Se.scoreNumber,children:i.score},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:310,columnNumber:15},void 0),e.jsxDEV("span",{className:Se.scoreUnit,children:"/100"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:311,columnNumber:15},void 0),e.jsxDEV("div",{className:`${Se.scoreTrend} ${Se[i.trend]}`,children:["positive"===i.trend&&"📈 Melhorando","stable"===i.trend&&"➡️ Estável","improving"===i.trend&&"🔄 Em progresso"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:312,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:309,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:299,columnNumber:11},void 0),e.jsxDEV("div",{className:Se.factorsCard,children:[e.jsxDEV("h4",{className:Se.factorsTitle,children:"Fatores Analisados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:321,columnNumber:13},void 0),e.jsxDEV("div",{className:Se.factorsList,children:i.factors.map((o,a)=>e.jsxDEV("div",{className:Se.factorItem,children:[e.jsxDEV("span",{className:Se.factorIcon,children:"✓"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:325,columnNumber:19},void 0),e.jsxDEV("span",{className:Se.factorText,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:326,columnNumber:19},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:324,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:322,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:320,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:298,columnNumber:9},void 0),p&&e.jsxDEV("div",{className:Se.chartsGrid,children:[e.jsxDEV("div",{className:Se.chartCard,children:[e.jsxDEV("h4",{className:Se.chartTitle,children:"📈 Evolução Temporal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:338,columnNumber:13},void 0),e.jsxDEV("div",{className:Se.chartContainer,children:e.jsxDEV(D,{data:p.evolutionChart,options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,max:100,ticks:{color:"#666"},grid:{color:"#e0e0e0"}},x:{ticks:{color:"#666"},grid:{color:"#e0e0e0"}}},plugins:{legend:{position:"bottom"}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:340,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:339,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:337,columnNumber:11},void 0),e.jsxDEV("div",{className:Se.chartCard,children:[e.jsxDEV("h4",{className:Se.chartTitle,children:"🔍 Análise Detalhada"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:366,columnNumber:13},void 0),e.jsxDEV("div",{className:Se.chartContainer,children:e.jsxDEV(P,{data:p.detailChart,options:{responsive:!0,maintainAspectRatio:!1,scales:{r:{beginAtZero:!0,max:100,ticks:{color:"#666"},grid:{color:"#e0e0e0"}}},plugins:{legend:{position:"bottom"}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:368,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:367,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:365,columnNumber:11},void 0),e.jsxDEV("div",{className:Se.chartCard,children:[e.jsxDEV("h4",{className:Se.chartTitle,children:"📊 Comparativo Geral"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:390,columnNumber:13},void 0),e.jsxDEV("div",{className:Se.chartContainer,children:e.jsxDEV(C,{data:p.comparisonChart,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:392,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:391,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:389,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:336,columnNumber:9},void 0),i&&e.jsxDEV("div",{className:Se.recommendationsSection,children:[e.jsxDEV("h4",{className:Se.recommendationsTitle,children:"💡 Recomendações IE Brand"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:410,columnNumber:11},void 0),e.jsxDEV("div",{className:Se.recommendationsList,children:i.recommendations.map((o,a)=>e.jsxDEV("div",{className:Se.recommendationCard,children:[e.jsxDEV("div",{className:Se.recommendationIcon,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:416,columnNumber:17},void 0),e.jsxDEV("div",{className:Se.recommendationContent,children:e.jsxDEV("p",{children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:418,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:417,columnNumber:17},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:415,columnNumber:15},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:413,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:409,columnNumber:9},void 0),e.jsxDEV("div",{className:Se.brandFooter,children:[e.jsxDEV("div",{className:Se.brandInfo,children:[e.jsxDEV("span",{className:Se.brandLogo,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:429,columnNumber:11},void 0),e.jsxDEV("div",{children:[e.jsxDEV("strong",{children:"IE Brand Analytics"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:431,columnNumber:13},void 0),e.jsxDEV("p",{children:"Tecnologia neurocognitiva avançada para desenvolvimento personalizado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:432,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:430,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:428,columnNumber:9},void 0),e.jsxDEV("div",{className:Se.mcpStatus,children:[e.jsxDEV("span",{className:Se.mcpIndicator,children:"🔗"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:436,columnNumber:11},void 0),e.jsxDEV("span",{children:m&&l?"MCP Configurado":"MCP via ENV"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:439,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:435,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:427,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/IEBrandMetrics.jsx",lineNumber:251,columnNumber:5},void 0)},ke="_mcpContainer_1uhai_5",Be="_mcpHeader_1uhai_27",ze="_mcpTitle_1uhai_45",Oe="_mcpIcon_1uhai_63",Ge="_statusBadge_1uhai_71",Ue="_statusIcon_1uhai_91",Le="_statusText_1uhai_99",$e="_configSection_1uhai_111",qe="_capabilitiesSection_1uhai_113",Fe="_resultsSection_1uhai_115",He="_instructionsSection_1uhai_117",Je="_sectionTitle_1uhai_135",We="_configForm_1uhai_155",Ke="_inputGroup_1uhai_167",Ze="_inputLabel_1uhai_179",Ye="_configInput_1uhai_191",Qe="_checkboxGroup_1uhai_235",Xe="_checkboxLabel_1uhai_249",eo="_checkbox_1uhai_235",oo="_configActions_1uhai_277",ao="_saveButton_1uhai_289",ro="_testButton_1uhai_291",so="_testMessageButton_1uhai_293",no="_capabilitiesList_1uhai_353",to="_capabilityItem_1uhai_365",io="_capabilityIcon_1uhai_379",co="_capabilityText_1uhai_405",lo="_successResults_1uhai_417",mo="_errorResults_1uhai_419",uo="_resultItem_1uhai_431",bo="_testMessageResult_1uhai_451",po="_responseMetadata_1uhai_491",vo="_instructionsList_1uhai_507",ho="_instructionStep_1uhai_519",No="_stepNumber_1uhai_531",go="_stepContent_1uhai_557",jo="_statusFooter_1uhai_593",fo="_integrationInfo_1uhai_611",xo="_integrationIcon_1uhai_623",Do="_loadingIndicator_1uhai_655",Ao="_spinner_1uhai_669",Co="_envInfo_1uhai_971",Po="_envStatus_1uhai_987",_o="_envList_1uhai_995",Io="_envItem_1uhai_1009",yo="_envSet_1uhai_1029",Eo="_envNotSet_1uhai_1039",Ro="_envInstructions_1uhai_1049",Mo="_envNote_1uhai_1115";var Vo={};const So=({onStatusChange:o,className:a})=>{const[r,s]=c.useState("disconnected"),[n,t]=c.useState({endpoint:"",apiKey:"",webhookUrl:"",enabled:!1}),[i,d]=c.useState(null),[l,m]=c.useState(!1),u={endpoint:Vo.REACT_APP_MCP_ENDPOINT||"https://your-n8n-instance.com/webhook/mcp-integration",knowledgeBaseUrl:Vo.REACT_APP_MCP_KNOWLEDGE_BASE_URL||"https://your-n8n-instance.com/webhook/knowledge-base",apiKey:Vo.REACT_APP_MCP_API_KEY||"",enabled:"true"===Vo.REACT_APP_MCP_ENABLED,description:"Endpoint para integração com N8n e base de conhecimento sobre TEA/TDAH",capabilities:["Análise de dados comportamentais","Recomendações terapêuticas","Base de conhecimento TEA/TDAH","Respostas contextualizadas","Integração com dashboard"]};c.useEffect(()=>{const e={endpoint:Vo.REACT_APP_MCP_ENDPOINT||"",apiKey:Vo.REACT_APP_MCP_API_KEY||"",webhookUrl:Vo.REACT_APP_MCP_KNOWLEDGE_BASE_URL||"",enabled:"true"===Vo.REACT_APP_MCP_ENABLED};if(e.endpoint)t(e),e.enabled&&e.endpoint&&b(e);else{const e=localStorage.getItem("mcp_config");if(e)try{const o=JSON.parse(e);t(o),o.enabled&&o.endpoint&&b(o)}catch(o){}}},[]),c.useEffect(()=>{o&&o(r,n)},[r,n,o]);const b=async(e=n)=>{if(e.endpoint){m(!0),s("connecting");try{(new Date).toISOString();await new Promise(e=>setTimeout(e,2e3));const e={status:"success",capabilities:u.capabilities,knowledgeBase:{tea_entries:1247,tdah_entries:893,strategies_count:456,last_updated:(new Date).toISOString()}};d(e),s("connected")}catch(o){s("error"),d({error:o.message})}finally{m(!1)}}else s("not_configured")};return e.jsxDEV("div",{className:`${ke} ${a||""}`,children:[e.jsxDEV("div",{className:Be,children:[e.jsxDEV("h3",{className:ze,children:[e.jsxDEV("span",{className:Oe,children:"🔗"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:199,columnNumber:11},void 0),"Integração MCP/N8n"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:198,columnNumber:9},void 0),e.jsxDEV("div",{className:Ge,children:[e.jsxDEV("span",{className:Ue,children:(()=>{switch(r){case"connected":return"🟢";case"connecting":return"🟡";case"error":return"🔴";case"not_configured":return"⚫";default:return"⚪"}})()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:203,columnNumber:11},void 0),e.jsxDEV("span",{className:Le,children:(()=>{switch(r){case"connected":return"Conectado e Pronto";case"connecting":return"Conectando...";case"error":return"Erro de Conexão";case"not_configured":return"Não Configurado";default:return"Desconectado"}})()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:204,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:202,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:197,columnNumber:7},void 0),e.jsxDEV("div",{className:$e,children:[e.jsxDEV("h4",{className:Je,children:"⚙️ Configuração"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:210,columnNumber:9},void 0),e.jsxDEV("div",{className:Co,children:[e.jsxDEV("div",{className:Po,children:[e.jsxDEV("strong",{children:"📋 Status das Variáveis de Ambiente:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:215,columnNumber:13},void 0),e.jsxDEV("div",{className:_o,children:[e.jsxDEV("div",{className:`${Io} ${Vo.REACT_APP_MCP_ENDPOINT?yo:Eo}`,children:[e.jsxDEV("span",{children:"REACT_APP_MCP_ENDPOINT:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:218,columnNumber:17},void 0),e.jsxDEV("span",{children:Vo.REACT_APP_MCP_ENDPOINT?"✅ Configurado":"❌ Não definido"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:219,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:217,columnNumber:15},void 0),e.jsxDEV("div",{className:`${Io} ${Vo.REACT_APP_MCP_ENABLED?yo:Eo}`,children:[e.jsxDEV("span",{children:"REACT_APP_MCP_ENABLED:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:222,columnNumber:17},void 0),e.jsxDEV("span",{children:Vo.REACT_APP_MCP_ENABLED||"❌ Não definido"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:223,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:221,columnNumber:15},void 0),e.jsxDEV("div",{className:`${Io} ${Vo.REACT_APP_MCP_API_KEY?yo:Eo}`,children:[e.jsxDEV("span",{children:"REACT_APP_MCP_API_KEY:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:226,columnNumber:17},void 0),e.jsxDEV("span",{children:Vo.REACT_APP_MCP_API_KEY?"✅ Configurada":"⚠️ Opcional"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:227,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:225,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:216,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:214,columnNumber:11},void 0),e.jsxDEV("div",{className:Ro,children:[e.jsxDEV("p",{children:e.jsxDEV("strong",{children:"💡 Para configurar via .env:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:233,columnNumber:16},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:233,columnNumber:13},void 0),e.jsxDEV("ol",{children:[e.jsxDEV("li",{children:["Edite o arquivo ",e.jsxDEV("code",{children:".env"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:235,columnNumber:35},void 0)," na raiz do projeto"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:235,columnNumber:15},void 0),e.jsxDEV("li",{children:["Defina ",e.jsxDEV("code",{children:"REACT_APP_MCP_ENDPOINT"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:236,columnNumber:26},void 0)," com sua URL N8n"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:236,columnNumber:15},void 0),e.jsxDEV("li",{children:["Configure ",e.jsxDEV("code",{children:"REACT_APP_MCP_ENABLED=true"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:237,columnNumber:29},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:237,columnNumber:15},void 0),e.jsxDEV("li",{children:"Reinicie o servidor de desenvolvimento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:238,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:234,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:232,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:213,columnNumber:9},void 0),e.jsxDEV("div",{className:We,children:[e.jsxDEV("div",{className:Ke,children:[e.jsxDEV("label",{className:Ze,children:"Endpoint N8n:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:245,columnNumber:13},void 0),e.jsxDEV("input",{type:"url",value:n.endpoint,onChange:e=>t(o=>({...o,endpoint:e.target.value})),placeholder:u.endpoint,className:Ye,disabled:!!Vo.REACT_APP_MCP_ENDPOINT},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:246,columnNumber:13},void 0),Vo.REACT_APP_MCP_ENDPOINT&&e.jsxDEV("small",{className:Mo,children:"🔒 Configurado via environment variable"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:255,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:244,columnNumber:11},void 0),e.jsxDEV("div",{className:Ke,children:[e.jsxDEV("label",{className:Ze,children:"API Key (Opcional):"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:260,columnNumber:13},void 0),e.jsxDEV("input",{type:"password",value:n.apiKey,onChange:e=>t(o=>({...o,apiKey:e.target.value})),placeholder:"Sua chave de API N8n",className:Ye,disabled:!!Vo.REACT_APP_MCP_API_KEY},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:261,columnNumber:13},void 0),Vo.REACT_APP_MCP_API_KEY&&e.jsxDEV("small",{className:Mo,children:"🔒 Configurada via environment variable"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:270,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:259,columnNumber:11},void 0),e.jsxDEV("div",{className:Ke,children:[e.jsxDEV("label",{className:Ze,children:"Webhook URL (Opcional):"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:275,columnNumber:13},void 0),e.jsxDEV("input",{type:"url",value:n.webhookUrl,onChange:e=>t(o=>({...o,webhookUrl:e.target.value})),placeholder:"URL para receber notificações",className:Ye},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:276,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:274,columnNumber:11},void 0),e.jsxDEV("div",{className:Qe,children:e.jsxDEV("label",{className:Xe,children:[e.jsxDEV("input",{type:"checkbox",checked:n.enabled,onChange:e=>t(o=>({...o,enabled:e.target.checked})),className:eo},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:287,columnNumber:15},void 0),"Habilitar integração MCP"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:286,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:285,columnNumber:11},void 0),e.jsxDEV("div",{className:oo,children:[e.jsxDEV("button",{onClick:()=>{try{localStorage.setItem("mcp_config",JSON.stringify(n)),b()}catch(e){}},className:ao,disabled:l,children:"💾 Salvar Configuração"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:298,columnNumber:13},void 0),e.jsxDEV("button",{onClick:()=>b(),className:ro,disabled:l||!n.endpoint,children:"🔄 Testar Conexão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:306,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:297,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:243,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:209,columnNumber:7},void 0),e.jsxDEV("div",{className:qe,children:[e.jsxDEV("h4",{className:Je,children:"🚀 Capacidades MCP"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:319,columnNumber:9},void 0),e.jsxDEV("div",{className:no,children:u.capabilities.map((o,a)=>e.jsxDEV("div",{className:to,children:[e.jsxDEV("span",{className:io,children:"✓"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:323,columnNumber:15},void 0),e.jsxDEV("span",{className:co,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:324,columnNumber:15},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:322,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:320,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:318,columnNumber:7},void 0),i&&e.jsxDEV("div",{className:Fe,children:[e.jsxDEV("h4",{className:Je,children:"📊 Resultados do Teste"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:333,columnNumber:11},void 0),"success"===i.status&&e.jsxDEV("div",{className:lo,children:[e.jsxDEV("div",{className:uo,children:[e.jsxDEV("strong",{children:"Status:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:338,columnNumber:17},void 0)," ✅ Conexão estabelecida"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:337,columnNumber:15},void 0),e.jsxDEV("div",{className:uo,children:[e.jsxDEV("strong",{children:"Base de Conhecimento TEA:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:341,columnNumber:17},void 0)," ",i.knowledgeBase?.tea_entries||0," entradas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:340,columnNumber:15},void 0),e.jsxDEV("div",{className:uo,children:[e.jsxDEV("strong",{children:"Base de Conhecimento TDAH:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:344,columnNumber:17},void 0)," ",i.knowledgeBase?.tdah_entries||0," entradas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:343,columnNumber:15},void 0),e.jsxDEV("div",{className:uo,children:[e.jsxDEV("strong",{children:"Estratégias Disponíveis:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:347,columnNumber:17},void 0)," ",i.knowledgeBase?.strategies_count||0]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:346,columnNumber:15},void 0),"connected"===r&&e.jsxDEV("button",{onClick:async()=>{if("connected"===r){m(!0);try{(new Date).toISOString();await new Promise(e=>setTimeout(e,1500));const e={response:"Conexão estabelecida com sucesso! Base de conhecimento TEA/TDAH carregada e pronta para responder sobre desenvolvimento neurodivergente.",confidence:.95,knowledge_sources:["TEA Clinical Guidelines","TDAH Treatment Protocols","Neurodevelopment Research"]};d(o=>({...o,test_message:e}))}catch(e){}finally{m(!1)}}},className:so,disabled:l,children:"💬 Enviar Mensagem de Teste"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:351,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:336,columnNumber:13},void 0),i.test_message&&e.jsxDEV("div",{className:bo,children:[e.jsxDEV("h5",{children:"Resposta do Teste:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:364,columnNumber:15},void 0),e.jsxDEV("p",{children:i.test_message.response},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:365,columnNumber:15},void 0),e.jsxDEV("div",{className:po,children:[e.jsxDEV("span",{children:["Confiança: ",Math.round(100*i.test_message.confidence),"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:367,columnNumber:17},void 0),e.jsxDEV("span",{children:["Fontes: ",i.test_message.knowledge_sources?.length||0]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:368,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:366,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:363,columnNumber:13},void 0),i.error&&e.jsxDEV("div",{className:mo,children:[e.jsxDEV("strong",{children:"Erro:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:375,columnNumber:15},void 0)," ",i.error]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:374,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:332,columnNumber:9},void 0),e.jsxDEV("div",{className:He,children:[e.jsxDEV("h4",{className:Je,children:"📖 Instruções de Setup"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:383,columnNumber:9},void 0),e.jsxDEV("div",{className:vo,children:[e.jsxDEV("div",{className:ho,children:[e.jsxDEV("span",{className:No,children:"1"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:386,columnNumber:13},void 0),e.jsxDEV("div",{className:go,children:[e.jsxDEV("strong",{children:"Configure seu N8n:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:388,columnNumber:15},void 0),e.jsxDEV("p",{children:"Crie um workflow no N8n com webhook para receber dados do dashboard"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:389,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:387,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:385,columnNumber:11},void 0),e.jsxDEV("div",{className:ho,children:[e.jsxDEV("span",{className:No,children:"2"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:394,columnNumber:13},void 0),e.jsxDEV("div",{className:go,children:[e.jsxDEV("strong",{children:"Adicione a base de conhecimento:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:396,columnNumber:15},void 0),e.jsxDEV("p",{children:"Importe dados sobre TEA, TDAH e neurodivergência no seu MCP"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:397,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:395,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:393,columnNumber:11},void 0),e.jsxDEV("div",{className:ho,children:[e.jsxDEV("span",{className:No,children:"3"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:402,columnNumber:13},void 0),e.jsxDEV("div",{className:go,children:[e.jsxDEV("strong",{children:"Configure o endpoint:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:404,columnNumber:15},void 0),e.jsxDEV("p",{children:'Cole o URL do webhook N8n no campo "Endpoint N8n" acima'},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:405,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:403,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:401,columnNumber:11},void 0),e.jsxDEV("div",{className:ho,children:[e.jsxDEV("span",{className:No,children:"4"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:410,columnNumber:13},void 0),e.jsxDEV("div",{className:go,children:[e.jsxDEV("strong",{children:"Teste a integração:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:412,columnNumber:15},void 0),e.jsxDEV("p",{children:'Use os botões "Testar Conexão" e "Enviar Mensagem de Teste"'},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:413,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:411,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:409,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:384,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:382,columnNumber:7},void 0),e.jsxDEV("div",{className:jo,children:[e.jsxDEV("div",{className:fo,children:[e.jsxDEV("span",{className:xo,children:"🔗"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:422,columnNumber:11},void 0),e.jsxDEV("div",{children:[e.jsxDEV("strong",{children:"MCP Integration Ready"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:424,columnNumber:13},void 0),e.jsxDEV("p",{children:"Preparado para conectar com sua instância N8n"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:425,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:423,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:421,columnNumber:9},void 0),l&&e.jsxDEV("div",{className:Do,children:[e.jsxDEV("span",{className:Ao},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:430,columnNumber:13},void 0),e.jsxDEV("span",{children:"Processando..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:431,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:429,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:420,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/MCPIntegration.jsx",lineNumber:195,columnNumber:5},void 0)},To={unifiedContainer:"_unifiedContainer_1shsg_15",dashboardTabs:"_dashboardTabs_1shsg_33",tabsContainer:"_tabsContainer_1shsg_57",dashboardTab:"_dashboardTab_1shsg_33",active:"_active_1shsg_115",tabIcon:"_tabIcon_1shsg_127",tabTitle:"_tabTitle_1shsg_135",filtersSection:"_filtersSection_1shsg_145",filterGroup:"_filterGroup_1shsg_163",filterLabel:"_filterLabel_1shsg_175",filterSelect:"_filterSelect_1shsg_189",dashboardContent:"_dashboardContent_1shsg_225",contentHeader:"_contentHeader_1shsg_235",contentTitle:"_contentTitle_1shsg_243",contentIcon:"_contentIcon_1shsg_263",contentDescription:"_contentDescription_1shsg_271",contentBody:"_contentBody_1shsg_283",overviewContent:"_overviewContent_1shsg_293",metricsGrid:"_metricsGrid_1shsg_305",metricCard:"_metricCard_1shsg_317",metricIcon:"_metricIcon_1shsg_345",metricValue:"_metricValue_1shsg_357",metricLabel:"_metricLabel_1shsg_371",keyMetricsSection:"_keyMetricsSection_1shsg_385",sectionTitle:"_sectionTitle_1shsg_399",keyMetricsList:"_keyMetricsList_1shsg_419",keyMetricItem:"_keyMetricItem_1shsg_431",metricName:"_metricName_1shsg_451",metricProgress:"_metricProgress_1shsg_461",progressBar:"_progressBar_1shsg_477",metricValueText:"_metricValueText_1shsg_491",trendIcon:"_trendIcon_1shsg_503",behavioralContent:"_behavioralContent_1shsg_513",patternsSection:"_patternsSection_1shsg_525",adaptationsSection:"_adaptationsSection_1shsg_527",patternsList:"_patternsList_1shsg_541",patternCard:"_patternCard_1shsg_553",patternType:"_patternType_1shsg_567",patternDetails:"_patternDetails_1shsg_579",adaptationsList:"_adaptationsList_1shsg_593",adaptationItem:"_adaptationItem_1shsg_605",adaptationIcon:"_adaptationIcon_1shsg_625",adaptationText:"_adaptationText_1shsg_635",gamesContent:"_gamesContent_1shsg_647",gamesGrid:"_gamesGrid_1shsg_659",gameStatsCard:"_gameStatsCard_1shsg_671",favoriteGamesList:"_favoriteGamesList_1shsg_699",favoriteGame:"_favoriteGame_1shsg_699",gameIcon:"_gameIcon_1shsg_729",difficultyInfo:"_difficultyInfo_1shsg_737",achievementsInfo:"_achievementsInfo_1shsg_739",therapeuticContent:"_therapeuticContent_1shsg_757",goalsSection:"_goalsSection_1shsg_769",interventionsSection:"_interventionsSection_1shsg_771",goalsList:"_goalsList_1shsg_785",interventionsList:"_interventionsList_1shsg_787",goalItem:"_goalItem_1shsg_799",interventionItem:"_interventionItem_1shsg_801",goalIcon:"_goalIcon_1shsg_821",interventionIcon:"_interventionIcon_1shsg_823",goalText:"_goalText_1shsg_833",interventionText:"_interventionText_1shsg_835",progressContent:"_progressContent_1shsg_847",growthSection:"_growthSection_1shsg_859",milestonesSection:"_milestonesSection_1shsg_861",growthBars:"_growthBars_1shsg_875",growthBar:"_growthBar_1shsg_875",skillName:"_skillName_1shsg_901",growthBarContainer:"_growthBarContainer_1shsg_911",growthBarFill:"_growthBarFill_1shsg_927",growthValue:"_growthValue_1shsg_941",milestonesList:"_milestonesList_1shsg_953",milestoneItem:"_milestoneItem_1shsg_965",milestoneIcon:"_milestoneIcon_1shsg_985",milestoneInfo:"_milestoneInfo_1shsg_995",milestoneSkill:"_milestoneSkill_1shsg_1003",milestoneDetails:"_milestoneDetails_1shsg_1015",sensoryContent:"_sensoryContent_1shsg_1027",sensoryProfile:"_sensoryProfile_1shsg_1039",strategiesSection:"_strategiesSection_1shsg_1041",sensoryGrid:"_sensoryGrid_1shsg_1055",sensoryItem:"_sensoryItem_1shsg_1067",sensoryName:"_sensoryName_1shsg_1083",sensoryLevel:"_sensoryLevel_1shsg_1095",hiperresponsivo:"_hiperresponsivo_1shsg_1111","típico":"_típico_1shsg_1121",hiporesponsivo:"_hiporesponsivo_1shsg_1131",buscasensorial:"_buscasensorial_1shsg_1141",strategiesList:"_strategiesList_1shsg_1151",strategyItem:"_strategyItem_1shsg_1163",strategyIcon:"_strategyIcon_1shsg_1183",strategyText:"_strategyText_1shsg_1193"},wo=({dashboardData:o,className:a,viewMode:r="standard"})=>{const[s,n]=c.useState("overview"),[t,i]=c.useState("default"),[d,l]=c.useState({timeRange:"30d",userId:"all",activityType:"all"}),m={overview:{title:"Visão Geral",icon:"📊",description:"Panorama completo de todas as métricas",color:"#667eea"},behavioral:{title:"Análises Comportamentais",icon:"🧠",description:"Padrões comportamentais e cognitivos",color:"#48bb78"},games:{title:"Métricas de Jogos",icon:"🎮",description:"Performance e progresso nos jogos",color:"#ed8936"},therapeutic:{title:"Relatórios Terapêuticos",icon:"🏥",description:"Avaliações e planos terapêuticos",color:"#9f7aea"},progress:{title:"Progressão de Atividades",icon:"📈",description:"Evolução temporal das habilidades",color:"#e91e63"},sensory:{title:"Integração Multissensorial",icon:"🌈",description:"Análises sensoriais avançadas",color:"#607d8b"}},[u,b]=c.useState({overview:{totalSessions:247,avgPerformance:82.5,improvementRate:15.8,activeGoals:12,completedActivities:156,timeSpent:"45h 32m",keyMetrics:[{name:"Atenção Sustentada",value:85,trend:"up"},{name:"Flexibilidade Cognitiva",value:78,trend:"up"},{name:"Memória de Trabalho",value:72,trend:"stable"},{name:"Controle Inibitório",value:80,trend:"up"}]},behavioral:{patterns:[{type:"Picos de Atenção",frequency:"Manhãs (9-11h)",intensity:"Alta"},{type:"Fadiga Cognitiva",frequency:"Tardes (14-16h)",intensity:"Moderada"},{type:"Engajamento Social",frequency:"Atividades em grupo",intensity:"Crescente"}],adaptations:["Sessões mais curtas no período da tarde","Pausas sensoriais a cada 15 minutos","Reforço positivo visual"]},games:{favoriteGames:["Memory Match","Color Sequence","Shape Sorting"],difficulty:{current:"Intermediário",progression:"+2 níveis"},achievements:23,streaks:{current:7,best:12}},therapeutic:{currentGoals:["Melhorar atenção sustentada (90% concluído)","Desenvolver habilidades sociais (75% concluído)","Fortalecer coordenação motora (60% concluído)"],interventions:["Terapia ocupacional - 2x/semana","Fonoaudiologia - 1x/semana","Psicopedagogia - 1x/semana"],nextSession:"2025-07-16"},progress:{monthlyGrowth:{attention:12,memory:8,executive:15,social:20},milestones:[{skill:"Sequenciamento",achieved:"2025-07-10",level:"Básico"},{skill:"Categorização",achieved:"2025-07-08",level:"Intermediário"}]},sensory:{profile:{visual:"Hiperresponsivo",auditory:"Típico",tactile:"Hiporesponsivo",vestibular:"Busca sensorial"},strategies:["Ambientes com pouca estimulação visual","Uso de fones com cancelamento de ruído","Atividades proprioceptivas regulares"]}});return e.jsxDEV("div",{className:`${To.unifiedContainer} ${a||""}`,children:[e.jsxDEV("div",{className:To.dashboardTabs,children:e.jsxDEV("div",{className:To.tabsContainer,children:Object.entries(m).map(([o,a])=>e.jsxDEV("button",{onClick:()=>n(o),className:`${To.dashboardTab} ${s===o?To.active:""}`,style:{"--tab-color":a.color},title:a.description,children:[e.jsxDEV("span",{className:To.tabIcon,children:a.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:386,columnNumber:15},void 0),e.jsxDEV("span",{className:To.tabTitle,children:a.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:387,columnNumber:15},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:379,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:377,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:376,columnNumber:7},void 0),e.jsxDEV("div",{className:To.filtersSection,children:[e.jsxDEV("div",{className:To.filterGroup,children:[e.jsxDEV("label",{className:To.filterLabel,children:"Período:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:396,columnNumber:11},void 0),e.jsxDEV("select",{value:d.timeRange,onChange:e=>l(o=>({...o,timeRange:e.target.value})),className:To.filterSelect,children:[e.jsxDEV("option",{value:"7d",children:"Últimos 7 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:402,columnNumber:13},void 0),e.jsxDEV("option",{value:"30d",children:"Últimos 30 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:403,columnNumber:13},void 0),e.jsxDEV("option",{value:"90d",children:"Últimos 90 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:404,columnNumber:13},void 0),e.jsxDEV("option",{value:"1y",children:"Último ano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:405,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:397,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:395,columnNumber:9},void 0),e.jsxDEV("div",{className:To.filterGroup,children:[e.jsxDEV("label",{className:To.filterLabel,children:"Usuário:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:410,columnNumber:11},void 0),e.jsxDEV("select",{value:d.userId,onChange:e=>l(o=>({...o,userId:e.target.value})),className:To.filterSelect,children:[e.jsxDEV("option",{value:"all",children:"Todos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:416,columnNumber:13},void 0),e.jsxDEV("option",{value:"current",children:"Usuário Atual"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:417,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:411,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:409,columnNumber:9},void 0),e.jsxDEV("div",{className:To.filterGroup,children:[e.jsxDEV("label",{className:To.filterLabel,children:"Tipo de Atividade:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:422,columnNumber:11},void 0),e.jsxDEV("select",{value:d.activityType,onChange:e=>l(o=>({...o,activityType:e.target.value})),className:To.filterSelect,children:[e.jsxDEV("option",{value:"all",children:"Todas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:428,columnNumber:13},void 0),e.jsxDEV("option",{value:"games",children:"Jogos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:429,columnNumber:13},void 0),e.jsxDEV("option",{value:"exercises",children:"Exercícios"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:430,columnNumber:13},void 0),e.jsxDEV("option",{value:"assessments",children:"Avaliações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:431,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:423,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:421,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:394,columnNumber:7},void 0),e.jsxDEV("div",{className:To.dashboardContent,children:[e.jsxDEV("div",{className:To.contentHeader,children:[e.jsxDEV("h3",{className:To.contentTitle,children:[e.jsxDEV("span",{className:To.contentIcon,children:m[s].icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:440,columnNumber:13},void 0),m[s].title]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:439,columnNumber:11},void 0),e.jsxDEV("p",{className:To.contentDescription,children:m[s].description},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:445,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:438,columnNumber:9},void 0),e.jsxDEV("div",{className:To.contentBody,children:(()=>{const o=u[s];switch(s){case"overview":return e.jsxDEV("div",{className:To.overviewContent,children:[e.jsxDEV("div",{className:To.metricsGrid,children:[e.jsxDEV("div",{className:To.metricCard,children:[e.jsxDEV("div",{className:To.metricIcon,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:150,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricValue,children:o.totalSessions},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:151,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricLabel,children:"Sessões Totais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:152,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:149,columnNumber:15},void 0),e.jsxDEV("div",{className:To.metricCard,children:[e.jsxDEV("div",{className:To.metricIcon,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:155,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricValue,children:[o.avgPerformance,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:156,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricLabel,children:"Performance Média"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:157,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:154,columnNumber:15},void 0),e.jsxDEV("div",{className:To.metricCard,children:[e.jsxDEV("div",{className:To.metricIcon,children:"📈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:160,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricValue,children:["+",o.improvementRate,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:161,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricLabel,children:"Melhoria Mensal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:162,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:159,columnNumber:15},void 0),e.jsxDEV("div",{className:To.metricCard,children:[e.jsxDEV("div",{className:To.metricIcon,children:"✅"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:165,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricValue,children:o.activeGoals},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:166,columnNumber:17},void 0),e.jsxDEV("div",{className:To.metricLabel,children:"Objetivos Ativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:167,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:164,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:148,columnNumber:13},void 0),e.jsxDEV("div",{className:To.keyMetricsSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Métricas Principais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:172,columnNumber:15},void 0),e.jsxDEV("div",{className:To.keyMetricsList,children:o.keyMetrics.map((o,a)=>e.jsxDEV("div",{className:To.keyMetricItem,children:[e.jsxDEV("div",{className:To.metricName,children:o.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:176,columnNumber:21},void 0),e.jsxDEV("div",{className:To.metricProgress,children:e.jsxDEV("div",{className:To.progressBar,style:{width:`${o.value}%`}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:178,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:177,columnNumber:21},void 0),e.jsxDEV("div",{className:To.metricValueText,children:[o.value,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:183,columnNumber:21},void 0),e.jsxDEV("div",{className:`${To.trendIcon} ${To[o.trend]}`,children:["up"===o.trend&&"↗️","down"===o.trend&&"↘️","stable"===o.trend&&"➡️"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:184,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:175,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:173,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:171,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:147,columnNumber:11},void 0);case"behavioral":return e.jsxDEV("div",{className:To.behavioralContent,children:[e.jsxDEV("div",{className:To.patternsSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Padrões Identificados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:200,columnNumber:15},void 0),e.jsxDEV("div",{className:To.patternsList,children:o.patterns.map((o,a)=>e.jsxDEV("div",{className:To.patternCard,children:[e.jsxDEV("div",{className:To.patternType,children:o.type},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:204,columnNumber:21},void 0),e.jsxDEV("div",{className:To.patternDetails,children:[e.jsxDEV("span",{children:["📅 ",o.frequency]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:206,columnNumber:23},void 0),e.jsxDEV("span",{children:["⚡ ",o.intensity]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:207,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:205,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:203,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:201,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:199,columnNumber:13},void 0),e.jsxDEV("div",{className:To.adaptationsSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Adaptações Recomendadas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:215,columnNumber:15},void 0),e.jsxDEV("div",{className:To.adaptationsList,children:o.adaptations.map((o,a)=>e.jsxDEV("div",{className:To.adaptationItem,children:[e.jsxDEV("span",{className:To.adaptationIcon,children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:219,columnNumber:21},void 0),e.jsxDEV("span",{className:To.adaptationText,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:220,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:218,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:216,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:214,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:198,columnNumber:11},void 0);case"games":return e.jsxDEV("div",{className:To.gamesContent,children:e.jsxDEV("div",{className:To.gamesGrid,children:[e.jsxDEV("div",{className:To.gameStatsCard,children:[e.jsxDEV("h4",{children:"Jogos Favoritos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:233,columnNumber:17},void 0),e.jsxDEV("div",{className:To.favoriteGamesList,children:o.favoriteGames.map((o,a)=>e.jsxDEV("div",{className:To.favoriteGame,children:[e.jsxDEV("span",{className:To.gameIcon,children:"🎮"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:237,columnNumber:23},void 0),e.jsxDEV("span",{children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:238,columnNumber:23},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:236,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:234,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:232,columnNumber:15},void 0),e.jsxDEV("div",{className:To.gameStatsCard,children:[e.jsxDEV("h4",{children:"Progressão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:245,columnNumber:17},void 0),e.jsxDEV("div",{className:To.difficultyInfo,children:[e.jsxDEV("div",{children:["Nível Atual: ",e.jsxDEV("strong",{children:o.difficulty.current},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:247,columnNumber:37},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:247,columnNumber:19},void 0),e.jsxDEV("div",{children:["Evolução: ",e.jsxDEV("strong",{children:o.difficulty.progression},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:248,columnNumber:34},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:248,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:246,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:244,columnNumber:15},void 0),e.jsxDEV("div",{className:To.gameStatsCard,children:[e.jsxDEV("h4",{children:"Conquistas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:253,columnNumber:17},void 0),e.jsxDEV("div",{className:To.achievementsInfo,children:[e.jsxDEV("div",{children:["🏆 ",o.achievements," conquistas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:255,columnNumber:19},void 0),e.jsxDEV("div",{children:["🔥 Sequência atual: ",o.streaks.current," dias"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:256,columnNumber:19},void 0),e.jsxDEV("div",{children:["⭐ Melhor sequência: ",o.streaks.best," dias"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:257,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:254,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:252,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:231,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:230,columnNumber:11},void 0);case"therapeutic":return e.jsxDEV("div",{className:To.therapeuticContent,children:[e.jsxDEV("div",{className:To.goalsSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Objetivos Atuais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:268,columnNumber:15},void 0),e.jsxDEV("div",{className:To.goalsList,children:o.currentGoals.map((o,a)=>e.jsxDEV("div",{className:To.goalItem,children:[e.jsxDEV("span",{className:To.goalIcon,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:272,columnNumber:21},void 0),e.jsxDEV("span",{className:To.goalText,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:273,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:271,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:269,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:267,columnNumber:13},void 0),e.jsxDEV("div",{className:To.interventionsSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Intervenções Ativas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:280,columnNumber:15},void 0),e.jsxDEV("div",{className:To.interventionsList,children:o.interventions.map((o,a)=>e.jsxDEV("div",{className:To.interventionItem,children:[e.jsxDEV("span",{className:To.interventionIcon,children:"🏥"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:284,columnNumber:21},void 0),e.jsxDEV("span",{className:To.interventionText,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:285,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:283,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:281,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:279,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:266,columnNumber:11},void 0);case"progress":return e.jsxDEV("div",{className:To.progressContent,children:[e.jsxDEV("div",{className:To.growthSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Crescimento Mensal (%)"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:297,columnNumber:15},void 0),e.jsxDEV("div",{className:To.growthBars,children:Object.entries(o.monthlyGrowth).map(([o,a])=>e.jsxDEV("div",{className:To.growthBar,children:[e.jsxDEV("div",{className:To.skillName,children:o.charAt(0).toUpperCase()+o.slice(1)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:301,columnNumber:21},void 0),e.jsxDEV("div",{className:To.growthBarContainer,children:e.jsxDEV("div",{className:To.growthBarFill,style:{width:5*a+"%"}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:305,columnNumber:23},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:304,columnNumber:21},void 0),e.jsxDEV("div",{className:To.growthValue,children:["+",a,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:310,columnNumber:21},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:300,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:298,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:296,columnNumber:13},void 0),e.jsxDEV("div",{className:To.milestonesSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Marcos Recentes"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:317,columnNumber:15},void 0),e.jsxDEV("div",{className:To.milestonesList,children:o.milestones.map((o,a)=>e.jsxDEV("div",{className:To.milestoneItem,children:[e.jsxDEV("div",{className:To.milestoneIcon,children:"🎉"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:321,columnNumber:21},void 0),e.jsxDEV("div",{className:To.milestoneInfo,children:[e.jsxDEV("div",{className:To.milestoneSkill,children:o.skill},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:323,columnNumber:23},void 0),e.jsxDEV("div",{className:To.milestoneDetails,children:[o.level," • ",o.achieved]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:324,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:322,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:320,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:318,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:316,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:295,columnNumber:11},void 0);case"sensory":return e.jsxDEV("div",{className:To.sensoryContent,children:[e.jsxDEV("div",{className:To.sensoryProfile,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Perfil Sensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:339,columnNumber:15},void 0),e.jsxDEV("div",{className:To.sensoryGrid,children:Object.entries(o.profile).map(([o,a])=>e.jsxDEV("div",{className:To.sensoryItem,children:[e.jsxDEV("div",{className:To.sensoryName,children:o.charAt(0).toUpperCase()+o.slice(1)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:343,columnNumber:21},void 0),e.jsxDEV("div",{className:`${To.sensoryLevel} ${To[a.toLowerCase().replace(" ","")]}`,children:a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:346,columnNumber:21},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:342,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:340,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:338,columnNumber:13},void 0),e.jsxDEV("div",{className:To.strategiesSection,children:[e.jsxDEV("h4",{className:To.sectionTitle,children:"Estratégias Recomendadas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:355,columnNumber:15},void 0),e.jsxDEV("div",{className:To.strategiesList,children:o.strategies.map((o,a)=>e.jsxDEV("div",{className:To.strategyItem,children:[e.jsxDEV("span",{className:To.strategyIcon,children:"🌈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:359,columnNumber:21},void 0),e.jsxDEV("span",{className:To.strategyText,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:360,columnNumber:21},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:358,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:356,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:354,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:337,columnNumber:11},void 0);default:return e.jsxDEV("div",{children:"Dashboard em desenvolvimento..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:369,columnNumber:16},void 0)}})()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:450,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:437,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/components/UnifiedDashboard.jsx",lineNumber:374,columnNumber:5},void 0)},ko={dashboardContainer:"_dashboardContainer_1enjx_15",loadingContainer:"_loadingContainer_1enjx_37",errorState:"_errorState_1enjx_55",dashboardHeader:"_dashboardHeader_1enjx_81",headerLeft:"_headerLeft_1enjx_101",dashboardTitle:"_dashboardTitle_1enjx_109",dashboardSubtitle:"_dashboardSubtitle_1enjx_137",titleIcon:"_titleIcon_1enjx_151",dashboardControls:"_dashboardControls_1enjx_169",analysisSelector:"_analysisSelector_1enjx_183",timeframeSelector:"_timeframeSelector_1enjx_185",chatButton:"_chatButton_1enjx_187",mcpButton:"_mcpButton_1enjx_189",refreshButton:"_refreshButton_1enjx_191",active:"_active_1enjx_253",statusBar:"_statusBar_1enjx_269",statusItem:"_statusItem_1enjx_291",statusIcon:"_statusIcon_1enjx_307",connected:"_connected_1enjx_315",disconnected:"_disconnected_1enjx_323",mcpSection:"_mcpSection_1enjx_333",ieBrandSection:"_ieBrandSection_1enjx_341",aiChatComponent:"_aiChatComponent_1enjx_349",dashboardTypeControls:"_dashboardTypeControls_1enjx_417",typeButton:"_typeButton_1enjx_431",typeIcon:"_typeIcon_1enjx_479",dashboardModeControls:"_dashboardModeControls_1enjx_489",modeButton:"_modeButton_1enjx_509",modeIcon:"_modeIcon_1enjx_557",unifiedDashboardWrapper:"_unifiedDashboardWrapper_1enjx_567",dashboardPlaceholder:"_dashboardPlaceholder_1enjx_617",placeholderIcon:"_placeholderIcon_1enjx_639",dashboardComponent:"_dashboardComponent_1enjx_649",metricsGrid:"_metricsGrid_1enjx_659",metricCard:"_metricCard_1enjx_673",metricHeader:"_metricHeader_1enjx_707",metricTitle:"_metricTitle_1enjx_721",metricIcon:"_metricIcon_1enjx_735",style:"_style_1enjx_757",strengths:"_strengths_1enjx_765",milestone:"_milestone_1enjx_773",recommendations:"_recommendations_1enjx_781",metricValue:"_metricValue_1enjx_789",metricTrend:"_metricTrend_1enjx_805",trendPositive:"_trendPositive_1enjx_821",chartsGrid:"_chartsGrid_1enjx_831",chartCard:"_chartCard_1enjx_845",chartTitle:"_chartTitle_1enjx_875",chartContainer:"_chartContainer_1enjx_895",insightsSection:"_insightsSection_1enjx_907",insightsTitle:"_insightsTitle_1enjx_925",insightsGrid:"_insightsGrid_1enjx_945",insightCard:"_insightCard_1enjx_957",insightTitle:"_insightTitle_1enjx_985",insightContent:"_insightContent_1enjx_1005",recommendationsSection:"_recommendationsSection_1enjx_1027",recommendationsTitle:"_recommendationsTitle_1enjx_1045",recommendationsGrid:"_recommendationsGrid_1enjx_1065",recommendationCard:"_recommendationCard_1enjx_1077",recommendationIcon:"_recommendationIcon_1enjx_1111",recommendationContent:"_recommendationContent_1enjx_1123",aiInsightsSection:"_aiInsightsSection_1enjx_1147",aiInsightsTitle:"_aiInsightsTitle_1enjx_1165",aiInsightsGrid:"_aiInsightsGrid_1enjx_1185",aiInsightCard:"_aiInsightCard_1enjx_1197",aiInsightIcon:"_aiInsightIcon_1enjx_1233",aiInsightContent:"_aiInsightContent_1enjx_1245"},Bo=c.lazy(()=>s(()=>import("./RealTimeDashboard-CXIudvdW.js"),__vite__mapDeps([8,0,1,2,3,4,5,6,7,9]))),zo=c.lazy(()=>s(()=>Promise.resolve().then(()=>Yo),void 0)),Oo=c.lazy(()=>s(()=>Promise.resolve().then(()=>Ra),void 0)),Go=c.lazy(()=>s(()=>Promise.resolve().then(()=>ya),void 0));l.register(m,u,b,p,v,h,N,g,j,f,x);const Uo=(e,o)=>o<5?"Em avaliação":e>=85?"Analítico avançado":e>=70?"Equilibrado":e>=50?"Em desenvolvimento":"Iniciante",Lo=e=>{if(e.length<3)return"Dados insuficientes";const o={};e.forEach(e=>{const a=new Date(e.date||e.timestamp||Date.now()).getHours();o[a]=(o[a]||0)+1});const a=Object.entries(o).sort(([,e],[,o])=>o-e)[0]?.[0];if(!a)return"Padrão não identificado";const r=parseInt(a);return r>=6&&r<12?"Manhã (6h-12h)":r>=12&&r<18?"Tarde (12h-18h)":r>=18&&r<24?"Noite (18h-24h)":"Madrugada (0h-6h)"},$o=e=>{const o={"Jogo da Memória":"Visual-Espacial","Combinação de Cores":"Visual","Reconhecimento de Letras":"Linguístico","Contagem de Números":"Lógico-Matemático","Associação de Imagens":"Visual-Espacial","Pintura Criativa":"Artístico-Motor"},a={};Object.entries(e).forEach(([e,r])=>{const s=o[e]||"Geral",n=r.reduce((e,o)=>e+o,0)/r.length;a[s]=(a[s]||0)+n});const r=Object.entries(a).sort(([,e],[,o])=>o-e)[0]?.[0];return r||"Multissensorial"},qo=(e,o,a,r)=>{const s={labels:["Atenção","Memória","Lógica","Linguagem","Execução","Visual"],datasets:[{label:"Perfil Atual",data:[Math.min(Math.round(.9*r),100),Math.min(Math.round(.85*r),100),Math.min(Math.round(1.1*r),100),Math.min(Math.round(.95*r),100),Math.min(Math.round(.8*r),100),Math.min(Math.round(1.05*r),100)],backgroundColor:"rgba(150, 206, 180, 0.2)",borderColor:"#96CEB4",borderWidth:2}]},n=o.slice(-7);return{cognitive:s,progress:{labels:n.map((e,o)=>`Sessão ${o+1}`),datasets:[{label:"Evolução da Performance",data:n.map(e=>e.accuracy||100*Math.random()),borderColor:"#96CEB4",backgroundColor:"rgba(150, 206, 180, 0.1)",fill:!0,tension:.4}]},distribution:{labels:a.slice(0,5).map(e=>e.game),datasets:[{data:a.slice(0,5).map(e=>e.average),backgroundColor:["#96CEB4","#FECA57","#FF6B6B","#4834D4","#A55EEA"],borderWidth:2}]}}},Fo=()=>{const[o,a]=c.useState(!0),[r,s]=c.useState("cognitive"),[t,i]=c.useState("30d"),[d,l]=c.useState(null),[m,u]=c.useState(!1),[b,p]=c.useState("disconnected"),[v,h]=c.useState(!1),[N,g]=c.useState(!0),[j,f]=c.useState("standard"),[x,A]=c.useState("unified"),[_,I]=c.useState(null),[y,E]=c.useState(null);c.useEffect(()=>{try{const e={info:console.info,error:console.error,warn:console.warn,debug:console.debug},o=new n(e);E(o)}catch(e){}},[]);c.useEffect(()=>{(async()=>{a(!0),setTimeout(()=>{const e=(()=>{try{const o=JSON.parse(localStorage.getItem("gameScores")||"[]"),a=JSON.parse(localStorage.getItem("gameSessions")||"[]"),r=JSON.parse(localStorage.getItem("userProgress")||"{}");if(0===o.length&&0===a.length)return{analysis:{cognitiveProfile:{strengths:["Aguardando dados para análise"],improvements:["Complete algumas atividades"],dominant_style:"A ser determinado",confidence:0},learningPattern:{optimal_time:"Dados insuficientes",peak_performance:"A ser calculado",preferred_modality:"A ser identificado"}},predictions:{next_milestone:{skill:"Primeira avaliação",timeline:"Após 5+ atividades",probability:0,requirements:["Complete atividades","Mantenha consistência"]}},recommendations:["Complete pelo menos 5 atividades para análise inicial","Experimente diferentes tipos de jogos","Mantenha regularidade na prática"],insights:["Sistema aguardando dados para análise","IA será ativada após coleta de dados suficientes"],charts:{cognitive:{labels:["Atenção","Memória","Lógica","Linguagem","Execução","Visual"],datasets:[{label:"Aguardando Dados",data:[0,0,0,0,0,0],backgroundColor:"rgba(189, 189, 189, 0.2)",borderColor:"#BDBDBD"}]},progress:{labels:["Sem dados"],datasets:[{label:"Performance",data:[0],borderColor:"#BDBDBD",backgroundColor:"rgba(189, 189, 189, 0.1)"}]},distribution:{labels:["Sem dados"],datasets:[{data:[100],backgroundColor:["#BDBDBD"]}]}}};const s=a.length,n=o.length>0?o.reduce((e,o)=>e+(o.accuracy||0),0)/o.length:0,t=o.filter(e=>e.completed).length;let i=null,d=null,c=null;if(y)try{i=y.analyzeMultisensoryData({gameScores:o,gameSessions:a,userProgress:r}),d=y.processGameMetrics({gameScores:o,recentSessions:a.slice(-10)}),c=y.generateAdaptationReport({gameScores:o,userProfile:r,cognitiveProfile:{avgAccuracy:n,totalSessions:s}})}catch(e){}const l={};o.forEach(e=>{const o=e.game||"Indefinido";l[o]||(l[o]=[]),l[o].push(e.accuracy||0)});const m=Object.entries(l).map(([e,o])=>({game:e,average:o.reduce((e,o)=>e+o,0)/o.length,sessions:o.length})).sort((e,o)=>o.average-e.average),u=m.slice(0,3).map(e=>e.game),b=m.slice(-2).map(e=>e.game),p=a.filter(e=>new Date(e.date||e.timestamp||Date.now())>=new Date(Date.now()-6048e5)).length,v=d?.cognitiveProfile||{strengths:u.length>0?u:["Análise em andamento"],improvements:b.length>0?b:["Continue praticando"],dominant_style:Uo(n,s),confidence:Math.min(Math.round(3.5*s),95)},h=i?.learningPatterns||{optimal_time:Lo(a),peak_performance:`${Math.round(n)}% de precisão média`,preferred_modality:$o(l)};return{analysis:{cognitiveProfile:v,learningPattern:h,multisensoryData:i,gameMetrics:d,adaptation:c},predictions:c?.predictions||{next_milestone:{skill:b.length>0?b[0]:"Desenvolvimento geral",timeline:s<5?"2-3 semanas":"1-2 semanas",probability:Math.min(Math.round(n+15),90),requirements:["Prática regular","Foco em fundamentos"]}},recommendations:c?.recommendations||[s<5?"Complete mais atividades para análises precisas":"Continue o bom trabalho",n<60?"Foque em jogos básicos para fortalecer fundamentos":"Experimente desafios mais complexos","Mantenha consistência na prática diária"],insights:d?.insights||[`Performance atual: ${Math.round(n)}% de precisão média`,`Total de sessões realizadas: ${s}`,`Jogos completados com sucesso: ${t}`,`Atividade esta semana: ${p} sessões`,"Nível de engajamento: "+(s>20?"Alto":s>10?"Médio":"Inicial")],charts:qo(0,a,m,n),aiBrain:y}}catch(o){return{analysis:{cognitiveProfile:{strengths:["Sistema temporariamente indisponível"],improvements:["Recarregue a página"],dominant_style:"Erro no sistema",confidence:0}},predictions:{next_milestone:{skill:"Sistema em manutenção",timeline:"Indisponível",probability:0,requirements:["Tente novamente mais tarde"]}},recommendations:["Sistema temporariamente indisponível"],insights:["Sistema temporariamente indisponível"],charts:{cognitive:{labels:["Atenção","Memória","Lógica","Linguagem","Execução","Visual"],datasets:[{label:"Aguardando Dados",data:[0,0,0,0,0,0],backgroundColor:"rgba(189, 189, 189, 0.2)",borderColor:"#BDBDBD"}]},progress:{labels:["Sem dados"],datasets:[{label:"Performance",data:[0],borderColor:"#BDBDBD",backgroundColor:"rgba(189, 189, 189, 0.1)"}]},distribution:{labels:["Sem dados"],datasets:[{data:[100],backgroundColor:["#BDBDBD"]}]}}}}})();l(e);const o=JSON.parse(localStorage.getItem("gameScores")||"[]"),r=JSON.parse(localStorage.getItem("gameSessions")||"[]"),s=o.length>0?o.reduce((e,o)=>e+(o.accuracy||0),0)/o.length:0;I({avgAccuracy:s,totalSessions:r.length,gameScores:o,gameSessions:r,analysis:e.analysis,aiBrain:y,multisensoryAnalysis:e.analysis.multisensoryData,gameMetricsAnalysis:e.analysis.gameMetrics,adaptationReport:e.analysis.adaptation}),a(!1)},1500)})()},[r,t]);if(o)return e.jsxDEV("div",{className:ko.loadingContainer,children:e.jsxDEV(T,{message:"🤖 IA analisando seus dados reais..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:469,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:468,columnNumber:7},void 0);if(!d)return e.jsxDEV("div",{className:ko.errorState,children:[e.jsxDEV("h3",{children:"❌ Erro ao carregar análise"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:477,columnNumber:9},void 0),e.jsxDEV("p",{children:"Tente recarregar a página"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:478,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:476,columnNumber:7},void 0);const{analysis:R,predictions:M,recommendations:V,insights:S,charts:w}=d;return e.jsxDEV("div",{className:ko.dashboardContainer,children:[e.jsxDEV("div",{className:ko.dashboardHeader,children:[e.jsxDEV("div",{className:ko.headerLeft,children:[e.jsxDEV("h1",{className:ko.dashboardTitle,children:[e.jsxDEV("span",{className:ko.titleIcon,children:"🤖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:490,columnNumber:13},void 0),"Dashboard A - IE Brand"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:489,columnNumber:11},void 0),e.jsxDEV("p",{className:ko.dashboardSubtitle,children:"Integração com Inteligência Artificial para análise de desenvolvimento neurocognitivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:493,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:488,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.dashboardControls,children:[e.jsxDEV("select",{className:ko.analysisSelector,value:r,onChange:e=>s(e.target.value),children:[e.jsxDEV("option",{value:"cognitive",children:"Análise Cognitiva"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:504,columnNumber:13},void 0),e.jsxDEV("option",{value:"behavioral",children:"Padrões Comportamentais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:505,columnNumber:13},void 0),e.jsxDEV("option",{value:"predictive",children:"Análise Preditiva"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:506,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:499,columnNumber:11},void 0),e.jsxDEV("select",{className:ko.timeframeSelector,value:t,onChange:e=>i(e.target.value),children:[e.jsxDEV("option",{value:"7d",children:"7 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:514,columnNumber:13},void 0),e.jsxDEV("option",{value:"30d",children:"30 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:515,columnNumber:13},void 0),e.jsxDEV("option",{value:"90d",children:"90 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:516,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:509,columnNumber:11},void 0),e.jsxDEV("button",{className:`${ko.chatButton} ${m?ko.active:""}`,onClick:()=>u(!m),children:"💬 Chat IA"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:519,columnNumber:11},void 0),e.jsxDEV("button",{className:`${ko.mcpButton} ${v?ko.active:""}`,onClick:()=>h(!v),children:"🔗 MCP Config"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:526,columnNumber:11},void 0),e.jsxDEV("button",{className:`${ko.unifiedButton} ${N?ko.active:""}`,onClick:()=>g(!N),children:"📊 Dashboard Unificado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:533,columnNumber:11},void 0),e.jsxDEV("button",{className:ko.refreshButton,onClick:()=>window.location.reload(),children:"🔄 Atualizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:540,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:498,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:487,columnNumber:7},void 0),e.jsxDEV("div",{className:ko.statusBar,children:[e.jsxDEV("div",{className:ko.statusItem,children:[e.jsxDEV("span",{className:ko.statusIcon,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:552,columnNumber:11},void 0),e.jsxDEV("span",{children:"IE Brand Analytics: Ativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:553,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:551,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.statusItem,children:[e.jsxDEV("span",{className:`${ko.statusIcon} ${"connected"===b?ko.connected:ko.disconnected}`,children:"🔗"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:556,columnNumber:11},void 0),e.jsxDEV("span",{children:["MCP: ","connected"===b?"Conectado":"Desconectado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:559,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:555,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.statusItem,children:[e.jsxDEV("span",{className:ko.statusIcon,children:"💬"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:562,columnNumber:11},void 0),e.jsxDEV("span",{children:["Chat IA: ",m?"Ativo":"Standby"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:563,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:561,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.statusItem,children:[e.jsxDEV("span",{className:ko.statusIcon,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:566,columnNumber:11},void 0),e.jsxDEV("span",{children:["Modo: ","unified"===x?"Unificado":"realtime"===x?"Tempo Real":"report"===x?"Relatório":"integrated"===x?"Sistema Integrado":"multisensory"===x?"Multissensorial":"Padrão"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:567,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:565,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:550,columnNumber:7},void 0),v&&e.jsxDEV(So,{onStatusChange:(e,o)=>{p(e)},className:ko.mcpSection},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:579,columnNumber:9},void 0),e.jsxDEV(we,{dashboardData:_,className:ko.ieBrandSection},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:586,columnNumber:7},void 0),N&&e.jsxDEV("div",{className:ko.unifiedDashboardWrapper,children:[e.jsxDEV("div",{className:ko.dashboardTypeControls,children:[e.jsxDEV("button",{className:`${ko.typeButton} ${"unified"===x?ko.active:""}`,onClick:()=>A("unified"),children:[e.jsxDEV("span",{className:ko.typeIcon,children:"🧩"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:600,columnNumber:15},void 0),"Dashboard Unificado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:596,columnNumber:13},void 0),e.jsxDEV("button",{className:`${ko.typeButton} ${"realtime"===x?ko.active:""}`,onClick:()=>A("realtime"),children:[e.jsxDEV("span",{className:ko.typeIcon,children:"⚡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:607,columnNumber:15},void 0),"Tempo Real"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:603,columnNumber:13},void 0),e.jsxDEV("button",{className:`${ko.typeButton} ${"report"===x?ko.active:""}`,onClick:()=>A("report"),children:[e.jsxDEV("span",{className:ko.typeIcon,children:"📝"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:614,columnNumber:15},void 0),"Relatório"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:610,columnNumber:13},void 0),e.jsxDEV("button",{className:`${ko.typeButton} ${"integrated"===x?ko.active:""}`,onClick:()=>A("integrated"),children:[e.jsxDEV("span",{className:ko.typeIcon,children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:621,columnNumber:15},void 0),"Sistema Integrado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:617,columnNumber:13},void 0),e.jsxDEV("button",{className:`${ko.typeButton} ${"multisensory"===x?ko.active:""}`,onClick:()=>A("multisensory"),children:[e.jsxDEV("span",{className:ko.typeIcon,children:"🌈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:628,columnNumber:15},void 0),"Multissensorial"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:624,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:595,columnNumber:11},void 0),"unified"===x&&e.jsxDEV("div",{className:ko.dashboardModeControls,children:[e.jsxDEV("button",{className:`${ko.modeButton} ${"standard"===j?ko.active:""}`,onClick:()=>f("standard"),children:[e.jsxDEV("span",{className:ko.modeIcon,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:640,columnNumber:17},void 0),"Padrão"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:636,columnNumber:15},void 0),e.jsxDEV("button",{className:`${ko.modeButton} ${"compact"===j?ko.active:""}`,onClick:()=>f("compact"),children:[e.jsxDEV("span",{className:ko.modeIcon,children:"📱"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:647,columnNumber:17},void 0),"Compacto"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:643,columnNumber:15},void 0),e.jsxDEV("button",{className:`${ko.modeButton} ${"detailed"===j?ko.active:""}`,onClick:()=>f("detailed"),children:[e.jsxDEV("span",{className:ko.modeIcon,children:"📈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:654,columnNumber:17},void 0),"Detalhado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:650,columnNumber:15},void 0),e.jsxDEV("button",{className:`${ko.modeButton} ${"professional"===j?ko.active:""}`,onClick:()=>f("professional"),children:[e.jsxDEV("span",{className:ko.modeIcon,children:"👔"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:661,columnNumber:17},void 0),"Profissional"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:657,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:635,columnNumber:13},void 0),"unified"===x&&e.jsxDEV(wo,{dashboardData:_,className:ko.unifiedSection,viewMode:j},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:669,columnNumber:13},void 0),"realtime"===x&&e.jsxDEV(c.Suspense,{fallback:e.jsxDEV("div",{className:ko.dashboardPlaceholder,children:[e.jsxDEV("span",{className:ko.placeholderIcon,children:"⚡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:678,columnNumber:17},void 0),e.jsxDEV("p",{children:"Carregando dashboard em tempo real..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:679,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:677,columnNumber:15},void 0),children:e.jsxDEV(Bo,{gameType:"all",userId:null,feedbackIntegration:!0,onFeedbackMessage:e=>{},className:ko.dashboardComponent},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:682,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:676,columnNumber:13},void 0),"report"===x&&e.jsxDEV(c.Suspense,{fallback:e.jsxDEV("div",{className:ko.dashboardPlaceholder,children:[e.jsxDEV("span",{className:ko.placeholderIcon,children:"📝"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:694,columnNumber:17},void 0),e.jsxDEV("p",{children:"Carregando relatório avançado..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:695,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:693,columnNumber:15},void 0),children:e.jsxDEV(zo,{className:ko.dashboardComponent},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:698,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:692,columnNumber:13},void 0),"integrated"===x&&e.jsxDEV(c.Suspense,{fallback:e.jsxDEV("div",{className:ko.dashboardPlaceholder,children:[e.jsxDEV("span",{className:ko.placeholderIcon,children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:706,columnNumber:17},void 0),e.jsxDEV("p",{children:"Carregando sistema integrado..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:707,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:705,columnNumber:15},void 0),children:e.jsxDEV(Oo,{className:ko.dashboardComponent},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:710,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:704,columnNumber:13},void 0),"multisensory"===x&&e.jsxDEV(c.Suspense,{fallback:e.jsxDEV("div",{className:ko.dashboardPlaceholder,children:[e.jsxDEV("span",{className:ko.placeholderIcon,children:"🌈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:718,columnNumber:17},void 0),e.jsxDEV("p",{children:"Carregando métricas multissensoriais..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:719,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:717,columnNumber:15},void 0),children:e.jsxDEV(Go,{timeframe:"month",userId:null,isPremiumUser:!0,className:ko.dashboardComponent},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:722,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:716,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:593,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.metricsGrid,children:[e.jsxDEV("div",{className:ko.metricCard,children:[e.jsxDEV("div",{className:ko.metricHeader,children:[e.jsxDEV("h3",{className:ko.metricTitle,children:"Estilo Dominante"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:737,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ko.metricIcon} ${ko.style}`,children:"🎨"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:738,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:736,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.metricValue,children:R.cognitiveProfile.dominant_style},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:740,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ko.metricTrend} ${ko.trendPositive}`,children:["📊 ",R.cognitiveProfile.confidence,"% confiança"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:741,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:735,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.metricCard,children:[e.jsxDEV("div",{className:ko.metricHeader,children:[e.jsxDEV("h3",{className:ko.metricTitle,children:"Pontos Fortes"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:748,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ko.metricIcon} ${ko.strengths}`,children:"💪"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:749,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:747,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.metricValue,children:R.cognitiveProfile.strengths.length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:751,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ko.metricTrend} ${ko.trendPositive}`,children:"✅ Habilidades identificadas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:752,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:746,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.metricCard,children:[e.jsxDEV("div",{className:ko.metricHeader,children:[e.jsxDEV("h3",{className:ko.metricTitle,children:"Próximo Marco"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:759,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ko.metricIcon} ${ko.milestone}`,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:760,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:758,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.metricValue,children:[M.next_milestone.probability,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:762,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ko.metricTrend} ${ko.trendPositive}`,children:["⏱️ ",M.next_milestone.timeline]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:763,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:757,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.metricCard,children:[e.jsxDEV("div",{className:ko.metricHeader,children:[e.jsxDEV("h3",{className:ko.metricTitle,children:"Recomendações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:770,columnNumber:13},void 0),e.jsxDEV("div",{className:`${ko.metricIcon} ${ko.recommendations}`,children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:771,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:769,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.metricValue,children:V.length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:773,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ko.metricTrend} ${ko.trendPositive}`,children:"📋 Sugestões ativas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:774,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:768,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:734,columnNumber:7},void 0),e.jsxDEV("div",{className:ko.chartsGrid,children:[e.jsxDEV("div",{className:ko.chartCard,children:[e.jsxDEV("h3",{className:ko.chartTitle,children:"🧠 Radar Cognitivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:783,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.chartContainer,children:w?.cognitive&&e.jsxDEV(P,{data:w.cognitive,options:{responsive:!0,maintainAspectRatio:!1,scales:{r:{beginAtZero:!0,max:100,ticks:{color:"#666"},grid:{color:"#e0e0e0"}}},plugins:{legend:{position:"bottom"}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:786,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:784,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:782,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.chartCard,children:[e.jsxDEV("h3",{className:ko.chartTitle,children:"📈 Evolução da Performance"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:815,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.chartContainer,children:w?.progress&&e.jsxDEV(D,{data:w.progress,options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,max:100,ticks:{color:"#666"},grid:{color:"#e0e0e0"}},x:{ticks:{color:"#666"},grid:{color:"#e0e0e0"}}},plugins:{legend:{position:"bottom"}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:818,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:816,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:814,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.chartCard,children:[e.jsxDEV("h3",{className:ko.chartTitle,children:"🎮 Distribuição por Atividades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:855,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.chartContainer,children:w?.distribution&&e.jsxDEV(C,{data:w.distribution,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom"}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:858,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:856,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:854,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:781,columnNumber:7},void 0),e.jsxDEV("div",{className:ko.insightsSection,children:[e.jsxDEV("h3",{className:ko.insightsTitle,children:"🧠 Análise Cognitiva Detalhada"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:877,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.insightsGrid,children:[e.jsxDEV("div",{className:ko.insightCard,children:[e.jsxDEV("h4",{className:ko.insightTitle,children:"🎯 Pontos Fortes"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:882,columnNumber:13},void 0),e.jsxDEV("div",{className:ko.insightContent,children:R.cognitiveProfile.strengths.map((o,a)=>e.jsxDEV("p",{children:["• ",o]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:885,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:883,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:881,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.insightCard,children:[e.jsxDEV("h4",{className:ko.insightTitle,children:"📈 Áreas de Melhoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:891,columnNumber:13},void 0),e.jsxDEV("div",{className:ko.insightContent,children:R.cognitiveProfile.improvements.map((o,a)=>e.jsxDEV("p",{children:["• ",o]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:894,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:892,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:890,columnNumber:11},void 0),e.jsxDEV("div",{className:ko.insightCard,children:[e.jsxDEV("h4",{className:ko.insightTitle,children:"🔮 Predições"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:900,columnNumber:13},void 0),e.jsxDEV("div",{className:ko.insightContent,children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Próxima habilidade:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:902,columnNumber:18},void 0)," ",M.next_milestone.skill]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:902,columnNumber:15},void 0),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Prazo estimado:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:903,columnNumber:18},void 0)," ",M.next_milestone.timeline]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:903,columnNumber:15},void 0),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Probabilidade:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:904,columnNumber:18},void 0)," ",M.next_milestone.probability,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:904,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:901,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:899,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:880,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:876,columnNumber:7},void 0),e.jsxDEV("div",{className:ko.recommendationsSection,children:[e.jsxDEV("h3",{className:ko.recommendationsTitle,children:"💡 Recomendações Personalizadas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:912,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.recommendationsGrid,children:V.map((o,a)=>e.jsxDEV("div",{className:ko.recommendationCard,children:[e.jsxDEV("div",{className:ko.recommendationIcon,children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:918,columnNumber:15},void 0),e.jsxDEV("div",{className:ko.recommendationContent,children:e.jsxDEV("p",{children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:920,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:919,columnNumber:15},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:917,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:915,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:911,columnNumber:7},void 0),e.jsxDEV("div",{className:ko.aiInsightsSection,children:[e.jsxDEV("h3",{className:ko.aiInsightsTitle,children:"🤖 Insights da Inteligência Artificial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:929,columnNumber:9},void 0),e.jsxDEV("div",{className:ko.aiInsightsGrid,children:S.map((o,a)=>e.jsxDEV("div",{className:ko.aiInsightCard,children:[e.jsxDEV("div",{className:ko.aiInsightIcon,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:935,columnNumber:15},void 0),e.jsxDEV("div",{className:ko.aiInsightContent,children:e.jsxDEV("p",{children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:937,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:936,columnNumber:15},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:934,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:932,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:928,columnNumber:7},void 0),e.jsxDEV(Ve,{isVisible:m,onClose:()=>u(!1),dashboardData:_,className:ko.aiChatComponent},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:945,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/AdvancedAIReport/AdvancedAIReport.jsx",lineNumber:485,columnNumber:5},void 0)};c.createContext(null);const Ho={home:{welcome:"Bem-vindo ao Portal Betina! Aqui você encontra atividades terapêuticas divertidas e educativas.",popularActivities:"Atividades Mais Populares",toolsUtilities:"Ferramentas e Utilitários",footerNavigation:"Use a navegação inferior para acessar diferentes seções"},games:{letterRecognition:{title:"Reconhecimento de Letras",description:"Aprenda o alfabeto de forma divertida e interativa",instruction:"Clique na letra que você ouvir ou ver na tela",difficultyEasy:"Nível fácil com letras maiúsculas",difficultyMedium:"Nível médio com letras minúsculas",difficultyHard:"Nível difícil com letras mistas",correct:"Muito bem! Resposta correta!",incorrect:"Tente novamente! Você consegue!",gameComplete:"Parabéns! Você completou o jogo!"},numberCounting:{title:"Contagem de Números",description:"Pratique contagem e reconhecimento numérico",instruction:"Conte os objetos na tela e clique no número correto",difficultyEasy:"Conte de 1 a 5",difficultyMedium:"Conte de 1 a 10",difficultyHard:"Conte de 1 a 15",correct:"Excelente! Contagem correta!",incorrect:"Conte novamente com cuidado!",gameComplete:"Fantástico! Você domina a contagem!"},musicalSequence:{title:"Sequência Musical",description:"Repita sequências sonoras e desenvolva a memória auditiva",instruction:"Ouça a sequência musical e repita clicando nos botões na ordem correta",difficultyEasy:"Sequências curtas de 2 a 3 notas",difficultyMedium:"Sequências médias de 4 a 5 notas",difficultyHard:"Sequências longas de 6 a 8 notas",listenCarefully:"Ouça com atenção a sequência musical",repeatSequence:"Agora repita a sequência",correct:"Perfeito! Sequência correta!",incorrect:"Ouça novamente e tente repetir",gameComplete:"Incrível! Sua memória auditiva está excelente!"},memoryGame:{title:"Jogo da Memória",description:"Encontre os pares e exercite sua memória",instruction:"Clique nas cartas para encontrar os pares iguais",difficultyEasy:"6 cartas - 3 pares",difficultyMedium:"12 cartas - 6 pares",difficultyHard:"20 cartas - 10 pares",cardFlipped:"Carta virada",pairFound:"Par encontrado! Muito bem!",tryAgain:"Não é um par. Tente novamente!",gameComplete:"Parabéns! Todos os pares foram encontrados!"},colorMatch:{title:"Combinação de Cores",description:"Combine cores e desenvolva percepção visual",instruction:"Clique na cor que combina com o exemplo mostrado",difficultyEasy:"Cores básicas e vibrantes",difficultyMedium:"Cores intermediárias",difficultyHard:"Tons e nuances sutis",correct:"Cor correta! Excelente percepção!",incorrect:"Observe melhor as cores e tente novamente",gameComplete:"Perfeito! Você domina as cores!"},imageAssociation:{title:"Associação de Imagens",description:"Associe imagens e desenvolva conexões cognitivas",instruction:"Encontre a imagem que se relaciona com a imagem principal",difficultyEasy:"Associações simples e diretas",difficultyMedium:"Associações por categoria",difficultyHard:"Associações conceituais",correct:"Associação correta! Muito inteligente!",incorrect:"Pense na relação entre as imagens",gameComplete:"Fantástico! Você fez todas as associações!"},creativePainting:{title:"Pintura Criativa",description:"Desenvolva criatividade através da arte digital",instruction:"Use as ferramentas de pintura para criar sua obra de arte",brushTool:"Pincel selecionado",eraserTool:"Borracha selecionada",colorSelected:"Cor selecionada",canvasClear:"Tela limpa para nova criação",artworkSaved:"Sua obra de arte foi salva!"}},tools:{dashboard:{title:"Dashboard do Sistema",description:"Monitore performance e estatísticas em tempo real",loading:"Carregando dados do dashboard"},userProfiles:{title:"Perfis de Usuário",description:"Gerencie perfis e progresso dos usuários",loading:"Carregando perfis de usuário"},backup:{title:"Backup e Exportação",description:"Faça backup e exporte dados do sistema",loading:"Preparando ferramentas de backup"},performance:{title:"Performance",description:"Analise métricas de desempenho e uso",loading:"Analisando dados de performance"},accessibility:{title:"Configurações de Acessibilidade",description:"Configure opções para melhor experiência",audioNarration:"Ativação de leitura em voz alta e feedback sonoro",highContrast:"Melhora a visibilidade com cores mais contrastantes",simplifiedNavigation:"Interface otimizada para diferentes necessidades",reducedAnimations:"Diminui movimentos que podem causar desconforto",comingSoon:"As configurações detalhadas serão implementadas em breve"}},navigation:{backToMenu:"Voltar ao menu principal",selectDifficulty:"Selecione o nível de dificuldade",startGame:"Iniciar jogo",pauseGame:"Pausar jogo",resumeGame:"Continuar jogo",restartGame:"Reiniciar jogo",nextLevel:"Próximo nível",menuButton:"Menu",settingsButton:"Configurações"},feedback:{loading:"Carregando, aguarde um momento",error:"Ops! Algo deu errado. Tente novamente",success:"Operação realizada com sucesso",wellDone:"Muito bem!",tryAgain:"Tente novamente",excellent:"Excelente!",perfect:"Perfeito!",keepGoing:"Continue assim!",almostThere:"Quase lá!"},accessibility:{buttonPress:"Botão pressionado",menuOpen:"Menu aberto",menuClose:"Menu fechado",gameStart:"Jogo iniciado",gameEnd:"Jogo finalizado",newLevel:"Novo nível desbloqueado",achievement:"Conquista desbloqueada"}},Jo=(e,o,a=null)=>{try{return a?Ho[e]?.[o]?.[a]||"":Ho[e]?.[o]||""}catch(r){return""}},Wo={ttsButton:"_ttsButton_1lc8s_15",speaking:"_speaking_1lc8s_81",pulse:"_pulse_1lc8s_1",paused:"_paused_1lc8s_91",pausePulse:"_pausePulse_1lc8s_1",disabled:"_disabled_1lc8s_101",ttsIcon:"_ttsIcon_1lc8s_139",ttsText:"_ttsText_1lc8s_151",small:"_small_1lc8s_163",medium:"_medium_1lc8s_177",large:"_large_1lc8s_191",highContrast:"_highContrast_1lc8s_305",reducedMotion:"_reducedMotion_1lc8s_327"},Ko=({text:o="",voice:a=null,rate:r=1,pitch:s=1,volume:n=1,size:t="medium",ariaLabel:i=null,onStart:d=null,onEnd:l=null,onError:m=null,onPause:u=null,onResume:b=null,showText:p=!1,className:v="",autoPlay:h=!1,pauseEnabled:N=!0,voiceFilter:g="pt-BR"})=>{const[j,f]=c.useState(!1),[x,D]=c.useState(!1),[A,C]=c.useState([]),[P,_]=c.useState(null),I=c.useRef(null),y=c.useRef(window.speechSynthesis),E=c.useMemo(()=>"undefined"!=typeof window&&"speechSynthesis"in window,[]),R=c.useMemo(()=>A.filter(e=>e.lang.includes(g)||e.name.toLowerCase().includes(g.toLowerCase())||e.name.toLowerCase().includes("portuguese")||e.name.toLowerCase().includes("brasil")),[A,g]),M=c.useMemo(()=>a||R[0]||null,[a,R]);c.useEffect(()=>{if(!E)return;const e=()=>{const e=y.current.getVoices();C(e)};return e(),y.current.onvoiceschanged=e,()=>{y.current.onvoiceschanged=null}},[E]),c.useEffect(()=>()=>{E&&y.current.cancel()},[E]),c.useEffect(()=>{if(h&&o.trim()&&!j&&!P){const e=setTimeout(()=>{if(!j&&E&&o.trim()){y.current.cancel(),_(null),f(!0),D(!1);const e=new SpeechSynthesisUtterance(o.trim());e.rate=Math.max(.1,Math.min(r,2)),e.pitch=Math.max(0,Math.min(s,2)),e.volume=Math.max(0,Math.min(n,1)),e.lang=g,e.voice=M,e.onstart=()=>{f(!0),d?.(e)},e.onend=()=>{f(!1),D(!1),l?.(e)},e.onpause=()=>{D(!0),u?.(e)},e.onresume=()=>{D(!1),b?.(e)},e.onerror=e=>{f(!1),D(!1);const o=`TTS Error: ${e.error}`;_(o),m?.(e)},I.current=e,y.current.speak(e)}},100);return()=>clearTimeout(e)}},[o,h,j,P,E,r,s,n,g,M,d,l,u,b,m]);const V=c.useCallback(()=>{if(!E||!o.trim()||j)return;y.current.cancel(),_(null),f(!0),D(!1);const e=new SpeechSynthesisUtterance(o.trim());e.rate=Math.max(.1,Math.min(r,2)),e.pitch=Math.max(0,Math.min(s,2)),e.volume=Math.max(0,Math.min(n,1)),e.lang=g,e.voice=M,e.onstart=()=>{f(!0),d?.(e)},e.onend=()=>{f(!1),D(!1),l?.(e)},e.onpause=()=>{D(!0),u?.(e)},e.onresume=()=>{D(!1),b?.(e)},e.onerror=e=>{f(!1),D(!1);const o=`TTS Error: ${e.error}`;_(o),m?.(e)},I.current=e,y.current.speak(e)},[o,r,s,n,E,M,g,j,d,l,m,u,b]),S=c.useCallback(()=>{E&&j&&!x&&(y.current.pause(),D(!0),u?.(I.current))},[E,j,x,u]),T=c.useCallback(()=>{E&&j&&x&&(y.current.resume(),D(!1),b?.(I.current))},[E,j,x,b]);c.useCallback(()=>{E&&(j||x)&&(y.current.cancel(),f(!1),D(!1))},[E,j,x]);const w=c.useCallback(()=>{j&&N&&!x?S():x?T():V()},[j,x,N,V,S,T]),k=c.useCallback(()=>{if(i)return i;return`${x?"Retomar":j&&N?"Pausar":"Ouvir"} texto: ${o.length>50?`${o.substring(0,50)}...`:o}`},[i,j,x,N,o]);if(!E)return null;const B=[Wo.ttsButton,Wo[t],j?Wo.speaking:"",x?Wo.paused:"",P?Wo.disabled:"",v].filter(Boolean).join(" ");return e.jsxDEV("button",{className:B,onClick:w,disabled:!o.trim()||P,"aria-label":k(),title:x?"Retomar leitura":j&&N?"Pausar leitura":"Ouvir texto",role:"button","aria-pressed":j||x,type:"button",children:[e.jsxDEV("span",{className:Wo.ttsIcon,children:x?"▶️":j?"⏸️":"🔊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/TextToSpeech.jsx",lineNumber:255,columnNumber:7},void 0),p&&e.jsxDEV("span",{className:Wo.ttsText,children:x?"Retomar":j&&N?"Pausar":"Ouvir"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/TextToSpeech.jsx",lineNumber:259,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/TextToSpeech.jsx",lineNumber:245,columnNumber:5},void 0)};Ko.propTypes={text:o.string,voice:o.object,rate:o.number,pitch:o.number,volume:o.number,size:o.oneOf(["small","medium","large"]),ariaLabel:o.string,onStart:o.func,onEnd:o.func,onError:o.func,onPause:o.func,onResume:o.func,showText:o.bool,className:o.string,autoPlay:o.bool,pauseEnabled:o.bool,voiceFilter:o.string},Ko.defaultProps={text:"",voice:null,rate:1,pitch:1,volume:1,size:"medium",ariaLabel:null,onStart:null,onEnd:null,onError:null,onPause:null,onResume:null,showText:!1,className:"",autoPlay:!1,pauseEnabled:!0,voiceFilter:"pt-BR"};const Zo=({children:o,feature:a,title:r="Recurso Premium",description:s="Este recurso está disponível apenas para usuários premium.",showUpgrade:n=!0})=>{const{isPremium:i,hasFeatureAccess:d,canAccessDashboard:l,availablePlans:m,upgradeToPremium:u}=t(),[b,p]=c.useState(!1);if("dashboard"===a?l():d(a))return o;return e.jsxDEV("div",{className:"premium-gate-container",children:e.jsxDEV("div",{className:"premium-gate-content",children:[e.jsxDEV("div",{className:"premium-gate-header",children:[e.jsxDEV("div",{className:"premium-gate-icon",children:e.jsxDEV("span",{style:{fontSize:"3rem"},children:"💎"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:46,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:45,columnNumber:11},void 0),e.jsxDEV("h2",{className:"premium-gate-title",children:r},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:48,columnNumber:11},void 0),e.jsxDEV(Ko,{text:Jo("system","premiumFeature"),size:"small"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:49,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:44,columnNumber:9},void 0),e.jsxDEV("div",{className:"premium-gate-description",children:e.jsxDEV("p",{children:s},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:56,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:55,columnNumber:9},void 0),n&&e.jsxDEV("div",{className:"premium-gate-actions",children:e.jsxDEV("button",{className:"premium-upgrade-button",onClick:()=>p(!b),children:[e.jsxDEV("span",{children:"🚀"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:65,columnNumber:15},void 0),"Ver Planos Premium"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:61,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:60,columnNumber:11},void 0),b&&e.jsxDEV("div",{className:"premium-plans-modal",children:[e.jsxDEV("div",{className:"premium-plans-overlay",onClick:()=>p(!1)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:73,columnNumber:13},void 0),e.jsxDEV("div",{className:"premium-plans-content",children:[e.jsxDEV("div",{className:"premium-plans-header",children:[e.jsxDEV("h3",{children:"Escolha seu Plano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:76,columnNumber:17},void 0),e.jsxDEV("button",{className:"premium-plans-close",onClick:()=>p(!1),children:"✕"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:77,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:75,columnNumber:15},void 0),e.jsxDEV("div",{className:"premium-plans-grid",children:Object.entries(m).map(([o,a])=>e.jsxDEV("div",{className:"premium-plan-card "+(a.popular?"popular":""),children:[a.popular&&e.jsxDEV("div",{className:"plan-badge",children:"Mais Popular"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:92,columnNumber:23},void 0),e.jsxDEV("div",{className:"plan-header",children:[e.jsxDEV("h4",{children:a.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:96,columnNumber:23},void 0),e.jsxDEV("div",{className:"plan-price",children:[a.price,e.jsxDEV("span",{className:"plan-period",children:a.period},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:99,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:97,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:95,columnNumber:21},void 0),e.jsxDEV("div",{className:"plan-features",children:[a.features.map((o,a)=>e.jsxDEV("div",{className:"plan-feature",children:["✅ ",o]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:105,columnNumber:25},void 0)),a.limitations&&a.limitations.map((o,a)=>e.jsxDEV("div",{className:"plan-limitation",children:["❌ ",o]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:110,columnNumber:25},void 0))]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:103,columnNumber:21},void 0),e.jsxDEV("button",{className:"plan-button "+("premium"===o?"primary":"secondary"),onClick:()=>(e=>{u(e),p(!1);const o=new Audio;o.src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuVgGk1",o.play().catch(()=>{})})(o),disabled:"free"===o,children:"free"===o?"Plano Atual":"Escolher Plano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:116,columnNumber:21},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:87,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:85,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:74,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:72,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:43,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:42,columnNumber:5},void 0)};l.register(m,u,b,p,v,h,N,x,g,j,f);const Yo=Object.freeze(Object.defineProperty({__proto__:null,default:()=>{const{user:o,isPremium:a}=t(),[r,s]=c.useState(!0),[n,i]=c.useState(null),[d,l]=c.useState("monthly"),[m,u]=c.useState("all"),b=c.useMemo(()=>({summary:{totalSessions:2847,averageScore:87.3,improvementRate:15.7,completionRate:92.1,totalTime:"847h 23m",streakDays:45},progressData:{labels:["Jan","Fev","Mar","Abr","Mai","Jun"],datasets:[{label:"Pontuação Média",data:[65,70,75,80,85,87],borderColor:"#2563EB",backgroundColor:"rgba(37, 99, 235, 0.1)",tension:.4,fill:!0},{label:"Taxa de Conclusão",data:[80,82,85,88,90,92],borderColor:"#059669",backgroundColor:"rgba(5, 150, 105, 0.1)",tension:.4,fill:!0}]},skillsRadar:{labels:["Matemática","Linguagem","Lógica","Memória","Atenção","Criatividade"],datasets:[{label:"Atual",data:[85,90,78,82,88,95],backgroundColor:"rgba(37, 99, 235, 0.2)",borderColor:"#2563EB",borderWidth:2},{label:"Meta",data:[90,95,85,90,95,98],backgroundColor:"rgba(219, 39, 119, 0.2)",borderColor:"#DB2777",borderWidth:2}]},gameDistribution:{labels:["Jogos de Memória","Matemática","Lógica","Linguagem","Criatividade"],datasets:[{data:[25,22,18,20,15],backgroundColor:["#2563EB","#059669","#DB2777","#EA580C","#6D28D9"],borderWidth:2,borderColor:"#ffffff"}]},weeklyActivity:{labels:["Seg","Ter","Qua","Qui","Sex","Sáb","Dom"],datasets:[{label:"Minutos Jogados",data:[45,52,38,47,55,62,35],backgroundColor:"#2563EB",borderRadius:8}]},achievements:[{id:1,title:"Sequência de 30 dias",icon:"fas fa-fire",color:"#EA580C",completed:!0},{id:2,title:"Mestre da Matemática",icon:"fas fa-calculator",color:"#2563EB",completed:!0},{id:3,title:"Memória de Elefante",icon:"fas fa-brain",color:"#DB2777",completed:!0},{id:4,title:"Lógica Avançada",icon:"fas fa-puzzle-piece",color:"#059669",completed:!1},{id:5,title:"Criativo Nato",icon:"fas fa-palette",color:"#6D28D9",completed:!1}],recommendations:[{type:"focus",title:"Melhorar Lógica",description:"Concentre-se nos jogos de lógica para atingir a meta de 85%",priority:"high"},{type:"maintain",title:"Manter Criatividade",description:"Continue explorando jogos criativos, você está indo muito bem!",priority:"medium"},{type:"schedule",title:"Horário Consistente",description:"Tente manter uma rotina de estudos mais regular durante a semana",priority:"low"}]}),[]),p={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#ffffff",font:{size:12}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#ffffff"}},x:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#ffffff"}}}},v=e=>{switch(e){case"high":return"#DC2626";case"medium":return"#EA580C";case"low":return"#059669";default:return"#6B7280"}},h=e=>{switch(e){case"focus":return"fas fa-bullseye";case"maintain":return"fas fa-check-circle";case"schedule":return"fas fa-calendar-alt";default:return"fas fa-lightbulb"}};return c.useEffect(()=>{a&&(async()=>{s(!0),setTimeout(()=>{i(b),s(!1)},1500)})()},[a,d,m,b]),a?r?e.jsxDEV(T,{message:"Gerando relatório avançado..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:269,columnNumber:12},void 0):e.jsxDEV("div",{className:"relatorio-dashboard",children:[e.jsxDEV("div",{className:"dashboard-header",children:[e.jsxDEV("h1",{className:"dashboard-title",children:[e.jsxDEV("i",{className:"fas fa-chart-line"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:276,columnNumber:11},void 0),"Relatório Avançado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:275,columnNumber:9},void 0),e.jsxDEV("p",{className:"dashboard-subtitle",children:"Análise completa do progresso e desempenho personalizado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:279,columnNumber:9},void 0),e.jsxDEV("div",{className:"dashboard-controls",children:[e.jsxDEV("select",{value:d,onChange:e=>l(e.target.value),className:"control-select",children:[e.jsxDEV("option",{value:"weekly",children:"Última Semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:289,columnNumber:13},void 0),e.jsxDEV("option",{value:"monthly",children:"Último Mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:290,columnNumber:13},void 0),e.jsxDEV("option",{value:"quarterly",children:"Último Trimestre"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:291,columnNumber:13},void 0),e.jsxDEV("option",{value:"yearly",children:"Último Ano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:292,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:284,columnNumber:11},void 0),e.jsxDEV("select",{value:m,onChange:e=>u(e.target.value),className:"control-select",children:[e.jsxDEV("option",{value:"all",children:"Todas as Métricas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:300,columnNumber:13},void 0),e.jsxDEV("option",{value:"cognitive",children:"Habilidades Cognitivas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:301,columnNumber:13},void 0),e.jsxDEV("option",{value:"academic",children:"Desempenho Acadêmico"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:302,columnNumber:13},void 0),e.jsxDEV("option",{value:"social",children:"Habilidades Sociais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:303,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:295,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:283,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:274,columnNumber:7},void 0),e.jsxDEV("div",{className:"summary-cards",children:[e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-play-circle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:311,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:310,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Total de Sessões"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:314,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:n?.summary.totalSessions},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:315,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+12% este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:316,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:313,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:309,columnNumber:9},void 0),e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-star"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:322,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:321,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Pontuação Média"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:325,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:[n?.summary.averageScore,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:326,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+5.3% este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:327,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:324,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:320,columnNumber:9},void 0),e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-arrow-up"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:333,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:332,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Taxa de Melhoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:336,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:[n?.summary.improvementRate,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:337,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+2.1% este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:338,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:335,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:331,columnNumber:9},void 0),e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-clock"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:344,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:343,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Tempo Total"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:347,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:n?.summary.totalTime},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:348,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+45h este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:349,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:346,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:342,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:308,columnNumber:7},void 0),e.jsxDEV("div",{className:"charts-section",children:[e.jsxDEV("div",{className:"chart-container large",children:[e.jsxDEV("h3",{className:"chart-title",children:"Progresso ao Longo do Tempo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:356,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(D,{data:n?.progressData,options:p},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:358,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:357,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:355,columnNumber:9},void 0),e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h3",{className:"chart-title",children:"Perfil de Habilidades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:363,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(P,{data:n?.skillsRadar,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#ffffff",font:{size:12}}}},scales:{r:{beginAtZero:!0,max:100,grid:{color:"rgba(255, 255, 255, 0.2)"},angleLines:{color:"rgba(255, 255, 255, 0.2)"},pointLabels:{color:"#ffffff",font:{size:11}},ticks:{color:"#ffffff",backdropColor:"transparent"}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:365,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:364,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:362,columnNumber:9},void 0),e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h3",{className:"chart-title",children:"Distribuição por Categoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:370,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(C,{data:n?.gameDistribution,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{color:"#ffffff",font:{size:11},usePointStyle:!0}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:372,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:371,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:369,columnNumber:9},void 0),e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h3",{className:"chart-title",children:"Atividade Semanal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:377,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(A,{data:n?.weeklyActivity,options:p},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:379,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:378,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:376,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:354,columnNumber:7},void 0),e.jsxDEV("div",{className:"achievements-section",children:[e.jsxDEV("h3",{className:"section-title",children:[e.jsxDEV("i",{className:"fas fa-trophy"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:386,columnNumber:11},void 0),"Conquistas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:385,columnNumber:9},void 0),e.jsxDEV("div",{className:"achievements-grid",children:n?.achievements.map(o=>e.jsxDEV("div",{className:"achievement-card "+(o.completed?"completed":"locked"),children:[e.jsxDEV("div",{className:"achievement-icon",style:{color:o.completed?o.color:"#6B7280"},children:e.jsxDEV("i",{className:o.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:399,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:395,columnNumber:15},void 0),e.jsxDEV("div",{className:"achievement-title",children:o.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:401,columnNumber:15},void 0),o.completed&&e.jsxDEV("div",{className:"achievement-badge",children:e.jsxDEV("i",{className:"fas fa-check"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:404,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:403,columnNumber:17},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:391,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:389,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:384,columnNumber:7},void 0),e.jsxDEV("div",{className:"recommendations-section",children:[e.jsxDEV("h3",{className:"section-title",children:[e.jsxDEV("i",{className:"fas fa-lightbulb"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:414,columnNumber:11},void 0),"Recomendações Personalizadas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:413,columnNumber:9},void 0),e.jsxDEV("div",{className:"recommendations-list",children:n?.recommendations.map((o,a)=>e.jsxDEV("div",{className:"recommendation-card",children:e.jsxDEV("div",{className:"recommendation-header",children:[e.jsxDEV("div",{className:"recommendation-icon",children:e.jsxDEV("i",{className:h(o.type)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:422,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:421,columnNumber:17},void 0),e.jsxDEV("div",{className:"recommendation-info",children:[e.jsxDEV("h4",{className:"recommendation-title",children:o.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:425,columnNumber:19},void 0),e.jsxDEV("p",{className:"recommendation-description",children:o.description},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:426,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:424,columnNumber:17},void 0),e.jsxDEV("div",{className:"recommendation-priority",style:{backgroundColor:v(o.priority)},children:o.priority},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:428,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:420,columnNumber:15},void 0)},a,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:419,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:417,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:412,columnNumber:7},void 0),e.jsxDEV("style",{children:"\n        .relatorio-dashboard {\n          padding: 2rem;\n          background: #000000;\n          min-height: 100vh;\n          color: white;\n        }\n\n        .dashboard-header {\n          text-align: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .dashboard-title {\n          font-size: 2.5rem;\n          margin-bottom: 0.5rem;\n          background: linear-gradient(135deg, #2563EB, #6D28D9);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n        }\n\n        .dashboard-title i {\n          margin-right: 1rem;\n          background: linear-gradient(135deg, #2563EB, #6D28D9);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n        }\n\n        .dashboard-subtitle {\n          font-size: 1.1rem;\n          color: rgba(255, 255, 255, 0.7);\n          margin-bottom: 1.5rem;\n        }\n\n        .dashboard-controls {\n          display: flex;\n          justify-content: center;\n          gap: 1rem;\n          flex-wrap: wrap;\n        }\n\n        .control-select {\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          border-radius: 0.5rem;\n          padding: 0.5rem 1rem;\n          color: white;\n          font-size: 0.9rem;\n        }\n\n        .control-select:focus {\n          outline: none;\n          border-color: #2563EB;\n          box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);\n        }\n\n        .summary-cards {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 1.5rem;\n          margin-bottom: 2rem;\n        }\n\n        .summary-card {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          border: 1px solid rgba(255, 255, 255, 0.1);\n          transition: all 0.3s ease;\n        }\n\n        .summary-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n          border-color: rgba(255, 255, 255, 0.2);\n        }\n\n        .card-icon {\n          width: 50px;\n          height: 50px;\n          background: linear-gradient(135deg, #2563EB, #6D28D9);\n          border-radius: 12px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          color: white;\n        }\n\n        .card-content h3 {\n          margin: 0 0 0.5rem 0;\n          font-size: 0.9rem;\n          color: rgba(255, 255, 255, 0.7);\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n\n        .card-value {\n          font-size: 1.8rem;\n          font-weight: bold;\n          color: white;\n          margin-bottom: 0.25rem;\n        }\n\n        .card-change {\n          font-size: 0.8rem;\n        }\n\n        .card-change.positive {\n          color: #059669;\n        }\n\n        .charts-section {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n          gap: 2rem;\n          margin-bottom: 2rem;\n        }\n\n        .chart-container {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          border: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .chart-container.large {\n          grid-column: 1 / -1;\n        }\n\n        .chart-title {\n          margin: 0 0 1.5rem 0;\n          font-size: 1.2rem;\n          color: white;\n          text-align: center;\n        }\n\n        .chart-wrapper {\n          height: 300px;\n          position: relative;\n        }\n\n        .achievements-section, .recommendations-section {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          border: 1px solid rgba(255, 255, 255, 0.1);\n          margin-bottom: 2rem;\n        }\n\n        .section-title {\n          margin: 0 0 1.5rem 0;\n          font-size: 1.2rem;\n          color: white;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .achievements-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 1rem;\n        }\n\n        .achievement-card {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 0.5rem;\n          padding: 1rem;\n          text-align: center;\n          border: 1px solid rgba(255, 255, 255, 0.05);\n          position: relative;\n          transition: all 0.3s ease;\n        }\n\n        .achievement-card.completed {\n          border-color: rgba(37, 99, 235, 0.3);\n        }\n\n        .achievement-card.locked {\n          opacity: 0.5;\n        }\n\n        .achievement-icon {\n          font-size: 2rem;\n          margin-bottom: 0.5rem;\n        }\n\n        .achievement-title {\n          font-size: 0.9rem;\n          color: white;\n        }\n\n        .achievement-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          width: 24px;\n          height: 24px;\n          background: #059669;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.8rem;\n          color: white;\n        }\n\n        .recommendations-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .recommendation-card {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 0.5rem;\n          padding: 1rem;\n          border: 1px solid rgba(255, 255, 255, 0.05);\n        }\n\n        .recommendation-header {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n        }\n\n        .recommendation-icon {\n          width: 40px;\n          height: 40px;\n          background: rgba(37, 99, 235, 0.2);\n          border-radius: 8px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #2563EB;\n        }\n\n        .recommendation-info {\n          flex: 1;\n        }\n\n        .recommendation-title {\n          margin: 0 0 0.25rem 0;\n          font-size: 1rem;\n          color: white;\n        }\n\n        .recommendation-description {\n          margin: 0;\n          font-size: 0.9rem;\n          color: rgba(255, 255, 255, 0.7);\n        }\n\n        .recommendation-priority {\n          padding: 0.25rem 0.5rem;\n          border-radius: 12px;\n          font-size: 0.7rem;\n          text-transform: uppercase;\n          font-weight: bold;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .relatorio-dashboard {\n            padding: 1rem;\n          }\n\n          .dashboard-title {\n            font-size: 2rem;\n          }\n\n          .summary-cards {\n            grid-template-columns: 1fr;\n          }\n\n          .charts-section {\n            grid-template-columns: 1fr;\n          }\n\n          .chart-wrapper {\n            height: 250px;\n          }\n\n          .achievements-grid {\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n          }\n\n          .recommendation-header {\n            flex-direction: column;\n            text-align: center;\n            gap: 0.5rem;\n          }\n        }\n      "},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:440,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:273,columnNumber:5},void 0):e.jsxDEV(Zo,{feature:"Relatório Avançado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:265,columnNumber:12},void 0)}},Symbol.toStringTag,{value:"Module"})),Qo="_dashboardContainer_lbydi_15",Xo="_dashboardHeader_lbydi_37",ea="_dashboardTitle_lbydi_55",oa="_titleIcon_lbydi_75",aa="_dashboardControls_lbydi_93",ra="_timeframeSelector_lbydi_105",sa="_refreshButton_lbydi_149",na="_metricsGrid_lbydi_191",ta="_metricCard_lbydi_205",ia="_metricHeader_lbydi_257",da="_metricTitle_lbydi_271",ca="_metricIcon_lbydi_285",la="_metricValue_lbydi_307",ma="_metricTrend_lbydi_323",ua="_trendPositive_lbydi_339",ba="_trendNeutral_lbydi_355",pa="_chartsGrid_lbydi_365",va="_chartCard_lbydi_379",ha="_chartTitle_lbydi_395",Na="_chartContainer_lbydi_409",ga="_analysisSection_lbydi_421",ja="_analysisTitle_lbydi_439",fa="_analysisGrid_lbydi_459",xa="_analysisCard_lbydi_471",Da="_analysisCardTitle_lbydi_485",Aa="_analysisCardContent_lbydi_499",Ca="_loadingContainer_lbydi_513";l.register(m,u,b,p,v,h,N,x,g,j,f);const Pa=()=>{const[o,a]=c.useState(!0),[r,s]=c.useState("30d"),[n,t]=c.useState(null);c.useEffect(()=>{(async()=>{a(!0),await new Promise(e=>setTimeout(e,1e3)),(()=>{try{const e=JSON.parse(localStorage.getItem("gameScores")||"[]"),o=JSON.parse(localStorage.getItem("gameSessions")||"[]"),a=new Date;let s=new Date;switch(r){case"7d":s.setDate(a.getDate()-7);break;case"30d":default:s.setDate(a.getDate()-30);break;case"90d":s.setDate(a.getDate()-90)}const n=e.filter(e=>new Date(e.timestamp)>=s),i=o.filter(e=>new Date(e.timestamp)>=s),d={attention:Math.round(n.reduce((e,o)=>e+(o.attention||30*Math.random()+60),0)/Math.max(n.length,1)),memory:Math.round(n.reduce((e,o)=>e+(o.memory||25*Math.random()+65),0)/Math.max(n.length,1)),processing:Math.round(n.reduce((e,o)=>e+(o.processing||35*Math.random()+55),0)/Math.max(n.length,1)),execution:Math.round(n.reduce((e,o)=>e+(o.execution||30*Math.random()+60),0)/Math.max(n.length,1)),comprehension:Math.round(n.reduce((e,o)=>e+(o.comprehension||25*Math.random()+70),0)/Math.max(n.length,1))},c={labels:["Sem 1","Sem 2","Sem 3","Sem 4"],datasets:[{label:"Progresso Cognitivo",data:[65,72,78,85],borderColor:"#667eea",backgroundColor:"rgba(102, 126, 234, 0.1)",tension:.4,fill:!0}]},l={labels:["Atenção","Memória","Processamento","Execução","Compreensão"],datasets:[{data:[d.attention,d.memory,d.processing,d.execution,d.comprehension],backgroundColor:["#667eea","#764ba2","#f093fb","#f5576c","#4ecdc4"],borderWidth:2,borderColor:"#fff"}]},m={labels:["Atenção","Memória","Processamento","Execução","Compreensão"],datasets:[{label:"Perfil Cognitivo",data:[d.attention,d.memory,d.processing,d.execution,d.comprehension],backgroundColor:"rgba(102, 126, 234, 0.2)",borderColor:"#667eea",borderWidth:2,pointBackgroundColor:"#667eea",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"#667eea"}]},u={linguagem:{current:d.comprehension,target:90,improvement:12},matematica:{current:d.processing,target:85,improvement:8},coordenacao:{current:d.execution,target:80,improvement:15},socializacao:{current:Math.round((d.attention+d.memory)/2),target:88,improvement:10}};t({cognitiveMetrics:d,weeklyProgress:c,skillDistribution:l,radarData:m,developmentAreas:u,totalSessions:i.length,averageScore:Math.round(n.reduce((e,o)=>e+o.score,0)/Math.max(n.length,1))})}catch(e){t({cognitiveMetrics:{attention:0,memory:0,processing:0,execution:0,comprehension:0},weeklyProgress:{labels:[],datasets:[]},skillDistribution:{labels:[],datasets:[]},radarData:{labels:[],datasets:[]},developmentAreas:{},totalSessions:0,averageScore:0})}})(),a(!1)})()},[r]);const i={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0,max:100}}};return o?e.jsxDEV("div",{className:Ca,children:e.jsxDEV(T,{message:"Carregando dashboard neuropedagógico..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:242,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:241,columnNumber:7},void 0):e.jsxDEV("div",{className:Qo,children:[e.jsxDEV("div",{className:Xo,children:[e.jsxDEV("h1",{className:ea,children:[e.jsxDEV("span",{className:oa,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:252,columnNumber:11},void 0),"Dashboard Neuropedagógico"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:251,columnNumber:9},void 0),e.jsxDEV("div",{className:aa,children:[e.jsxDEV("select",{className:ra,value:r,onChange:e=>s(e.target.value),children:[e.jsxDEV("option",{value:"7d",children:"7 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:262,columnNumber:13},void 0),e.jsxDEV("option",{value:"30d",children:"30 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:263,columnNumber:13},void 0),e.jsxDEV("option",{value:"90d",children:"90 dias"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:264,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:257,columnNumber:11},void 0),e.jsxDEV("button",{className:sa,onClick:()=>window.location.reload(),children:"🔄 Atualizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:266,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:256,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:250,columnNumber:7},void 0),e.jsxDEV("div",{className:na,children:[e.jsxDEV("div",{className:ta,children:[e.jsxDEV("div",{className:ia,children:[e.jsxDEV("h3",{className:da,children:"Atenção"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:279,columnNumber:13},void 0),e.jsxDEV("div",{className:ca,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:280,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:278,columnNumber:11},void 0),e.jsxDEV("div",{className:la,children:[n?.cognitiveMetrics?.attention||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:282,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ma} ${ua}`,children:"↗️ +12% esta semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:283,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:277,columnNumber:9},void 0),e.jsxDEV("div",{className:ta,children:[e.jsxDEV("div",{className:ia,children:[e.jsxDEV("h3",{className:da,children:"Memória"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:290,columnNumber:13},void 0),e.jsxDEV("div",{className:ca,children:"🧩"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:291,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:289,columnNumber:11},void 0),e.jsxDEV("div",{className:la,children:[n?.cognitiveMetrics?.memory||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:293,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ma} ${ua}`,children:"↗️ +8% esta semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:294,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:288,columnNumber:9},void 0),e.jsxDEV("div",{className:ta,children:[e.jsxDEV("div",{className:ia,children:[e.jsxDEV("h3",{className:da,children:"Processamento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:301,columnNumber:13},void 0),e.jsxDEV("div",{className:ca,children:"⚡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:302,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:300,columnNumber:11},void 0),e.jsxDEV("div",{className:la,children:[n?.cognitiveMetrics?.processing||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:304,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ma} ${ua}`,children:"↗️ +15% esta semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:305,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:299,columnNumber:9},void 0),e.jsxDEV("div",{className:ta,children:[e.jsxDEV("div",{className:ia,children:[e.jsxDEV("h3",{className:da,children:"Execução"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:312,columnNumber:13},void 0),e.jsxDEV("div",{className:ca,children:"🎨"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:313,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:311,columnNumber:11},void 0),e.jsxDEV("div",{className:la,children:[n?.cognitiveMetrics?.execution||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:315,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ma} ${ba}`,children:"➡️ Estável"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:316,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:310,columnNumber:9},void 0),e.jsxDEV("div",{className:ta,children:[e.jsxDEV("div",{className:ia,children:[e.jsxDEV("h3",{className:da,children:"Compreensão"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:323,columnNumber:13},void 0),e.jsxDEV("div",{className:ca,children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:324,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:322,columnNumber:11},void 0),e.jsxDEV("div",{className:la,children:[n?.cognitiveMetrics?.comprehension||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:326,columnNumber:11},void 0),e.jsxDEV("div",{className:`${ma} ${ua}`,children:"↗️ +10% esta semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:327,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:321,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:276,columnNumber:7},void 0),e.jsxDEV("div",{className:pa,children:[e.jsxDEV("div",{className:va,children:[e.jsxDEV("h3",{className:ha,children:"📈 Progresso Semanal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:336,columnNumber:11},void 0),e.jsxDEV("div",{className:Na,children:n?.weeklyProgress?.datasets&&e.jsxDEV(D,{data:n.weeklyProgress,options:i},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:339,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:337,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:335,columnNumber:9},void 0),e.jsxDEV("div",{className:va,children:[e.jsxDEV("h3",{className:ha,children:"🎯 Distribuição de Habilidades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:345,columnNumber:11},void 0),e.jsxDEV("div",{className:Na,children:n?.skillDistribution?.datasets&&e.jsxDEV(_,{data:n.skillDistribution,options:{...i,scales:void 0}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:348,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:346,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:344,columnNumber:9},void 0),e.jsxDEV("div",{className:va,children:[e.jsxDEV("h3",{className:ha,children:"🧠 Perfil Cognitivo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:354,columnNumber:11},void 0),e.jsxDEV("div",{className:Na,children:n?.radarData?.datasets&&e.jsxDEV(P,{data:n.radarData,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{r:{beginAtZero:!0,max:100,ticks:{stepSize:20}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:357,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:355,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:353,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:334,columnNumber:7},void 0),e.jsxDEV("div",{className:ga,children:[e.jsxDEV("h3",{className:ja,children:"🧠 Análise Neuropedagógica"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:365,columnNumber:9},void 0),e.jsxDEV("div",{className:fa,children:n?.developmentAreas&&Object.entries(n.developmentAreas).map(([o,a])=>e.jsxDEV("div",{className:xa,children:[e.jsxDEV("h4",{className:Da,children:o.charAt(0).toUpperCase()+o.slice(1)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:371,columnNumber:15},void 0),e.jsxDEV("div",{className:Aa,children:[e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Pontuação Atual:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:375,columnNumber:20},void 0)," ",a.current,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:375,columnNumber:17},void 0),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Meta:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:376,columnNumber:20},void 0)," ",a.target,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:376,columnNumber:17},void 0),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Melhoria:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:377,columnNumber:20},void 0)," +",a.improvement,"% no período"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:377,columnNumber:17},void 0),e.jsxDEV("div",{className:"progress-bar",style:{width:"100%",height:"8px",backgroundColor:"#e2e8f0",borderRadius:"4px",marginTop:"8px",overflow:"hidden"},children:e.jsxDEV("div",{style:{width:a.current/a.target*100+"%",height:"100%",background:"linear-gradient(90deg, #667eea, #764ba2)",borderRadius:"4px",transition:"width 0.3s ease"}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:386,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:378,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:374,columnNumber:15},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:370,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:368,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:364,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/NeuropedagogicalDashboard/NeuropedagogicalDashboard.jsx",lineNumber:248,columnNumber:5},void 0)},_a={dashboardContainer:"_dashboardContainer_1k0sx_15",dashboardHeader:"_dashboardHeader_1k0sx_37",dashboardTitle:"_dashboardTitle_1k0sx_55",titleIcon:"_titleIcon_1k0sx_75",dashboardControls:"_dashboardControls_1k0sx_93",timeframeSelector:"_timeframeSelector_1k0sx_107",refreshButton:"_refreshButton_1k0sx_143",sensoryMetricsGrid:"_sensoryMetricsGrid_1k0sx_185",sensoryMetricCard:"_sensoryMetricCard_1k0sx_199",visual:"_visual_1k0sx_249",auditory:"_auditory_1k0sx_257",tactile:"_tactile_1k0sx_265",cognitive:"_cognitive_1k0sx_273",sensoryHeader:"_sensoryHeader_1k0sx_281",sensoryTitle:"_sensoryTitle_1k0sx_295",sensoryIcon:"_sensoryIcon_1k0sx_309",sensoryValue:"_sensoryValue_1k0sx_363",sensoryProgress:"_sensoryProgress_1k0sx_379",sensoryProgressBar:"_sensoryProgressBar_1k0sx_397",chartsGrid:"_chartsGrid_1k0sx_443",chartCard:"_chartCard_1k0sx_457",chartTitle:"_chartTitle_1k0sx_475",chartContainer:"_chartContainer_1k0sx_495",radar:"_radar_1k0sx_505",doughnut:"_doughnut_1k0sx_513",insightsSection:"_insightsSection_1k0sx_523",insightsTitle:"_insightsTitle_1k0sx_541",insightsGrid:"_insightsGrid_1k0sx_561",insightCard:"_insightCard_1k0sx_573",insightTitle:"_insightTitle_1k0sx_621",insightContent:"_insightContent_1k0sx_635",insightScore:"_insightScore_1k0sx_647",filtersSection:"_filtersSection_1k0sx_705",filtersTitle:"_filtersTitle_1k0sx_721",filtersGrid:"_filtersGrid_1k0sx_735",filterGroup:"_filterGroup_1k0sx_747",filterLabel:"_filterLabel_1k0sx_759",filterSelect:"_filterSelect_1k0sx_771",loadingContainer:"_loadingContainer_1k0sx_809",loadingText:"_loadingText_1k0sx_827",errorContainer:"_errorContainer_1k0sx_837",emptyState:"_emptyState_1k0sx_855",emptyStateIcon:"_emptyStateIcon_1k0sx_867",emptyStateText:"_emptyStateText_1k0sx_879",emptyStateSubtext:"_emptyStateSubtext_1k0sx_889",fadeIn:"_fadeIn_1k0sx_1",slideIn:"_slideIn_1k0sx_1",aiAnalysisSection:"_aiAnalysisSection_1k0sx_1193",aiAnalysisTitle:"_aiAnalysisTitle_1k0sx_1209",aiLoading:"_aiLoading_1k0sx_1227",aiInsightsGrid:"_aiInsightsGrid_1k0sx_1247",aiInsightCard:"_aiInsightCard_1k0sx_1259",aiInsightHeader:"_aiInsightHeader_1k0sx_1279",aiInsightIcon:"_aiInsightIcon_1k0sx_1297",aiInsightList:"_aiInsightList_1k0sx_1319",aiInsightItem:"_aiInsightItem_1k0sx_1331",modalityProfile:"_modalityProfile_1k0sx_1375",modalityPrimary:"_modalityPrimary_1k0sx_1387",modalitySecondary:"_modalitySecondary_1k0sx_1389",modalityStrength:"_modalityStrength_1k0sx_1391"};l.register(m,u,b,p,v,h,N,g,j,f,x);const Ia=({timeframe:o="week",userId:a=null,isPremiumUser:r=!1,...s})=>{const[n,t]=c.useState({loading:!0,error:null,sensoryData:null}),[i,d]=c.useState(null),{loading:l,refreshAI:m}=((e,o)=>{const[a,r]=c.useState(null),[s,n]=c.useState(!1),[t,i]=c.useState(null),d=async()=>{};return c.useEffect(()=>{d()},[e,o]),{aiMetrics:a,loading:s,error:t,refresh:d}})(a,null),u=e=>{const o=e.filter(e=>e.game?.includes("Cores")||e.game?.includes("Visual")||e.game?.includes("Reconhecimento"));return{consistency:o.length>1?h(o):.5,preference:o.length>0?"high":"low",responseTime:o.length>0?o.reduce((e,o)=>e+(o.responseTime||3e3),0)/o.length:3e3}},b=e=>{const o=e.filter(e=>e.game?.includes("Sons")||e.game?.includes("Música")||e.game?.includes("Áudio"));return{consistency:o.length>1?h(o):.5,preference:o.length>0?"high":"low",responseTime:o.length>0?o.reduce((e,o)=>e+(o.responseTime||3e3),0)/o.length:3e3}},p=e=>{const o=e.filter(e=>e.game?.includes("Toque")||e.game?.includes("Coordenação")||e.game?.includes("Motor"));return{consistency:o.length>1?h(o):.5,preference:o.length>0?"high":"low",pressure:.5*Math.random()+.5}},v=e=>({stability:.3*Math.random()+.7,coordination:e.length>0?e.reduce((e,o)=>e+(o.accuracy||0),0)/e.length/100:.7,patterns:"stable"}),h=e=>{if(e.length<2)return.5;const o=e.map(e=>e.accuracy||0),a=o.reduce((e,o)=>e+o,0)/o.length,r=o.reduce((e,o)=>e+Math.pow(o-a,2),0)/o.length;return Math.max(0,1-Math.sqrt(r)/100)},N=e=>e.length<2?.7:h(e);if(c.useEffect(()=>{(async()=>{try{t(e=>({...e,loading:!0,error:null}));let o=[],r=[],s={};try{const e=await fetch("/api/public/metrics/dashboard");if(e.ok){const a=await e.json();a.success&&a.data&&(o=a.data.game_sessions||[],r=a.data.user_engagement||[],s=a.data.performance_data||{})}}catch(e){}0===o.length&&(o=JSON.parse(localStorage.getItem("gameScores")||"[]"),r=JSON.parse(localStorage.getItem("gameSessions")||"[]"),s=JSON.parse(localStorage.getItem("sensoryProgress")||"{}"));const n=o.length>0?Math.round(o.reduce((e,o)=>e+o.accuracy,0)/o.length):75,i=o.filter(e=>e.game?.includes("Cores")||e.game?.includes("Visual")||e.game?.includes("Reconhecimento")),c=o.filter(e=>e.game?.includes("Sons")||e.game?.includes("Música")||e.game?.includes("Áudio")),l=o.filter(e=>e.game?.includes("Toque")||e.game?.includes("Coordenação")||e.game?.includes("Motor")),h=o.filter(e=>e.game?.includes("Memória")||e.game?.includes("Números")||e.game?.includes("Lógica")),g={visual:{score:i.length>0?Math.round(i.reduce((e,o)=>e+o.accuracy,0)/i.length):s.visual||Math.round(.9*n)||70,sessions:i.length,improvement:s.visualImprovement||Math.round(10*Math.random()+5)},auditory:{score:c.length>0?Math.round(c.reduce((e,o)=>e+o.accuracy,0)/c.length):s.auditory||Math.round(1.1*n)||75,sessions:c.length,improvement:s.auditoryImprovement||Math.round(8*Math.random()+3)},tactile:{score:l.length>0?Math.round(l.reduce((e,o)=>e+o.accuracy,0)/l.length):s.tactile||Math.round(.85*n)||65,sessions:l.length,improvement:s.tactileImprovement||Math.round(12*Math.random()+8)},cognitive:{score:h.length>0?Math.round(h.reduce((e,o)=>e+o.accuracy,0)/h.length):s.cognitive||Math.round(1.05*n)||80,sessions:h.length,improvement:s.cognitiveImprovement||Math.round(10*Math.random()+6)}},j={...s,visual:g.visual.score,auditory:g.auditory.score,tactile:g.tactile.score,cognitive:g.cognitive.score,lastUpdate:(new Date).toISOString()};localStorage.setItem("sensoryProgress",JSON.stringify(j)),t({loading:!1,error:null,sensoryData:g}),await(async(e,o)=>{try{const r={visual:{score:e.visual.score,sessions:e.visual.sessions,improvement:e.visual.improvement,patterns:u(o)},auditory:{score:e.auditory.score,sessions:e.auditory.sessions,improvement:e.auditory.improvement,patterns:b(o)},tactile:{score:e.tactile.score,sessions:e.tactile.sessions,improvement:e.tactile.improvement,patterns:p(o)},movement:{coordination:e.cognitive.score,stability:N(o),patterns:v(o)}},s={childId:a||"demo_user",sessionId:`multisensory_${Date.now()}`,totalSessions:o.length,avgAccuracy:o.length>0?o.reduce((e,o)=>e+(o.accuracy||0),0)/o.length:0,timestamp:(new Date).toISOString()},n=await m(a,{gameName:"multisensory_analysis",metrics:{...s,multisensoryData:r}});n&&n.success&&d(n)}catch(r){}})(g,o)}catch(o){t({loading:!1,error:"Erro ao carregar dados sensoriais",sensoryData:null})}})()},[o,a]),n.loading)return e.jsxDEV("div",{className:_a.loadingContainer,children:[e.jsxDEV(T,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:311,columnNumber:9},void 0),e.jsxDEV("p",{className:_a.loadingText,children:"Carregando métricas multissensoriais..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:312,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:310,columnNumber:7},void 0);if(n.error)return e.jsxDEV("div",{className:_a.errorContainer,children:[e.jsxDEV("h3",{children:"⚠️ Erro"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:320,columnNumber:9},void 0),e.jsxDEV("p",{children:n.error},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:321,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:319,columnNumber:7},void 0);if(!n.sensoryData)return e.jsxDEV("div",{className:_a.emptyState,children:[e.jsxDEV("div",{className:_a.emptyStateIcon,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:329,columnNumber:9},void 0),e.jsxDEV("h3",{className:_a.emptyStateText,children:"Nenhum dado disponível"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:330,columnNumber:9},void 0),e.jsxDEV("p",{className:_a.emptyStateSubtext,children:"Comece jogando para ver suas métricas multissensoriais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:331,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:328,columnNumber:7},void 0);const g={labels:["Visual","Auditivo","Tátil","Cognitivo"],datasets:[{label:"Pontuação Sensorial",data:Object.values(n.sensoryData).map(e=>e.score),backgroundColor:["rgba(102, 126, 234, 0.8)","rgba(240, 147, 251, 0.8)","rgba(78, 205, 196, 0.8)","rgba(252, 182, 159, 0.8)"],borderColor:["#667eea","#f093fb","#4ecdc4","#fcb69f"],borderWidth:2}]},j={labels:["Visual","Auditivo","Tátil","Cognitivo"],datasets:[{label:"Perfil Multissensorial",data:Object.values(n.sensoryData).map(e=>e.score),backgroundColor:"rgba(78, 205, 196, 0.2)",borderColor:"#4ecdc4",borderWidth:2,pointBackgroundColor:"#4ecdc4",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"#4ecdc4"}]};return e.jsxDEV("div",{className:_a.dashboardContainer,children:[e.jsxDEV("div",{className:_a.dashboardHeader,children:[e.jsxDEV("h1",{className:_a.dashboardTitle,children:[e.jsxDEV("span",{className:_a.titleIcon,children:"🧠"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:412,columnNumber:11},void 0),"Métricas Multissensoriais"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:411,columnNumber:9},void 0),e.jsxDEV("div",{className:_a.dashboardControls,children:[e.jsxDEV("select",{className:_a.timeframeSelector,value:o,onChange:e=>setTimeframe&&setTimeframe(e.target.value),children:[e.jsxDEV("option",{value:"week",children:"Semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:421,columnNumber:13},void 0),e.jsxDEV("option",{value:"month",children:"Mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:422,columnNumber:13},void 0),e.jsxDEV("option",{value:"quarter",children:"Trimestre"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:423,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:416,columnNumber:11},void 0),e.jsxDEV("button",{className:_a.refreshButton,onClick:()=>window.location.reload(),children:"🔄 Atualizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:425,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:415,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:410,columnNumber:7},void 0),e.jsxDEV("div",{className:_a.sensoryMetricsGrid,children:Object.entries(n.sensoryData).map(([o,a])=>e.jsxDEV("div",{className:`${_a.sensoryMetricCard} ${_a[o]}`,children:[e.jsxDEV("div",{className:_a.sensoryHeader,children:[e.jsxDEV("h3",{className:_a.sensoryTitle,children:o.charAt(0).toUpperCase()+o.slice(1)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:438,columnNumber:15},void 0),e.jsxDEV("div",{className:`${_a.sensoryIcon} ${_a[o]}`,children:["visual"===o&&"👁️","auditory"===o&&"👂","tactile"===o&&"✋","cognitive"===o&&"🧠"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:439,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:437,columnNumber:13},void 0),e.jsxDEV("div",{className:_a.sensoryValue,children:[a.score,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:446,columnNumber:13},void 0),e.jsxDEV("div",{className:_a.sensoryProgress,children:e.jsxDEV("div",{className:`${_a.sensoryProgressBar} ${_a[o]}`,style:{width:`${a.score}%`}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:448,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:447,columnNumber:13},void 0),e.jsxDEV("div",{className:_a.sensoryDetails,children:[e.jsxDEV("span",{children:[a.sessions," sessões"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:454,columnNumber:15},void 0),e.jsxDEV("span",{className:_a.improvement,children:["+",a.improvement,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:455,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:453,columnNumber:13},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:436,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:434,columnNumber:7},void 0),e.jsxDEV("div",{className:_a.chartsGrid,children:[e.jsxDEV("div",{className:_a.chartCard,children:[e.jsxDEV("h3",{className:_a.chartTitle,children:"📊 Distribuição Sensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:463,columnNumber:11},void 0),e.jsxDEV("div",{className:`${_a.chartContainer} ${_a.doughnut}`,children:e.jsxDEV(C,{data:g,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0,max:100}},scales:void 0}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:465,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:464,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:462,columnNumber:9},void 0),e.jsxDEV("div",{className:_a.chartCard,children:[e.jsxDEV("h3",{className:_a.chartTitle,children:"🎯 Perfil Multissensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:470,columnNumber:11},void 0),e.jsxDEV("div",{className:`${_a.chartContainer} ${_a.radar}`,children:e.jsxDEV(P,{data:j,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top"}},scales:{r:{beginAtZero:!0,max:100,ticks:{stepSize:20}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:472,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:471,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:469,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:461,columnNumber:7},void 0),e.jsxDEV("div",{className:_a.insightsSection,children:[e.jsxDEV("h3",{className:_a.insightsTitle,children:"💡 Insights Multissensoriais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:478,columnNumber:9},void 0),e.jsxDEV("div",{className:_a.insightsGrid,children:Object.entries(n.sensoryData).map(([o,a])=>e.jsxDEV("div",{className:`${_a.insightCard} ${_a[o]}`,children:[e.jsxDEV("h4",{className:_a.insightTitle,children:[o.charAt(0).toUpperCase()+o.slice(1),e.jsxDEV("span",{className:`${_a.insightScore} ${_a[o]}`,children:[a.score,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:486,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:484,columnNumber:15},void 0),e.jsxDEV("div",{className:_a.insightContent,children:[e.jsxDEV("p",{children:[a.score>=80&&`Excelente desempenho na modalidade ${o}. Continue praticando para manter o nível.`,a.score>=60&&a.score<80&&`Bom desenvolvimento na área ${o}. Há espaço para crescimento.`,a.score<60&&`Área ${o} com potencial de melhoria. Recomendamos mais atividades focadas.`]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:491,columnNumber:17},void 0),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Sessões:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:496,columnNumber:20},void 0)," ",a.sessions]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:496,columnNumber:17},void 0),e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"Melhoria:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:497,columnNumber:20},void 0)," +",a.improvement,"% no período"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:497,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:490,columnNumber:15},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:483,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:481,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:477,columnNumber:7},void 0),i&&e.jsxDEV("div",{className:_a.aiAnalysisSection,children:[e.jsxDEV("h3",{className:_a.aiAnalysisTitle,children:"🧠 Análise IA Multissensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:507,columnNumber:11},void 0),l?e.jsxDEV("div",{className:_a.aiLoading,children:[e.jsxDEV(T,{size:"small"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:513,columnNumber:15},void 0),e.jsxDEV("span",{children:"Processando análise IA..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:514,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:512,columnNumber:13},void 0):e.jsxDEV("div",{className:_a.aiInsightsGrid,children:i.insights&&e.jsxDEV(e.Fragment,{children:[i.insights.strengths&&i.insights.strengths.length>0&&e.jsxDEV("div",{className:_a.aiInsightCard,children:[e.jsxDEV("div",{className:_a.aiInsightHeader,children:[e.jsxDEV("span",{className:_a.aiInsightIcon,children:"💪"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:524,columnNumber:25},void 0),e.jsxDEV("h4",{children:"Pontos Fortes"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:525,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:523,columnNumber:23},void 0),e.jsxDEV("ul",{className:_a.aiInsightList,children:i.insights.strengths.map((o,a)=>e.jsxDEV("li",{className:_a.aiInsightItem,children:o},a,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:529,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:527,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:522,columnNumber:21},void 0),i.insights.challenges&&i.insights.challenges.length>0&&e.jsxDEV("div",{className:_a.aiInsightCard,children:[e.jsxDEV("div",{className:_a.aiInsightHeader,children:[e.jsxDEV("span",{className:_a.aiInsightIcon,children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:541,columnNumber:25},void 0),e.jsxDEV("h4",{children:"Áreas de Desenvolvimento"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:542,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:540,columnNumber:23},void 0),e.jsxDEV("ul",{className:_a.aiInsightList,children:i.insights.challenges.map((o,a)=>e.jsxDEV("li",{className:_a.aiInsightItem,children:o},a,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:546,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:544,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:539,columnNumber:21},void 0),i.insights.recommendations&&i.insights.recommendations.length>0&&e.jsxDEV("div",{className:_a.aiInsightCard,children:[e.jsxDEV("div",{className:_a.aiInsightHeader,children:[e.jsxDEV("span",{className:_a.aiInsightIcon,children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:558,columnNumber:25},void 0),e.jsxDEV("h4",{children:"Recomendações IA"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:559,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:557,columnNumber:23},void 0),e.jsxDEV("ul",{className:_a.aiInsightList,children:i.insights.recommendations.map((o,a)=>e.jsxDEV("li",{className:_a.aiInsightItem,children:o},a,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:563,columnNumber:27},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:561,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:556,columnNumber:21},void 0),i.insights.modalityProfile&&e.jsxDEV("div",{className:_a.aiInsightCard,children:[e.jsxDEV("div",{className:_a.aiInsightHeader,children:[e.jsxDEV("span",{className:_a.aiInsightIcon,children:"🎨"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:575,columnNumber:25},void 0),e.jsxDEV("h4",{children:"Perfil Sensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:576,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:574,columnNumber:23},void 0),e.jsxDEV("div",{className:_a.modalityProfile,children:[e.jsxDEV("div",{className:_a.modalityPrimary,children:[e.jsxDEV("strong",{children:"Modalidade Primária:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:580,columnNumber:27},void 0)," ",i.insights.modalityProfile.primary]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:579,columnNumber:25},void 0),e.jsxDEV("div",{className:_a.modalitySecondary,children:[e.jsxDEV("strong",{children:"Modalidade Secundária:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:583,columnNumber:27},void 0)," ",i.insights.modalityProfile.secondary]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:582,columnNumber:25},void 0),e.jsxDEV("div",{className:_a.modalityStrength,children:[e.jsxDEV("strong",{children:"Força do Perfil:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:586,columnNumber:27},void 0)," ",Math.round(100*i.insights.modalityProfile.strength),"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:585,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:578,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:573,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:519,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:517,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:506,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/MultisensoryMetricsDashboard/MultisensoryMetricsDashboard.jsx",lineNumber:409,columnNumber:5},void 0)};Ia.propTypes={timeframe:o.string,userId:o.string,isPremiumUser:o.bool};const ya=Object.freeze(Object.defineProperty({__proto__:null,default:Ia},Symbol.toStringTag,{value:"Module"}));l.register(m,u,b,p,v,h,N,g,j);const Ea=()=>{const[o,a]=c.useState(!0),[r,s]=c.useState(new Date),[n,t]=c.useState(null),i=()=>{try{const e=JSON.parse(localStorage.getItem("gameScores")||"[]"),o=JSON.parse(localStorage.getItem("gameSessions")||"[]"),a=JSON.parse(localStorage.getItem("registeredUsers")||"[]"),r=JSON.parse(localStorage.getItem("systemLogs")||"[]"),s=o.length,n=a.length||1,t=e.length>0?e.reduce((e,o)=>e+o.accuracy,0)/e.length:85;return{systems:[{id:"auth",name:"Sistema de Autenticação",status:"active",uptime:"99.9%",responseTime:Math.round(50*Math.random()+80)+"ms",icon:"fas fa-shield-alt",metrics:{activeUsers:n,dailyLogins:Math.max(s,1),failedAttempts:Math.round(5*Math.random())}},{id:"database",name:"Banco de Dados",status:"active",uptime:"99.8%",responseTime:Math.round(30*Math.random()+20)+"ms",icon:"fas fa-database",metrics:{connections:Math.round(2.5*n),queries:Math.max(100*e.length,100),storage:Math.round(30*Math.random()+50)+"%"}},{id:"api",name:"API Gateway",status:t>80?"active":"warning",uptime:"99.5%",responseTime:Math.round(100*Math.random()+150)+"ms",icon:"fas fa-exchange-alt",metrics:{requests:Math.max(50*s,100),errors:Math.round(10*Math.random()),bandwidth:Math.round(40*Math.random()+30)+"%"}},{id:"games",name:"Sistema de Jogos",status:"active",uptime:"99.7%",responseTime:Math.round(80*Math.random()+100)+"ms",icon:"fas fa-gamepad",metrics:{activeSessions:Math.round(.1*s),completedGames:e.filter(e=>e.completed).length,avgScore:Math.round(t)}},{id:"accessibility",name:"Sistema de Acessibilidade",status:"active",uptime:"99.9%",responseTime:Math.round(40*Math.random()+60)+"ms",icon:"fas fa-universal-access",metrics:{activeFeatures:8,usersWithA11y:Math.round(.3*n),compliance:"98%"}}],performance:{cpu:Math.round(30*Math.random()+20),memory:Math.round(40*Math.random()+30),disk:Math.round(25*Math.random()+15),network:Math.round(50*Math.random()+30)},alerts:r.slice(0,5).map((e,o)=>({id:o,type:e.level||"info",message:e.message||"Sistema funcionando normalmente",timestamp:e.timestamp||(new Date).toISOString(),resolved:e.resolved||Math.random()>.3})),analytics:{totalUsers:n,activeSessions:Math.round(.1*s),systemLoad:Math.round(60*Math.random()+20),successRate:Math.round(t),errorRate:Math.round((100-t)/10)}}}catch(e){return{systems:[],performance:{cpu:0,memory:0,disk:0,network:0},alerts:[],analytics:{totalUsers:0,activeSessions:0,systemLoad:0,successRate:0,errorRate:0}}}},d={labels:["CPU","Memória","Disco","Rede"],datasets:[{label:"Utilização (%)",data:[n?.performance?.cpu||0,n?.performance?.memory||0,n?.performance?.disk||0,n?.performance?.network||0],backgroundColor:["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4"],borderWidth:2,borderColor:"#ffffff"}]},l=e=>{switch(e){case"active":return"#059669";case"warning":return"#F59E0B";case"error":return"#DC2626";case"maintenance":return"#6B7280";default:return"#2563EB"}},m=e=>{switch(e){case"active":return"fas fa-check-circle";case"warning":return"fas fa-exclamation-triangle";case"error":return"fas fa-times-circle";case"maintenance":return"fas fa-tools";default:return"fas fa-question-circle"}},u=e=>{switch(e){case"error":return"#DC2626";case"warning":return"#F59E0B";case"info":default:return"#2563EB";case"success":return"#059669"}};return c.useEffect(()=>{(async()=>{a(!0),setTimeout(()=>{const e=i();t(e),a(!1)},700)})()},[]),c.useEffect(()=>{const e=setInterval(()=>{s(new Date)},3e4);return()=>clearInterval(e)},[]),o?e.jsxDEV(T,{message:"Carregando dashboard integrado..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:259,columnNumber:12},void 0):e.jsxDEV("div",{className:"integrated-dashboard",children:[e.jsxDEV("div",{className:"dashboard-header",children:[e.jsxDEV("div",{className:"header-content",children:[e.jsxDEV("h2",{children:"🔧 Dashboard Integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:267,columnNumber:11},void 0),e.jsxDEV("p",{children:"Monitoramento completo do sistema Portal Betina V3"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:268,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:266,columnNumber:9},void 0),e.jsxDEV("div",{className:"refresh-info",children:[e.jsxDEV("span",{children:["Última atualização: ",r.toLocaleTimeString()]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:272,columnNumber:11},void 0),e.jsxDEV("button",{onClick:()=>{const e=i();t(e),s(new Date)},className:"refresh-btn",children:e.jsxDEV("i",{className:"fas fa-sync-alt"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:281,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:273,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:271,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:265,columnNumber:7},void 0),e.jsxDEV("div",{className:"systems-section",children:[e.jsxDEV("h3",{children:"🖥️ Status dos Sistemas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:288,columnNumber:9},void 0),e.jsxDEV("div",{className:"systems-grid",children:n?.systems?.map(o=>e.jsxDEV("div",{className:"system-card",children:[e.jsxDEV("div",{className:"system-header",children:[e.jsxDEV("div",{className:"system-icon",children:e.jsxDEV("i",{className:o.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:294,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:293,columnNumber:17},void 0),e.jsxDEV("div",{className:"system-info",children:[e.jsxDEV("h4",{children:o.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:297,columnNumber:19},void 0),e.jsxDEV("div",{className:"system-status",children:[e.jsxDEV("i",{className:m(o.status),style:{color:l(o.status)}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:299,columnNumber:21},void 0),e.jsxDEV("span",{style:{color:l(o.status)},children:o.status.toUpperCase()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:303,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:298,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:296,columnNumber:17},void 0),e.jsxDEV("div",{className:"system-metrics",children:[e.jsxDEV("div",{className:"metric",children:[e.jsxDEV("span",{className:"label",children:"Uptime:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:310,columnNumber:21},void 0),e.jsxDEV("span",{className:"value",children:o.uptime},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:311,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:309,columnNumber:19},void 0),e.jsxDEV("div",{className:"metric",children:[e.jsxDEV("span",{className:"label",children:"Resposta:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:314,columnNumber:21},void 0),e.jsxDEV("span",{className:"value",children:o.responseTime},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:315,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:313,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:308,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:292,columnNumber:15},void 0),e.jsxDEV("div",{className:"system-details",children:Object.entries(o.metrics).map(([o,a])=>e.jsxDEV("div",{className:"detail-item",children:[e.jsxDEV("span",{className:"detail-label",children:[o.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),":"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:323,columnNumber:21},void 0),e.jsxDEV("span",{className:"detail-value",children:a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:326,columnNumber:21},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:322,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:320,columnNumber:15},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:291,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:289,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:287,columnNumber:7},void 0),e.jsxDEV("div",{className:"analytics-section",children:e.jsxDEV("div",{className:"analytics-grid",children:[e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h4",{children:"📊 Performance do Sistema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:340,columnNumber:13},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(C,{data:d,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#ffffff",font:{size:12}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff"}},scales:{x:{ticks:{color:"#ffffff"},grid:{color:"rgba(255, 255, 255, 0.1)"}},y:{ticks:{color:"#ffffff"},grid:{color:"rgba(255, 255, 255, 0.1)"}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:342,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:341,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:339,columnNumber:11},void 0),e.jsxDEV("div",{className:"metrics-container",children:[e.jsxDEV("h4",{children:"📈 Métricas Gerais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:348,columnNumber:13},void 0),e.jsxDEV("div",{className:"metrics-list",children:[e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-users"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:352,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:351,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Usuários Totais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:355,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:n?.analytics?.totalUsers||0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:356,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:354,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:350,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-play"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:362,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:361,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Sessões Ativas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:365,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:n?.analytics?.activeSessions||0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:366,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:364,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:360,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-tachometer-alt"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:372,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:371,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Carga do Sistema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:375,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:[n?.analytics?.systemLoad||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:376,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:374,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:370,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-check-circle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:382,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:381,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Taxa de Sucesso"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:385,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:[n?.analytics?.successRate||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:386,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:384,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:380,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-exclamation-triangle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:392,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:391,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Taxa de Erro"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:395,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:[n?.analytics?.errorRate||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:396,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:394,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:390,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:349,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:347,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:337,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:336,columnNumber:7},void 0),e.jsxDEV("div",{className:"alerts-section",children:[e.jsxDEV("h3",{children:"🚨 Alertas e Eventos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:406,columnNumber:9},void 0),e.jsxDEV("div",{className:"alerts-container",children:n?.alerts?.length>0?n.alerts.map(o=>e.jsxDEV("div",{className:"alert-item",children:[e.jsxDEV("div",{className:"alert-icon",children:e.jsxDEV("i",{className:"fas fa-circle",style:{color:u(o.type)}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:412,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:411,columnNumber:17},void 0),e.jsxDEV("div",{className:"alert-content",children:[e.jsxDEV("div",{className:"alert-message",children:o.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:418,columnNumber:19},void 0),e.jsxDEV("div",{className:"alert-meta",children:[e.jsxDEV("span",{className:"alert-type",style:{color:u(o.type)},children:o.type.toUpperCase()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:420,columnNumber:21},void 0),e.jsxDEV("span",{className:"alert-time",children:new Date(o.timestamp).toLocaleString()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:423,columnNumber:21},void 0),o.resolved&&e.jsxDEV("span",{className:"alert-resolved",children:[e.jsxDEV("i",{className:"fas fa-check"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:428,columnNumber:25},void 0)," Resolvido"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:427,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:419,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:417,columnNumber:17},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:410,columnNumber:15},void 0)):e.jsxDEV("div",{className:"no-alerts",children:[e.jsxDEV("i",{className:"fas fa-check-circle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:437,columnNumber:15},void 0),e.jsxDEV("span",{children:"Nenhum alerta ativo. Sistema funcionando normalmente."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:438,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:436,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:407,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:405,columnNumber:7},void 0),e.jsxDEV("style",{children:"\n        .integrated-dashboard {\n          padding: 2rem;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          min-height: 100vh;\n          color: white;\n        }\n\n        .dashboard-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          flex-wrap: wrap;\n          gap: 1rem;\n        }\n\n        .header-content h2 {\n          margin: 0;\n          font-size: 2rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .header-content p {\n          margin: 0.5rem 0 0 0;\n          opacity: 0.9;\n        }\n\n        .refresh-info {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          font-size: 0.9rem;\n          opacity: 0.8;\n        }\n\n        .refresh-btn {\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.3);\n          color: white;\n          padding: 0.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          transition: all 0.3s ease;\n        }\n\n        .refresh-btn:hover {\n          background: rgba(255, 255, 255, 0.2);\n        }\n\n        .systems-section,\n        .analytics-section,\n        .alerts-section {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 2rem;\n          margin-bottom: 2rem;\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .systems-section h3,\n        .analytics-section h3,\n        .alerts-section h3 {\n          margin: 0 0 1.5rem 0;\n          color: #4ECDC4;\n        }\n\n        .systems-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n          gap: 1rem;\n        }\n\n        .system-card {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 1rem;\n          padding: 1.5rem;\n        }\n\n        .system-header {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          margin-bottom: 1rem;\n        }\n\n        .system-icon {\n          font-size: 1.5rem;\n          color: #96CEB4;\n        }\n\n        .system-info {\n          flex: 1;\n        }\n\n        .system-info h4 {\n          margin: 0 0 0.5rem 0;\n          color: #FFEAA7;\n        }\n\n        .system-status {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.9rem;\n          font-weight: bold;\n        }\n\n        .system-metrics {\n          display: flex;\n          flex-direction: column;\n          gap: 0.25rem;\n          font-size: 0.8rem;\n        }\n\n        .metric {\n          display: flex;\n          justify-content: space-between;\n          gap: 0.5rem;\n        }\n\n        .label {\n          opacity: 0.8;\n        }\n\n        .value {\n          font-weight: bold;\n        }\n\n        .system-details {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n          gap: 0.5rem;\n          padding-top: 1rem;\n          border-top: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .detail-item {\n          display: flex;\n          flex-direction: column;\n          gap: 0.25rem;\n        }\n\n        .detail-label {\n          font-size: 0.8rem;\n          opacity: 0.8;\n        }\n\n        .detail-value {\n          font-weight: bold;\n          color: #4ECDC4;\n        }\n\n        .analytics-grid {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 2rem;\n        }\n\n        .chart-container,\n        .metrics-container {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 1rem;\n          padding: 1.5rem;\n        }\n\n        .chart-container h4,\n        .metrics-container h4 {\n          margin: 0 0 1rem 0;\n          color: #96CEB4;\n        }\n\n        .chart-wrapper {\n          height: 300px;\n          position: relative;\n        }\n\n        .metrics-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .metric-item {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 1rem;\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 0.5rem;\n        }\n\n        .metric-icon {\n          font-size: 1.2rem;\n          color: #FFEAA7;\n        }\n\n        .metric-info {\n          display: flex;\n          flex-direction: column;\n          gap: 0.25rem;\n        }\n\n        .metric-label {\n          font-size: 0.9rem;\n          opacity: 0.8;\n        }\n\n        .metric-value {\n          font-size: 1.2rem;\n          font-weight: bold;\n          color: #4ECDC4;\n        }\n\n        .alerts-container {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .alert-item {\n          display: flex;\n          align-items: flex-start;\n          gap: 1rem;\n          padding: 1rem;\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 0.5rem;\n        }\n\n        .alert-icon {\n          margin-top: 0.25rem;\n        }\n\n        .alert-content {\n          flex: 1;\n        }\n\n        .alert-message {\n          margin-bottom: 0.5rem;\n          line-height: 1.4;\n        }\n\n        .alert-meta {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          font-size: 0.8rem;\n          opacity: 0.8;\n        }\n\n        .alert-type {\n          font-weight: bold;\n        }\n\n        .alert-resolved {\n          color: #059669;\n          display: flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .no-alerts {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          padding: 2rem;\n          opacity: 0.8;\n          font-style: italic;\n        }\n\n        @media (max-width: 768px) {\n          .integrated-dashboard {\n            padding: 1rem;\n          }\n\n          .dashboard-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .systems-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .analytics-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .chart-wrapper {\n            height: 250px;\n          }\n\n          .system-header {\n            flex-wrap: wrap;\n          }\n\n          .system-details {\n            grid-template-columns: 1fr;\n          }\n        }\n      "},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:444,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:263,columnNumber:5},void 0)},Ra=Object.freeze(Object.defineProperty({__proto__:null,default:Ea},Symbol.toStringTag,{value:"Module"})),Ma={performance:{component:"PerformanceDashboard",title:"Performance Dashboard",description:"Métricas avançadas de performance e uso",access:"premium",icon:"📊",features:["Accuracy detalhada","Tempo de sessão","Pontuação avançada","Progresso completo"]},relatorioA:{component:"AdvancedAIReport",title:"Relatório A - Análise IA",description:"Análise avançada com Inteligência Artificial",access:"premium",icon:"🤖",features:["Análise cognitiva IA","Padrões comportamentais","Previsões desenvolvimento","Mapeamento neural"]},neuropedagogical:{component:"NeuropedagogicalDashboard",title:"Dashboard Neuropedagógico",description:"Métricas especializadas para terapeutas",access:"premium",icon:"🧠",features:["Função executiva","Atenção sustentada","Processamento sensorial","Relatórios profissionais"]},multisensory:{component:"MultisensoryMetricsDashboard",title:"Métricas Multissensoriais",description:"Análise detalhada de interações sensoriais",access:"premium",icon:"🎨",features:["Métricas visuais","Processamento auditivo","Dados táteis","Sensores móveis"]},integrated:{component:"IntegratedSystemDashboard",title:"Sistema Integrado",description:"Visão completa do sistema integrado - APENAS ADMIN",access:"admin",icon:"🔗",features:["Métricas unificadas","Sincronização tempo real","Análise longitudinal","Insights consolidados"]}},Va=e=>"admin"===Ma[e]?.access,Sa=["performance","relatorioA","neuropedagogical","multisensory","integrated"],Ta="_adminGate_1p3zc_13",wa="_backButton_1p3zc_37",ka="_gateContent_1p3zc_83",Ba="_gateIcon_1p3zc_107",za="_gateTitle_1p3zc_141",Oa="_gateMessage_1p3zc_161",Ga="_gateInfo_1p3zc_175",Ua="_accessRequirements_1p3zc_217",La="_contactInfo_1p3zc_269",$a=({title:o="Acesso Restrito",message:a="Este recurso está disponível apenas para administradores do sistema.",onBack:r})=>e.jsxDEV("div",{className:Ta,children:[r&&e.jsxDEV("button",{className:wa,onClick:r,"aria-label":"Voltar para a página anterior",children:"← Voltar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:19,columnNumber:9},void 0),e.jsxDEV("div",{className:ka,children:[e.jsxDEV("div",{className:Ba,children:"🔒"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:29,columnNumber:9},void 0),e.jsxDEV("h2",{className:za,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:33,columnNumber:9},void 0),e.jsxDEV("p",{className:Oa,children:a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:37,columnNumber:9},void 0),e.jsxDEV("div",{className:Ga,children:[e.jsxDEV("h3",{children:"🛡️ Sistema Integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:42,columnNumber:11},void 0),e.jsxDEV("p",{children:"O Sistema Integrado contém informações sensíveis do sistema e métricas avançadas que requerem privilégios administrativos para visualização."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:43,columnNumber:11},void 0),e.jsxDEV("div",{className:Ua,children:[e.jsxDEV("h4",{children:"Requisitos de Acesso:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:49,columnNumber:13},void 0),e.jsxDEV("ul",{children:[e.jsxDEV("li",{children:"✅ Credenciais administrativas válidas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:51,columnNumber:15},void 0),e.jsxDEV("li",{children:"✅ Permissões de sistema integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:52,columnNumber:15},void 0),e.jsxDEV("li",{children:"✅ Sessão administrativa ativa"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:53,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:50,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:48,columnNumber:11},void 0),e.jsxDEV("div",{className:La,children:e.jsxDEV("p",{children:[e.jsxDEV("strong",{children:"📞 Precisa de acesso?"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:59,columnNumber:15},void 0),e.jsxDEV("br",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:59,columnNumber:53},void 0),"Entre em contato com o administrador do sistema para solicitar as permissões necessárias."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:58,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:57,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:41,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:28,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:17,columnNumber:5},void 0);$a.propTypes={title:o.string,message:o.string,onBack:o.func},o.string,o.object,o.number,o.number,o.oneOf(["small","medium","large"]),o.string,o.func,o.func,o.func,o.node.isRequired,o.oneOf(["primary","secondary","success","danger","warning","info","light","dark","link"]),o.oneOf(["small","medium","large"]),o.oneOf(["default","rounded","pill","circle","square"]),o.bool,o.bool,o.bool,o.node,o.oneOf(["left","right"]),o.string,o.func,o.oneOf(["button","submit","reset"]),o.string;const qa=({initialTab:o="performance"})=>{const[a,r]=c.useState(o),[s,n]=c.useState([]),[l,m]=c.useState(!1),[u,b]=c.useState({email:"",password:""}),[p,v]=c.useState(""),[h,N]=c.useState(!1),{isPremium:g,canAccessDashboard:j}=t(),{isAdmin:f,canAccessIntegratedDashboard:x}=i(),{settings:D}=d();c.useEffect(()=>{const e=g?"premium":"public",a=((e,o=!1)=>Object.entries(Ma).filter(([a,r])=>"admin"===r.access?o:"premium"===e&&"premium"===r.access))(e,f);n(a),"performance"===o||((e,o,a=!1)=>{const r=Ma[e];return!!r&&("admin"===r.access?a:"premium"!==r.access||"premium"===o)})(o,e,f)||r("performance")},[g,f,o]);const A=async e=>{e.preventDefault(),N(!0),v("");try{const e=await fetch("/api/auth/dashboard/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:u.email,password:u.password,rememberMe:!0})}),o=await e.json();o.success&&o.token?(localStorage.setItem("authToken",o.token),localStorage.setItem("userData",JSON.stringify(o.user||{email:u.email})),m(!0),v(""),N(!1),C()):(v(o.message||"Erro ao fazer login"),N(!1))}catch(o){v("Erro de conexão. Tente novamente."),N(!1)}},C=async()=>{try{const e=localStorage.getItem("authToken");if(!e)return;const o=await fetch("/api/premium/auth/status",{headers:{Authorization:`Bearer ${e}`}}),a=await o.json();o.ok&&a.success}catch(e){}};c.useEffect(()=>{const e=localStorage.getItem("authToken"),o=localStorage.getItem("userData");if(e&&o)try{JSON.parse(o);m(!0),C()}catch(a){localStorage.removeItem("authToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userData"),m(!1)}else m(!1)},[]);const P=(e,o)=>{b(a=>({...a,[e]:o})),v("")},_=[...s].sort((e,o)=>Sa.indexOf(e[0])-Sa.indexOf(o[0]));return e.jsxDEV("div",{className:I,children:[e.jsxDEV("div",{className:y,children:_.map(([o,s])=>e.jsxDEV("button",{className:`${R} ${a===o?M:""}`,onClick:()=>r(o),"aria-pressed":a===o,children:[e.jsxDEV("span",{"aria-hidden":"true",children:s.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:383,columnNumber:13},void 0)," ",s.title]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:377,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:375,columnNumber:7},void 0),e.jsxDEV("div",{className:E,children:e.jsxDEV("div",{className:V,children:(()=>{if(!l)return e.jsxDEV("div",{style:{minHeight:"100vh",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontFamily:"Segoe UI, Tahoma, Geneva, Verdana, sans-serif"},children:e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",backdropFilter:"blur(20px)",borderRadius:"16px",padding:"2rem",border:"1px solid rgba(255, 255, 255, 0.2)",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)",maxWidth:"360px",width:"100%",margin:"1rem","@media (max-width: 480px)":{padding:"1.5rem",maxWidth:"320px"}},children:["          ",e.jsxDEV("div",{style:{textAlign:"center",marginBottom:"1.5rem"},children:[e.jsxDEV("h1",{style:{fontSize:"1.8rem",marginBottom:"0.5rem"},children:Va(a)?"🔐 Admin":"🔐 Premium"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:212,columnNumber:13},void 0),e.jsxDEV("p",{style:{opacity:.9,lineHeight:1.4,fontSize:"0.9rem"},children:Va(a)?"Credenciais administrativas":"Entre para acessar os dashboards"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:215,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:211,columnNumber:22},void 0),e.jsxDEV("form",{onSubmit:A,style:{display:"flex",flexDirection:"column",gap:"1rem"},children:[e.jsxDEV("div",{children:[e.jsxDEV("label",{style:{display:"block",marginBottom:"0.3rem",fontWeight:"500",fontSize:"0.9rem"},children:"Email:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:225,columnNumber:15},void 0),e.jsxDEV("input",{type:"email",value:u.email,onChange:e=>P("email",e.target.value),style:{width:"100%",padding:"0.8rem",borderRadius:"10px",border:"2px solid rgba(255, 255, 255, 0.3)",background:"rgba(255, 255, 255, 0.1)",color:"white",fontSize:"0.9rem",outline:"none",transition:"all 0.3s ease"},placeholder:"Digite seu email",required:!0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:228,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:224,columnNumber:13},void 0),e.jsxDEV("div",{children:[e.jsxDEV("label",{style:{display:"block",marginBottom:"0.3rem",fontWeight:"500",fontSize:"0.9rem"},children:"Senha:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:249,columnNumber:15},void 0),e.jsxDEV("input",{type:"password",value:u.password,onChange:e=>P("password",e.target.value),style:{width:"100%",padding:"0.8rem",borderRadius:"10px",border:"2px solid rgba(255, 255, 255, 0.3)",background:"rgba(255, 255, 255, 0.1)",color:"white",fontSize:"0.9rem",outline:"none",transition:"all 0.3s ease"},placeholder:"Digite sua senha",required:!0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:252,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:248,columnNumber:13},void 0),p&&e.jsxDEV("div",{style:{background:"rgba(239, 68, 68, 0.2)",color:"#fecaca",padding:"0.8rem",borderRadius:"10px",border:"2px solid #ef4444",textAlign:"center",fontSize:"0.85rem"},children:p},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:273,columnNumber:15},void 0),e.jsxDEV("button",{type:"submit",disabled:h,style:{background:h?"rgba(107, 114, 128, 0.8)":"linear-gradient(135deg, #22c55e, #16a34a)",color:"white",border:"none",borderRadius:"10px",padding:"0.8rem 1.5rem",fontSize:"0.9rem",fontWeight:"600",cursor:h?"not-allowed":"pointer",transition:"all 0.3s ease",textTransform:"uppercase",letterSpacing:"0.5px"},children:h?"🔄 Entrando...":"🔓 Entrar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:286,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:223,columnNumber:11},void 0),e.jsxDEV("div",{style:{marginTop:"1.5rem",padding:"0.8rem",background:"rgba(59, 130, 246, 0.2)",borderRadius:"10px",border:"2px solid #3b82f6",fontSize:"0.8rem",textAlign:"center"},children:[e.jsxDEV("strong",{children:"💡 Demo:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:316,columnNumber:13},void 0),e.jsxDEV("br",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:316,columnNumber:38},void 0),e.jsxDEV("code",{children:"<EMAIL>"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:317,columnNumber:13},void 0)," / ",e.jsxDEV("code",{children:"admin123"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:317,columnNumber:45},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:307,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:196,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:188,columnNumber:19},void 0);if(Va(a)&&(!f||!x()))return e.jsxDEV($a,{title:"Sistema Integrado - Acesso Restrito",message:"Este dashboard contém informações administrativas sensíveis e está disponível apenas para administradores autorizados do sistema."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:339,columnNumber:23},void 0);switch(a){case"performance":return e.jsxDEV(Ee,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:349,columnNumber:16},void 0);case"neuropedagogical":return e.jsxDEV(Pa,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:351,columnNumber:16},void 0);case"multisensory":return e.jsxDEV(Ia,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:353,columnNumber:16},void 0);case"integrated":return e.jsxDEV(Ea,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:355,columnNumber:16},void 0);case"relatorioA":return e.jsxDEV(Fo,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:357,columnNumber:16},void 0);default:return e.jsxDEV(Ee,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:359,columnNumber:16},void 0)}})()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:389,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:388,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:373,columnNumber:5},void 0)};qa.propTypes={initialTab:o.string};export{qa as default};
