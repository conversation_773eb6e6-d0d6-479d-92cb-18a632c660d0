/**
 * 🧩 QUEBRA-CABEÇA V3 - JOGO DE QUEBRA-CABEÇA COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useContext, useCallback, useRef } from 'react'
import { QuebraCabecaConfig, QuebraCabecaV3Config } from './QuebraCabecaConfig'
import { QuebraCabecaMetrics } from './QuebraCabecaMetrics'
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic'
import { useAccessibilityContext } from '../../components/context/AccessibilityContext'
import { SystemContext } from '../../components/context/SystemContext.jsx'
import { v4 as uuidv4 } from 'uuid';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// 🧩 Importar coletores avançados V3
import { QuebraCabecaCollectorsHub } from './collectors/index.js'
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🎨 Importar estilos V3
import styles from './QuebraCabeca.module.css';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - QUEBRA-CABEÇA
const ACTIVITY_TYPES = {
  FREE_ASSEMBLY: {
    id: 'free_assembly',
    name: 'Montagem Livre',
    icon: '🧩',
    description: 'Monte quebra-cabeças livremente',
    component: 'FreeAssemblyActivity'
  },
  GUIDED_ASSEMBLY: {
    id: 'guided_assembly',
    name: 'Montagem Guiada',
    icon: '🎯',
    description: 'Siga as dicas para montar o quebra-cabeça',
    component: 'GuidedAssemblyActivity'
  },
  PATTERN_MATCHING: {
    id: 'pattern_matching',
    name: 'Correspondência de Padrões',
    icon: '🔍',
    description: 'Encontre peças que combinam com padrões',
    component: 'PatternMatchingActivity'
  },
  SHAPE_SORTING: {
    id: 'shape_sorting',
    name: 'Classificação de Formas',
    icon: '📐',
    description: 'Classifique peças por formas e cores',
    component: 'ShapeSortingActivity'
  },
  TIMED_CHALLENGE: {
    id: 'timed_challenge',
    name: 'Desafio Cronometrado',
    icon: '⏱️',
    description: 'Monte quebra-cabeças contra o tempo',
    component: 'TimedChallengeActivity'
  },
  CREATIVE_BUILDING: {
    id: 'creative_building',
    name: 'Construção Criativa',
    icon: '🎨',
    description: 'Crie suas próprias composições',
    component: 'CreativeBuildingActivity'
  }
};

function QuebraCabecaGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.FREE_ASSEMBLY.id,
    activityCycle: [
      ACTIVITY_TYPES.FREE_ASSEMBLY.id,
      ACTIVITY_TYPES.GUIDED_ASSEMBLY.id,
      ACTIVITY_TYPES.PATTERN_MATCHING.id,
      ACTIVITY_TYPES.SHAPE_SORTING.id,
      ACTIVITY_TYPES.TIMED_CHALLENGE.id,
      ACTIVITY_TYPES.CREATIVE_BUILDING.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      freeAssembly: {
        puzzlePieces: [],
        placedPieces: [],
        completedPuzzles: 0
      },
      guidedAssembly: {
        currentHint: null,
        hintsUsed: 0,
        guidanceLevel: 'basic'
      },
      patternMatching: {
        patterns: [],
        matchedPatterns: [],
        currentPattern: null
      },
      shapeSorting: {
        shapes: [],
        sortedShapes: {},
        categories: []
      },
      timedChallenge: {
        timeLimit: 60,
        timeRemaining: 60,
        isTimerActive: false
      },
      creativeBuilding: {
        availablePieces: [],
        userCreation: [],
        savedCreations: []
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0,
    
    // Peças e controle do jogo
    availablePieces: [],
    draggedPiece: null,
    isComplete: false,
    
    // Feedback e interação
    feedback: null,
    showFeedback: false,
    
    // Estatísticas gerais
    gameStats: {
      score: 0,
      completed: 0,
      totalAttempts: 0,
      accuracy: 100,
      sessionStartTime: null,
      roundStartTime: null
    },
    
    // Dados específicos por atividade V3
    activityData: {
      freeAssembly: {
        completionTime: [],
        strategyType: 'systematic',
        pieceSequence: []
      },
      guidedAssembly: {
        instructionAdherence: 100,
        sequenceMemory: [],
        guidanceUsage: 0
      },
      rotationReconstruction: {
        rotationAccuracy: [],
        mentalRotationTime: [],
        spatialErrors: []
      },
      pieceClassification: {
        categoryAccuracy: {},
        sortingStrategy: 'color_first',
        classificationTime: []
      },
      patternIdentification: {
        patternRecognitionSpeed: [],
        logicalAccuracy: [],
        predictionSuccess: []
      },
      collaborativeSolving: {
        cooperationIndex: 0,
        communicationTurns: 0,
        leadershipEvents: []
      }
    },
    
    // Métricas comportamentais V3
    behavioralMetrics: {
      reactionTime: [],
      accuracy: [],
      attentionSpan: 0,
      frustrationEvents: [],
      persistenceLevel: 0,
      engagementScore: 100,
      multisensoryProcessing: {},
      activitySpecific: {}
    }
  });

  // ✅ REFS PARA CONTROLE DE SESSÃO
  const sessionIdRef = useRef(null);
  const roundStartTimeRef = useRef(null);
  
  // 🧠 Integração com sistema unificado de métricas
  const { 
    collectMetrics, 
    processGameSession, 
    initializeSession,
    processAdvancedMetrics // Novo: para AdvancedMetricsEngine
  } = useUnifiedGameLogic('QuebraCabeca')

  // 🧩 Inicializar coletores avançados V3
  const [collectorsHub] = useState(() => new QuebraCabecaCollectorsHub())

  // TTS control
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('quebraCabeca_ttsActive');
    return saved ? JSON.parse(saved) : true;
  });

  // 🔄 Hook multissensorial integrado
  const multisensoryIntegration = useMultisensoryIntegration('puzzle-game-v3', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info'
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    gameType: 'puzzle-game',
    collectorsHub,
    multisensoryIntegration,
    autoUpdate: true,
    logLevel: 'info'
  });

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext()

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)

  // ✅ FUNÇÕES V3 - GERAÇÃO DE ATIVIDADES

  // 🎯 Gerar conteúdo para atividade atual
  const generateActivityContent = useCallback((activityId, difficulty) => {
    const config = QuebraCabecaV3Config.DIFFICULTY_CONFIGS[difficulty.toUpperCase()];
    const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];
    
    switch (activityId) {
      case 'free_assembly':
        return generateFreeAssemblyActivity(config, activityConfig);
      case 'guided_assembly':
        return generateGuidedAssemblyActivity(config, activityConfig);
      case 'rotation_reconstruction':
        return generateRotationReconstructionActivity(config, activityConfig);
      case 'piece_classification':
        return generatePieceClassificationActivity(config, activityConfig);
      case 'pattern_identification':
        return generatePatternIdentificationActivity(config, activityConfig);
      case 'collaborative_solving':
        return generateCollaborativeSolvingActivity(config, activityConfig);
      default:
        return generateFreeAssemblyActivity(config, activityConfig);
    }
  }, []);

  // 🧩 Gerar atividade de montagem livre
  const generateFreeAssemblyActivity = useCallback((difficultyConfig, activityConfig) => {
    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig.emotionComplexity];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];
    
    const difficultySettings = activityConfig.difficulties[gameState.difficulty];
    const correctPieces = randomEmotion.pieces.slice(0, difficultySettings.pieces);
    const distractorPieces = generateDistractorPieces(correctPieces, difficultySettings.distractors);
    
    return {
      emotion: randomEmotion,
      correctPieces,
      availablePieces: [...correctPieces, ...distractorPieces].sort(() => Math.random() - 0.5),
      gridSize: difficultySettings.gridSize,
      timeLimit: difficultySettings.timeLimit,
      instruction: `Monte o quebra-cabeça da emoção "${randomEmotion.name}"`,
      targetSlots: difficultySettings.pieces
    };
  }, [gameState.difficulty]);

  // 🎯 Gerar atividade de montagem guiada
  const generateGuidedAssemblyActivity = useCallback((difficultyConfig, activityConfig) => {
    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig.emotionComplexity];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];
    
    const difficultySettings = activityConfig.difficulties[gameState.difficulty];
    const sequence = randomEmotion.pieces.slice(0, difficultySettings.sequenceLength);
    
    return {
      emotion: randomEmotion,
      guidedSequence: sequence,
      currentStep: 0,
      guidanceType: difficultySettings.guidanceType,
      pauseBetweenSteps: difficultySettings.pauseBetweenSteps,
      allowRepeats: difficultySettings.allowRepeats,
      instruction: `Siga a sequência: ${sequence.map((p, i) => `${i + 1}. ${p}`).join(' → ')}`,
      completed: false
    };
  }, [gameState.difficulty]);

  // 🔄 Gerar atividade de reconstrução por rotação
  const generateRotationReconstructionActivity = useCallback((difficultyConfig, activityConfig) => {
    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig.emotionComplexity];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];
    
    const difficultySettings = activityConfig.difficulties[gameState.difficulty];
    const pieces = randomEmotion.pieces.slice(0, difficultySettings.pieceCount);
    
    const rotatedPieces = pieces.map(piece => ({
      original: piece,
      rotated: piece, // Simular rotação visual
      angle: difficultySettings.rotationAngles[Math.floor(Math.random() * difficultySettings.rotationAngles.length)],
      placed: false
    }));
    
    return {
      emotion: randomEmotion,
      rotatedPieces,
      showPreview: difficultySettings.rotationPreview,
      timePerPiece: difficultySettings.timePerPiece,
      instruction: `Mentalize a rotação e monte as peças na posição correta`,
      completedRotations: 0
    };
  }, [gameState.difficulty]);

  // 🎨 Gerar atividade de classificação de peças
  const generatePieceClassificationActivity = useCallback((difficultyConfig, activityConfig) => {
    const difficultySettings = activityConfig.difficulties[gameState.difficulty];
    const categories = generateClassificationCategories(difficultySettings);
    const pieces = generatePiecesForClassification(categories, difficultySettings);
    
    return {
      categories,
      pieces,
      sortingCriteria: difficultySettings.sortingCriteria,
      showHints: difficultySettings.categoryHints,
      instruction: `Organize as peças nas categorias corretas: ${Object.keys(categories).join(', ')}`,
      classified: {}
    };
  }, [gameState.difficulty]);

  // 🔍 Gerar atividade de identificação de padrões
  const generatePatternIdentificationActivity = useCallback((difficultyConfig, activityConfig) => {
    const difficultySettings = activityConfig.difficulties[gameState.difficulty];
    const pattern = generateLogicalPattern(difficultySettings);
    const options = generatePatternOptions(pattern, difficultySettings);
    
    return {
      pattern,
      options,
      patternType: pattern.type,
      hintLevel: difficultySettings.hintLevel,
      instruction: `Complete o padrão lógico observando a sequência`,
      solved: false
    };
  }, [gameState.difficulty]);

  // 🧠 Gerar atividade de resolução colaborativa
  const generateCollaborativeSolvingActivity = useCallback((difficultyConfig, activityConfig) => {
    const emotionLibrary = QuebraCabecaV3Config.EMOTIONS_LIBRARY[difficultyConfig.emotionComplexity];
    const randomEmotion = emotionLibrary[Math.floor(Math.random() * emotionLibrary.length)];
    
    const difficultySettings = activityConfig.difficulties[gameState.difficulty];
    const totalPieces = difficultySettings.sharedPieces;
    const playerPieces = randomEmotion.pieces.slice(0, Math.ceil(totalPieces / 2));
    const partnerPieces = randomEmotion.pieces.slice(Math.ceil(totalPieces / 2), totalPieces);
    
    return {
      emotion: randomEmotion,
      playerPieces,
      partnerPieces,
      teamSize: difficultySettings.teamSize,
      communicationLevel: difficultySettings.communicationLevel,
      instruction: `Trabalhe em equipe para montar "${randomEmotion.name}"`,
      partnerActions: [],
      turnPhase: 'player'
    };
  }, [gameState.difficulty]);

  // 🔧 Funções auxiliares de geração
  const generateDistractorPieces = useCallback((correctPieces, count) => {
    const allPieces = ['😊', '😢', '😲', '😠', '😌', '🤩', '🌞', '🌧️', '💔', '🎁', '🎉', '❓', '✨', '🌊', '🕊️', '🌿', '💥', '🌋', '⚡', '🎪', '🎢', '🎊'];
    const distractors = allPieces.filter(piece => !correctPieces.includes(piece));
    return distractors.sort(() => Math.random() - 0.5).slice(0, count);
  }, []);

  const generateClassificationCategories = useCallback((settings) => {
    const baseCategories = {
      emotions: { name: 'Emoções', icon: '😊', pieces: [] },
      nature: { name: 'Natureza', icon: '🌿', pieces: [] },
      objects: { name: 'Objetos', icon: '🎁', pieces: [] },
      symbols: { name: 'Símbolos', icon: '✨', pieces: [] }
    };
    
    const selectedCategories = Object.keys(baseCategories).slice(0, settings.categories);
    const result = {};
    selectedCategories.forEach(key => {
      result[key] = baseCategories[key];
    });
    
    return result;
  }, []);

  const generatePiecesForClassification = useCallback((categories, settings) => {
    const categoryKeys = Object.keys(categories);
    const pieces = [];
    
    categoryKeys.forEach(categoryKey => {
      const categoryPieces = getCategoryPieces(categoryKey);
      const selectedPieces = categoryPieces.slice(0, settings.piecesPerCategory);
      pieces.push(...selectedPieces.map(piece => ({ piece, category: categoryKey })));
    });
    
    return pieces.sort(() => Math.random() - 0.5);
  }, []);

  const getCategoryPieces = useCallback((category) => {
    const categoryMap = {
      emotions: ['😊', '😢', '😲', '😠', '😌', '🤩'],
      nature: ['🌞', '🌧️', '🌊', '🕊️', '🌿', '🌋'],
      objects: ['🎁', '🎉', '🎪', '🎢', '🎊', '💔'],
      symbols: ['❓', '✨', '💥', '⚡', '🏆', '⭐']
    };
    return categoryMap[category] || [];
  }, []);

  const generateLogicalPattern = useCallback((settings) => {
    const patternTypes = settings.patternTypes;
    const selectedType = patternTypes[Math.floor(Math.random() * patternTypes.length)];
    
    switch (selectedType) {
      case 'alternating':
        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };
      case 'sequential':
        return { type: 'sequential', sequence: ['😊', '🌞', '😢', '🌧️'], next: '😲' };
      case 'progressive':
        return { type: 'progressive', sequence: ['😊', '🤩', '😢', '😭'], next: '😡' };
      default:
        return { type: 'alternating', sequence: ['😊', '😢', '😊', '😢'], next: '😊' };
    }
  }, []);

  const generatePatternOptions = useCallback((pattern, settings) => {
    const correctAnswer = pattern.next;
    const wrongOptions = ['😲', '🤔', '😴', '🥳'].filter(opt => opt !== correctAnswer);
    const selectedWrong = wrongOptions.slice(0, settings.completionOptions - 1);
    
    return [correctAnswer, ...selectedWrong].sort(() => Math.random() - 0.5);
  }, []);

  // ✅ FUNÇÕES V3 - ROTAÇÃO DE ATIVIDADES

  // 🔄 Rotacionar para próxima atividade
  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const nextIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextIndex];
      
      // Registrar rotação de atividade
      if (multisensoryIntegration.recordInteraction) {
        multisensoryIntegration.recordInteraction('activity_rotation', {
          from: prev.currentActivity,
          to: nextActivity,
          automatic: true,
          round: prev.round
        });
      }
      
      const newState = {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextIndex,
        activityRoundCount: 0,
        round: prev.round + 1,
        roundStartTime: Date.now()
      };
      
      // Gerar novo conteúdo para a atividade
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      // Atualizar dados específicos da atividade
      newState.currentEmotion = activityContent.emotion || null;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      
      // Anunciar nova atividade
      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];
      const activityName = activityConfig?.name || 'Nova Atividade';
      
      setTimeout(() => {
        if (speak) {
          speak(`Nova atividade: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });
        }
      }, 500);
      
      return newState;
    });
  }, [generateActivityContent, multisensoryIntegration, speak]);

  // 🎯 Trocar atividade manualmente
  const switchActivity = useCallback((activityId) => {
    setGameState(prev => {
      const activityIndex = prev.activityCycle.indexOf(activityId);
      if (activityIndex === -1) return prev;
      
      const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[activityId.toUpperCase()];
      const activityName = activityConfig?.name || 'Nova Atividade';
      
      // Registrar troca manual de atividade
      if (multisensoryIntegration.recordInteraction) {
        multisensoryIntegration.recordInteraction('activity_switch', {
          from: prev.currentActivity,
          to: activityId,
          manual: true,
          round: prev.round
        });
      }
      
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityIndex: activityIndex,
        activityRoundCount: 0,
        round: prev.round + 1,
        roundStartTime: Date.now()
      };
      
      // Gerar novo conteúdo
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      newState.currentEmotion = activityContent.emotion || null;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      
      // Anunciar atividade
      setTimeout(() => {
        if (speak) {
          speak(`Atividade alterada para: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });
        }
      }, 300);
      
      return newState;
    });
  }, [generateActivityContent, multisensoryIntegration, speak]);

  // ✅ FUNÇÕES V3 - GERAÇÃO DE NOVA RODADA
  const generateNewRound = useCallback(() => {
    console.log('🎯 Gerando nova rodada V3...', { 
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round 
    });
    
    setGameState(prev => {
      // Verificar se precisa rotar atividade
      const shouldRotateActivity = prev.activityRoundCount >= prev.roundsPerActivity;
      
      console.log('🔄 Verificando rotação de atividade:', { 
        shouldRotateActivity, 
        activityRoundCount: prev.activityRoundCount,
        roundsPerActivity: prev.roundsPerActivity 
      });
      
      let newState = { ...prev };
      
      if (shouldRotateActivity) {
        // Rotar para próxima atividade
        const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
        const nextActivity = prev.activityCycle[nextActivityIndex];
        
        console.log('🎮 Rotacionando para nova atividade:', { 
          from: prev.currentActivity, 
          to: nextActivity,
          nextActivityIndex 
        });
        
        newState = {
          ...newState,
          currentActivity: nextActivity,
          activityIndex: nextActivityIndex,
          activityRoundCount: 0,
          activitiesCompleted: prev.activitiesCompleted + 1
        };
        
        // 🔊 Anunciar nova atividade
        const activityConfig = QuebraCabecaV3Config.ACTIVITY_CONFIG[nextActivity.toUpperCase()];
        const activityName = activityConfig?.name || 'Nova Atividade';
        setTimeout(() => {
          if (speak) {
            speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });
          }
        }, 500);
      }
      
      // Gerar conteúdo específico da atividade
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      console.log('📝 Conteúdo gerado para atividade:', { 
        activity: newState.currentActivity, 
        content: activityContent 
      });
      
      // Atualizar estado baseado na atividade atual
      newState.currentEmotion = activityContent.emotion || newState.currentEmotion;
      newState.puzzlePieces = activityContent.availablePieces || activityContent.pieces || [];
      newState.placedPieces = new Array(activityContent.targetSlots || 3).fill(null);
      newState.isComplete = false;
      newState.draggedPiece = null;
      newState.feedback = null;
      newState.showFeedback = false;
      
      // Incrementar contadores
      newState.activityRoundCount = newState.activityRoundCount + 1;
      newState.round = newState.round + 1;
      newState.roundStartTime = Date.now();
      
      return newState;
    });
  }, [gameState.currentActivity, gameState.activityRoundCount, gameState.round, generateActivityContent, speak]);

  // 🔊 Cleanup do TTS quando componente é desmontado
  useEffect(() => {
    return () => {
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // Estados para métricas avançadas
  const [sessionStartTime, setSessionStartTime] = useState(null)
  const [pieceInteractions, setPieceInteractions] = useState([])
  const [spatialStrategies, setSpatialStrategies] = useState([])
  const [problemSolvingApproach, setProblemSolvingApproach] = useState('systematic')

  // Inicializar sessão ao começar o jogo
  useEffect(() => {
    if (gameStarted && !sessionStartTime) {
      setSessionStartTime(Date.now())
      initializeSession({
        gameType: 'QuebraCabeca',
        difficulty,
        timestamp: new Date().toISOString()
      })
    }
  }, [gameStarted, sessionStartTime, difficulty, initializeSession])

  // Calcular precisão
  const getAccuracy = useCallback(() => {
    if (gameStats.totalAttempts === 0) return 100
    return Math.round((gameStats.completed / gameStats.totalAttempts) * 100)
  }, [gameStats])

  // 🧠 Coletar métricas específicas do quebra-cabeça
  const collectPuzzleMetrics = async () => {
    const currentTime = Date.now()
    const sessionDuration = sessionStartTime ? currentTime - sessionStartTime : 0

    // Métricas básicas do jogo
    const basicMetrics = {
      totalTime: sessionDuration,
      correctAnswers: gameStats.completed,
      incorrectAnswers: gameStats.totalAttempts - gameStats.completed,
      accuracy: getAccuracy(),
      difficultyLevel: difficulty,
      completionLevel: isComplete ? 100 : (placedPieces.length / puzzlePieces.length) * 100
    }

    // Métricas específicas do quebra-cabeça
    const puzzleSpecificMetrics = {
      spatialReasoning: calculateSpatialReasoning(),
      piecePlacementAccuracy: calculatePlacementAccuracy(),
      completionStrategy: identifyCompletionStrategy(),
      visualSpatialMemory: analyzeVisualSpatialMemory(),
      problemSolvingApproach: problemSolvingApproach,
      frustranceTolerance: calculateFrustranceTolerance(),
      persistenceLevel: calculatePersistenceLevel(),
      rotationAttempts: countRotationAttempts(),
      sequentialPlacement: analyzeSequentialPlacement()
    }

    // Coletar métricas através do sistema unificado
    await collectMetrics({
      ...basicMetrics,
      ...puzzleSpecificMetrics
    })

    // 🚀 Processar métricas avançadas para análise espacial
    await processAdvancedMetrics({
      gameType: 'QuebraCabeca',
      sessionData: {
        ...basicMetrics,
        ...puzzleSpecificMetrics,
        interactions: pieceInteractions,
        spatialStrategies: spatialStrategies,
        duration: sessionDuration
      },
      userProfile: {
        preferredDifficulty: difficulty,
        spatialPreferences: identifySpatialPreferences()
      }
    })
  }

  // Funções auxiliares para análise de métricas espaciais
  const calculateSpatialReasoning = () => {
    const correctPlacements = pieceInteractions.filter(i => i.correct).length
    const totalInteractions = pieceInteractions.length
    return totalInteractions > 0 ? (correctPlacements / totalInteractions) * 100 : 0
  }

  const calculatePlacementAccuracy = () => {
    const firstTryCorrect = pieceInteractions.filter(i => i.attemptNumber === 1 && i.correct).length
    const totalPieces = puzzlePieces.length
    return totalPieces > 0 ? (firstTryCorrect / totalPieces) * 100 : 0
  }

  const identifyCompletionStrategy = () => {
    if (spatialStrategies.length === 0) return 'unknown'
    
    const strategies = spatialStrategies.reduce((count, strategy) => {
      count[strategy] = (count[strategy] || 0) + 1
      return count
    }, {})
    
    return Object.keys(strategies).reduce((a, b) => strategies[a] > strategies[b] ? a : b)
  }

  const analyzeVisualSpatialMemory = () => {
    const memoryScore = pieceInteractions.reduce((score, interaction, index) => {
      if (index > 0) {
        const prevInteraction = pieceInteractions[index - 1]
        if (interaction.pieceId === prevInteraction.pieceId && 
            interaction.position.x === prevInteraction.position.x &&
            interaction.position.y === prevInteraction.position.y) {
          score += 10 // Bonus por lembrar posição
        }
      }
      return score
    }, 0)
    
    return Math.min(memoryScore, 100)
  }

  const calculateFrustranceTolerance = () => {
    const incorrectAttempts = pieceInteractions.filter(i => !i.correct).length
    const totalAttempts = pieceInteractions.length
    
    if (totalAttempts === 0) return 100
    
    // Menor % de tentativas incorretas = maior tolerância
    return Math.max(0, 100 - ((incorrectAttempts / totalAttempts) * 100))
  }

  const calculatePersistenceLevel = () => {
    const maxAttemptsPerPiece = Math.max(...puzzlePieces.map(piece => {
      return pieceInteractions.filter(i => i.pieceId === piece.id).length
    }), 1)
    
    // Persistência baseada no número máximo de tentativas por peça
    return Math.min(maxAttemptsPerPiece * 20, 100)
  }

  const countRotationAttempts = () => {
    return pieceInteractions.filter(i => i.action === 'rotate').length
  }

  const analyzeSequentialPlacement = () => {
    if (pieceInteractions.length < 2) return 'insufficient_data'
    
    let sequentialCount = 0
    for (let i = 1; i < pieceInteractions.length; i++) {
      const curr = pieceInteractions[i]
      const prev = pieceInteractions[i - 1]
      
      if (curr.pieceId === prev.pieceId + 1) {
        sequentialCount++
      }
    }
    
    const sequentialRatio = sequentialCount / (pieceInteractions.length - 1)
    return sequentialRatio > 0.7 ? 'sequential' : sequentialRatio > 0.4 ? 'mixed' : 'random'
  }

  const identifySpatialPreferences = () => {
    return {
      preferredStartPosition: 'top-left', // Placeholder
      rotationFrequency: countRotationAttempts() / Math.max(pieceInteractions.length, 1),
      systematicApproach: problemSolvingApproach === 'systematic'
    }
  }

  // 🧩 Registrar interação com peça para análise cognitiva
  const recordPieceInteraction = async (pieceId, position, action, correct) => {
    const interactionData = {
      pieceId,
      position,
      action,
      correct,
      timestamp: Date.now(),
      attemptNumber: attemptCount + 1,
      difficultyLevel: difficulty,
      sessionTime: Date.now() - (sessionStartTime || Date.now())
    }

    // Adicionar à lista de interações
    setPieceInteractions(prev => [...prev, interactionData])
    
    // Coletar dados com os coletores especializados
    try {
      await collectorsHub.collectMoveData({
        ...interactionData,
        puzzleState: {
          totalPieces: puzzlePieces.length,
          placedPieces: placedPieces.length,
          completionPercentage: (placedPieces.filter(p => p !== null).length / puzzlePieces.length) * 100
        }
      });
      
      // 🔄 Registrar interação multissensorial
      await multisensoryIntegration.recordInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: interactionData,
        multisensoryProcessing: {
          spatialProcessing: { spatialReasoning: 0.7, visualSpatialMemory: 0.7, spatialOrientation: 0.7 },
          cognitiveProcessing: { problemSolving: 0.7, processingSpeed: 0.7, adaptability: 0.7 },
          behavioralProcessing: { interactionCount: 0.7, averageResponseTime: 0.7, persistence: 0.7 }
        }
      });

      // A cada 3 tentativas, fazer análise cognitiva completa
      const newAttemptCount = attemptCount + 1
      setAttemptCount(newAttemptCount)

      if (newAttemptCount % 3 === 0) {
        performCognitiveAnalysis()
      }

    } catch (error) {
      console.error('Erro ao coletar dados da interação:', error)
    }
  }

  // 🧠 Realizar análise cognitiva completa
  const performCognitiveAnalysis = async () => {
    try {
      setCognitiveAnalysisVisible(true)
      
      const analysisData = await collectorsHub.collectComprehensiveData({
        sessionId: sessionStartTime?.toString() || 'session',
        gameState: {
          difficulty,
          currentLevel: gameStats.level,
          score: gameStats.score,
          accuracy: getAccuracy(),
          isComplete
        },
        interactions: pieceInteractions,
        spatialData: {
          strategies: spatialStrategies,
          approach: problemSolvingApproach
        }
      })

      setAnalysisResults(analysisData)

      // Manter visível por 3 segundos
      setTimeout(() => {
        setCognitiveAnalysisVisible(false)
      }, 3000)

    } catch (error) {
      console.error('Erro na análise cognitiva:', error)
      setCognitiveAnalysisVisible(false)
    }
  }

  // Iniciar o jogo
  const startGame = useCallback(async () => {
    setGameStarted(true)
    setGameStats({
      level: 1,
      score: 0,
      completed: 0,
      totalAttempts: 0
    });

    // 🔄 Inicializar integração multissensorial
    try {
      await multisensoryIntegration.initializeSession(`session_${Date.now()}`, {
        difficulty: difficulty,
        gameMode: 'puzzle_solving',
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
    }

    generateNewPuzzle()
  }, [multisensoryIntegration, difficulty, user]);

  // Gerar novo quebra-cabeça
  const generateNewPuzzle = useCallback(() => {
    const randomEmotion = QuebraCabecaConfig.emotions[Math.floor(Math.random() * QuebraCabecaConfig.emotions.length)]
    const difficultyData = QuebraCabecaConfig.difficulties.find(d => d.id === difficulty)
    
    setCurrentEmotion(randomEmotion)
    setIsComplete(false)
    setFeedback(null)
    setGameStats(prev => ({ ...prev, totalAttempts: prev.totalAttempts + 1 }))

    // Criar peças com posições corretas definidas
    const pieces = randomEmotion.pieces.slice(0, difficultyData.pieces).map((piece, index) => ({
      id: index,
      content: piece,
      placed: false,
      correctPosition: index // Posição correta da peça
    }))
    
    // Embaralhar as peças para o usuário
    setPuzzlePieces(pieces.sort(() => Math.random() - 0.5))
    setPlacedPieces(Array(difficultyData.pieces).fill(null))
  }, [difficulty]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('quebraCabeca_ttsActive', JSON.stringify(newState));
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);

  // Lidar com início do drag
  const handleDragStart = useCallback((piece) => {
    setDraggedPiece(piece)
  }, [])

  // Lidar com drop
  const handleDrop = useCallback((targetIndex) => {
    if (!draggedPiece) return

    const newPlacedPieces = [...placedPieces]
    newPlacedPieces[targetIndex] = draggedPiece

    // Verificar se o posicionamento está correto
    const isCorrectPlacement = draggedPiece.correctPosition === targetIndex
    
    // 🧠 Registrar interação com métricas avançadas
    recordPieceInteraction(
      draggedPiece.id, 
      { x: targetIndex % 2, y: Math.floor(targetIndex / 2) }, 
      'place', 
      isCorrectPlacement
    )

    setPlacedPieces(newPlacedPieces)
    setPuzzlePieces(prev => prev.filter(p => p.id !== draggedPiece.id))
    setDraggedPiece(null)

    // Feedback sonoro para o usuário (substituindo feedback visual)
    if (isCorrectPlacement) {
      speak('Peça colocada corretamente!');
    } else {
      speak('Tente uma posição diferente!');
      // Atualizar estratégia de resolução para trial-error
      setProblemSolvingApproach('trial_error')
    }

    // Verificar se completou
    if (newPlacedPieces.every(piece => piece !== null)) {
      handlePuzzleComplete()
    }
  }, [draggedPiece, placedPieces, recordPieceInteraction, speak, setProblemSolvingApproach, handlePuzzleComplete]);

  // Lidar com quebra-cabeça completo
  const handlePuzzleComplete = async () => {
    setIsComplete(true)
    
    const points = QuebraCabecaConfig.gameSettings.pointsByDifficulty[difficulty.toUpperCase()] || 10
    setGameStats(prev => ({
      ...prev,
      completed: prev.completed + 1,
      score: prev.score + points,
      level: prev.level + 1
    }))

    const message = QuebraCabecaConfig.encouragingMessages[Math.floor(Math.random() * QuebraCabecaConfig.encouragingMessages.length)]
    
    // Feedback sonoro ao completar (substituindo feedback visual)
    speak(`${message} Você ganhou ${points} pontos!`);

    // 🧠 Coletar métricas completas ao completar o quebra-cabeça
    try {
      await collectPuzzleMetrics()
      console.log('🧩 Métricas do quebra-cabeça coletadas com sucesso!')
    } catch (error) {
      console.error('❌ Erro ao coletar métricas do quebra-cabeça:', error)
    }
  }

  // Próximo quebra-cabeça
  const nextPuzzle = () => {
    // Resetar métricas para nova rodada
    setPieceInteractions([])
    setSpatialStrategies([])
    setProblemSolvingApproach('systematic')
    
    setTimeout(() => {
      generateNewPuzzle()
    }, 100)
  }

  // Função para finalizar sessão e coletar métricas finais
  const handleGameEnd = async () => {
    if (sessionStartTime && pieceInteractions.length > 0) {
      try {
        await collectPuzzleMetrics()
        console.log('🎯 Métricas finais do quebra-cabeça coletadas!')
      } catch (error) {
        console.error('❌ Erro ao coletar métricas finais:', error)
      }
    }
    
    if (onBack) {
      onBack()
    }
  }

  // Função para iniciar o jogo a partir da tela inicial
  const handleGameStart = (selectedDifficulty) => {
    setDifficulty(selectedDifficulty)
    setShowStartScreen(false)
    setTimeout(() => {
      startGame()
    }, 100)
  }
  // Se ainda não iniciou, mostra a tela de início
  // Se ainda não iniciou, mostra a tela de início
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Quebra-Cabeça Emocional"
        gameSubtitle="Monte emoções e aprenda sobre sentimentos"
        gameInstruction="Arraste as peças para formar uma face emocional completa!"
        gameIcon="🧩"
        difficulties={[
          {
            id: 'easy',
            name: 'Fácil',
            description: '3 peças simples - Ideal para iniciantes',
            icon: '😊',
            preview: (
              <div style={{ display: 'flex', gap: '4px', justifyContent: 'center' }}>
                <div style={{ fontSize: '16px' }}>😊</div>
                <div style={{ fontSize: '12px' }}>🧩</div>
                <div style={{ fontSize: '12px' }}>🧩</div>
              </div>
            )
          },
          {
            id: 'medium',
            name: 'Médio',
            description: '4 peças - Desafio equilibrado',
            icon: '🎯',
            preview: (
              <div style={{ display: 'flex', gap: '3px', justifyContent: 'center' }}>
                <div style={{ fontSize: '14px' }}>😍</div>
                <div style={{ fontSize: '10px' }}>🧩</div>
                <div style={{ fontSize: '10px' }}>🧩</div>
                <div style={{ fontSize: '10px' }}>🧩</div>
              </div>
            )
          },
          {
            id: 'hard',
            name: 'Avançado',
            description: '4 peças complexas - Para especialistas',
            icon: '🚀',
            preview: (
              <div style={{ display: 'flex', gap: '2px', justifyContent: 'center', flexWrap: 'wrap' }}>
                <div style={{ fontSize: '12px' }}>🤔</div>
                <div style={{ fontSize: '8px' }}>🧩</div>
                <div style={{ fontSize: '8px' }}>🧩</div>
                <div style={{ fontSize: '8px' }}>🧩</div>
                <div style={{ fontSize: '8px' }}>🧩</div>
              </div>
            )
          }
        ]}
        customContent={
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '16px',
            padding: '1.5rem',
            marginBottom: '1rem'
          }}>
            <h3 style={{ marginBottom: '1rem', color: 'white', textAlign: 'center' }}>
              🧩 Este jogo desenvolve:
            </h3>
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(180px, 1fr))',
              gap: '1rem'
            }}>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>😊</div>
                <div>Reconhecimento Emocional</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🧩</div>
                <div>Raciocínio Lógico</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🤏</div>
                <div>Coordenação Motora</div>
              </div>
              <div style={{ textAlign: 'center', color: 'white' }}>
                <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🎯</div>
                <div>Concentração</div>
              </div>
            </div>
          </div>
        }
        onStart={(difficulty) => handleGameStart(difficulty)}
        onBack={onBack}
      />
    )
  }
  return (
    <div 
      className={`${styles.quebraCabecaGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {/* 🧠 Indicador de Análise Cognitiva */}
      {cognitiveAnalysisVisible && (
        <div className="cognitive-analysis-indicator">
          <div className="analysis-content">
            <div className="analysis-icon">🧩🧠</div>
            <div className="analysis-text">
              <div className="analysis-title">Análise Espacial em Progresso</div>
              <div className="analysis-details">
                Avaliando raciocínio espacial e estratégias de resolução...
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 📊 Painel de Insights Cognitivos */}
      {analysisResults && (
        <div className="cognitive-insights-panel">
          <div className="insights-header">
            <span className="insights-icon">🎯</span>
            <span className="insights-title">Análise Cognitiva</span>
          </div>
          <div className="insights-content">
            <div className="insight-item">
              <span className="insight-label">Raciocínio Espacial:</span>
              <span className="insight-value">{analysisResults.spatialReasoning || 'Em análise'}</span>
            </div>
            <div className="insight-item">
              <span className="insight-label">Estratégia:</span>
              <span className="insight-value">{analysisResults.strategy || problemSolvingApproach}</span>
            </div>
            <div className="insight-item">
              <span className="insight-label">Padrão Cognitivo:</span>
              <span className="insight-value">{analysisResults.cognitivePattern || 'Identificando...'}</span>
            </div>
          </div>
        </div>
      )}

      <div className={styles.gameContent}>
        {/* Header do jogo */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🧩 Quebra-Cabeça Emocional
            <div className={styles.activitySubtitle}>
              Monte emoções e aprenda sobre sentimentos
            </div>
          </h1>          
          <button 
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            aria-label={ttsActive ? "Desativar TTS" : "Ativar TTS"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {currentEmotion && (
          <div className={styles.puzzleArea}>
            <div className={styles.puzzleBoard}>
              <div className={styles.boardTitle}>
                Monte a emoção: {currentEmotion.name}
              </div>
              <div className={styles.puzzleGrid}>
                {placedPieces.map((piece, index) => (
                  <div
                    key={index}
                    className={`${styles.puzzlePiece} ${piece ? styles.filled : styles.empty}`}
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={() => handleDrop(index)}
                    style={{ backgroundColor: currentEmotion.color + '20' }}
                  >
                    {piece && (
                      <div className={`${styles.puzzlePiece} ${styles.correct}`}>
                        {piece.content}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            {/* Área das peças disponíveis */}
            <div className={styles.piecesArea}>
              <div className={styles.piecesTitle}>Peças Disponíveis</div>
              <div className={styles.piecesGrid}>
                {puzzlePieces.map((piece) => (
                  <div
                    key={piece.id}
                    className={`${styles.availablePiece} ${piece.used ? styles.used : ''}`}
                    draggable
                    onDragStart={() => handleDragStart(piece)}
                    onClick={() => {
                      // Para dispositivos touch, implementar seleção por clique
                      const emptySlotIndex = placedPieces.findIndex(p => p === null)
                      if (emptySlotIndex !== -1) {
                        handleDrop(emptySlotIndex)
                      }
                    }}
                  >
                    {piece.content}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Imagem de referência */}
        {currentEmotion && (
          <div className={styles.referenceImage}>
            <div className={styles.referenceTitle}>Emoção de Referência</div>
            <div className={styles.referencePreview} style={{ backgroundColor: currentEmotion.color + '30' }}>
              {currentEmotion.emoji}
            </div>
          </div>
        )}

        {/* Estatísticas do jogo */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.gameStats.completed}</div>
            <div className={styles.statLabel}>Completados</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.gameStats.score}</div>
            <div className={styles.statLabel}>Pontuação</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.gameStats.accuracy}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
        </div>

        {/* Controles do jogo */}
        <div className={styles.gameControls}>
          {isComplete && (
            <button className={`${styles.controlButton} ${styles.nextButton}`} onClick={nextPuzzle}>
              ➡️ Próximo Quebra-Cabeça
            </button>
          )}
          <button className={`${styles.controlButton} ${styles.resetButton}`} onClick={handleGameEnd}>
            🏠 Sair do Jogo
          </button>
        </div>
      </div>
    </div>
  )
}

export default QuebraCabecaGame
