/**
 * 🎯 COLOR MATCH ERROR PATTERN COLLECTOR
 * Algoritmo especializado para coleta e análise de padrões de erro no jogo de combinação de cores
 */

export class ErrorPatternCollector {
  constructor() {
    this.name = 'ColorMatchErrorPatternCollector';
    this.description = 'Coleta padrões de erros no ColorMatch';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    
    this.errorData = {
      colorConfusions: {},
      visualErrors: [],
      spatialErrors: [],
      attentionErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      colorPerceptionIssues: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3, // Erros repetidos considerados persistentes
      cluster: 5,    // Número mínimo para formar cluster
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    
    console.log(`🎨 ${this.name} v${this.version} inicializado`);
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("ColorMatchErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }

    console.log(`📊 ColorMatchErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || 'sem ID'}`);
    
    // Coletar erros de diferentes tipos
    try {
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      
      // Registrar a coleta para histórico
      this.collectedData.push({
        timestamp: new Date().toISOString(),
        sessionId: gameData.sessionId || `session_${Date.now()}`,
        metrics: errorMetrics
      });
      
      return {
        errors: this.errorData.visualErrors.concat(
          this.errorData.spatialErrors,
          this.errorData.attentionErrors
        ),
        patterns: this.errorData.errorClusters,
        metrics: errorMetrics
      };
    }
    catch (error) {
      console.error(`❌ ColorMatchErrorPatternCollector: Erro na análise`, error);
      return { errors: [], patterns: [], metrics: {} };
    }
  }
  
  /**
   * Método padronizado de análise para integração com testes e processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  analyze(gameData) {
    return this.collect(gameData);
  }

  /**
   * Extrai e categoriza erros dos dados do jogo
   */
  analyzeErrorPatterns(gameData) {
    const patterns = {
      colorConfusions: this.detectColorConfusions(gameData),
      visualErrors: this.detectVisualErrors(gameData),
      spatialErrors: this.detectSpatialErrors(gameData),
      attentionErrors: this.detectAttentionErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };

    return patterns;
  }

  detectColorConfusions(gameData) {
    // Implementação específica para ColorMatch
    return [];
  }

  detectVisualErrors(gameData) {
    // Implementação específica para ColorMatch
    return [];
  }

  detectSpatialErrors(gameData) {
    // Implementação específica para ColorMatch
    return [];
  }

  detectAttentionErrors(gameData) {
    // Implementação específica para ColorMatch
    return [];
  }

  calculateOverallSeverity(gameData) {
    return 'low';
  }

  categorizeErrors(errorMetrics) {
    // Categorizar erros por tipo
  }

  /**
   * Coleta erros de confusão entre cores
   */
  collectColorConfusion(targetColor, selectedColor, context) {
    const confusionKey = `${targetColor}->${selectedColor}`;
    
    const confusionData = {
      timestamp: new Date().toISOString(),
      targetColor,
      selectedColor,
      confusionType: this.identifyColorConfusionType(targetColor, selectedColor),
      context: {
        difficulty: context.difficulty || 'unknown',
        position: context.position || null,
        distractors: context.distractors || [],
        responseTime: context.responseTime || 0,
        attempts: context.attempts || 1,
        lightingCondition: context.lightingCondition || 'normal'
      },
      severity: this.calculateColorErrorSeverity(targetColor, selectedColor, context),
      frequency: this.updateConfusionFrequency(confusionKey),
      colorDistance: this.calculateColorDistance(targetColor, selectedColor)
    };

    if (!this.errorData.colorConfusions[confusionKey]) {
      this.errorData.colorConfusions[confusionKey] = [];
    }
    this.errorData.colorConfusions[confusionKey].push(confusionData);

    // Detectar padrões persistentes
    this.detectPersistentColorError(confusionKey, confusionData);

    // Detectar problemas de percepção de cor
    this.detectColorPerceptionIssues(confusionData);

    return confusionData;
  }

  /**
   * Identifica o tipo de confusão de cor
   */
  identifyColorConfusionType(targetColor, selectedColor) {
    const colorGroups = {
      warm: ['red', 'orange', 'yellow', 'pink'],
      cool: ['blue', 'green', 'purple', 'cyan'],
      neutral: ['black', 'white', 'gray', 'brown']
    };

    const targetGroup = this.getColorGroup(targetColor, colorGroups);
    const selectedGroup = this.getColorGroup(selectedColor, colorGroups);

    if (targetGroup === selectedGroup) {
      return 'intra-group'; // Confusão dentro do mesmo grupo
    } else if (this.areSimilarColors(targetColor, selectedColor)) {
      return 'similarity-based'; // Baseado em similaridade visual
    } else if (this.areComplementaryColors(targetColor, selectedColor)) {
      return 'complementary'; // Cores complementares
    } else {
      return 'random'; // Erro aparentemente aleatório
    }
  }

  /**
   * Coleta erros relacionados a cores
   */
  collectColorError(targetColor, selectedColor, context) {
    const errorKey = `${targetColor}->${selectedColor}`;
    
    const colorError = {
      timestamp: new Date().toISOString(),
      targetColor: targetColor,
      selectedColor: selectedColor,
      errorType: this.identifyColorErrorType(targetColor, selectedColor),
      context: {
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculateColorErrorSeverity(targetColor, selectedColor, context),
      colorDistance: this.calculateColorDistance(targetColor, selectedColor)
    };

    if (!this.errorData.colorConfusions[errorKey]) {
      this.errorData.colorConfusions[errorKey] = [];
    }
    this.errorData.colorConfusions[errorKey].push(colorError);

    return colorError;
  }

  /**
   * Identifica o tipo de erro relacionado a cores
   */
  identifyColorErrorType(targetColor, selectedColor) {
    if (!selectedColor) return 'no_selection';
    
    // Verificar se são cores similares (em termos de tom)
    if (this.areSimilarColors(targetColor, selectedColor)) {
      return 'similar_color';
    }
    
    // Verificar se são cores complementares
    if (this.areComplementaryColors(targetColor, selectedColor)) {
      return 'complementary_color';
    }
    
    // Verificar se é erro de brilho (mais claro/escuro)
    if (this.isBrightnessError(targetColor, selectedColor)) {
      return 'brightness_error';
    }
    
    // Verificar se é erro de saturação
    if (this.isSaturationError(targetColor, selectedColor)) {
      return 'saturation_error';
    }
    
    return 'general_color_error';
  }

  /**
   * Verifica se duas cores são similares
   */
  areSimilarColors(color1, color2) {
    // Implementação simplificada - na prática, converteria para HSL e compararia valores
    return false;
  }

  /**
   * Verifica se duas cores são complementares
   */
  areComplementaryColors(color1, color2) {
    // Implementação simplificada - na prática, verificaria cores opostas no círculo cromático
    return false;
  }

  /**
   * Verifica se existe diferença principalmente de brilho entre as cores
   */
  isBrightnessError(color1, color2) {
    // Implementação simplificada
    return false;
  }

  /**
   * Verifica se existe diferença principalmente de saturação entre as cores
   */
  isSaturationError(color1, color2) {
    // Implementação simplificada
    return false;
  }

  /**
   * Calcula a distância entre duas cores (métrica para diferença)
   */
  calculateColorDistance(color1, color2) {
    // Implementação simplificada - na prática, usaria fórmula de distância Euclidiana em espaço de cor
    return 0.5;
  }

  /**
   * Calcula a severidade do erro de cor
   */
  calculateColorErrorSeverity(targetColor, selectedColor, context) {
    let severity = 0.5; // Base
    
    // Ajuste por distância entre cores
    const distance = this.calculateColorDistance(targetColor, selectedColor);
    severity += distance * 0.3;
    
    // Ajuste por tempo de resposta
    if (context.responseTime > 5000) severity += 0.1; // Muito lento
    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade
    
    // Ajuste por dificuldade
    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis
    
    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1
  }

  /**
   * Coleta erros espaciais (posição incorreta)
   */
  collectSpatialError(targetPosition, selectedPosition, context) {
    const spatialError = {
      timestamp: new Date().toISOString(),
      targetPosition,
      selectedPosition,
      distance: this.calculateSpatialDistance(targetPosition, selectedPosition),
      direction: this.calculateDirection(targetPosition, selectedPosition),
      context: {
        gridSize: context.gridSize || '3x3',
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0
      },
      severity: this.calculateSpatialErrorSeverity(targetPosition, selectedPosition, context)
    };

    this.errorData.spatialErrors.push(spatialError);
    this.detectSpatialPattern(spatialError);

    return spatialError;
  }

  /**
   * Coleta erros de atenção (cliques em áreas não-alvo)
   */
  collectAttentionError(clickPosition, validTargets, context) {
    const attentionError = {
      timestamp: new Date().toISOString(),
      clickPosition,
      validTargets,
      nearestTarget: this.findNearestTarget(clickPosition, validTargets),
      context: {
        distractorCount: context.distractorCount || 0,
        timeRemaining: context.timeRemaining || 0,
        currentLevel: context.currentLevel || 1
      },
      errorType: this.classifyAttentionError(clickPosition, validTargets),
      severity: this.calculateAttentionErrorSeverity(clickPosition, validTargets, context)
    };

    this.errorData.attentionErrors.push(attentionError);
    this.detectAttentionPattern(attentionError);

    return attentionError;
  }

  /**
   * Detecta padrões persistentes de erro de cor
   */
  detectPersistentColorError(confusionKey, confusionData) {
    if (!this.errorData.persistentErrors[confusionKey]) {
      this.errorData.persistentErrors[confusionKey] = [];
    }

    this.errorData.persistentErrors[confusionKey].push(confusionData);

    // Se o erro ocorreu múltiplas vezes, marcar como persistente
    if (this.errorData.persistentErrors[confusionKey].length >= this.errorThresholds.persistent) {
      confusionData.isPersistent = true;
      this.flagForIntervention(confusionKey, 'color_confusion');
    }
  }

  /**
   * Detecta problemas de percepção de cor
   */
  detectColorPerceptionIssues(confusionData) {
    const { targetColor, selectedColor } = confusionData;
    
    // Detectar possível daltonismo
    if (this.isPossibleColorBlindnessError(targetColor, selectedColor)) {
      const issueKey = 'color_blindness_indicator';
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }

    // Detectar dificuldades com saturação
    if (this.isSaturationIssue(targetColor, selectedColor)) {
      const issueKey = 'saturation_difficulty';
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }
  }

  /**
   * Analisa padrões de erro coletados
   */
  analyzeErrorPatterns() {
    const analysis = {
      timestamp: new Date().toISOString(),
      sessionDuration: Date.now() - this.sessionStartTime,
      totalErrors: this.getTotalErrorCount(),
      errorCategories: this.categorizeErrors(),
      persistentPatterns: this.identifyPersistentPatterns(),
      learningProgress: this.assessLearningProgress(),
      interventionRecommendations: this.generateInterventionRecommendations(),
      severity: this.calculateOverallSeverity()
    };

    return analysis;
  }

  /**
   * Gera métricas de erro estruturadas
   */
  generateErrorMetrics() {
    const metrics = {
      colorConfusion: {
        total: Object.keys(this.errorData.colorConfusions).length,
        mostCommon: this.getMostCommonColorError(),
        severity: this.getAverageColorErrorSeverity()
      },
      spatialErrors: {
        total: this.errorData.spatialErrors.length,
        averageDistance: this.getAverageSpatialDistance(),
        commonDirections: this.getCommonSpatialDirections()
      },
      attentionErrors: {
        total: this.errorData.attentionErrors.length,
        severity: this.getAverageAttentionErrorSeverity(),
        patterns: this.getAttentionErrorPatterns()
      },
      perceptionIssues: {
        colorBlindnessIndicators: this.errorData.colorPerceptionIssues.color_blindness_indicator?.length || 0,
        saturationDifficulties: this.errorData.colorPerceptionIssues.saturation_difficulty?.length || 0
      },
      learningIndicators: this.generateLearningIndicators()
    };

    return metrics;
  }

  // Métodos auxiliares
  getColorGroup(color, colorGroups) {
    for (const [group, colors] of Object.entries(colorGroups)) {
      if (colors.includes(color.toLowerCase())) {
        return group;
      }
    }
    return 'unknown';
  }

  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    
    Object.entries(this.errorData.colorConfusions).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    
    return {
      error: mostCommonError,
      count: maxCount
    };
  }

  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    
    Object.values(this.errorData.colorConfusions).forEach(errors => {
      errors.forEach(error => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }

  calculateColorDistance(color1, color2) {
    // Simulação de distância de cor (em implementação real, usar espaço de cor LAB)
    const colorMap = {
      red: [255, 0, 0],
      green: [0, 255, 0],
      blue: [0, 0, 255],
      yellow: [255, 255, 0],
      orange: [255, 165, 0],
      purple: [128, 0, 128],
      pink: [255, 192, 203],
      cyan: [0, 255, 255],
      black: [0, 0, 0],
      white: [255, 255, 255],
      gray: [128, 128, 128],
      brown: [165, 42, 42]
    };

    const rgb1 = colorMap[color1.toLowerCase()] || [128, 128, 128];
    const rgb2 = colorMap[color2.toLowerCase()] || [128, 128, 128];

    const distance = Math.sqrt(
      Math.pow(rgb1[0] - rgb2[0], 2) +
      Math.pow(rgb1[1] - rgb2[1], 2) +
      Math.pow(rgb1[2] - rgb2[2], 2)
    );

    return distance / (Math.sqrt(3) * 255); // Normalizar para 0-1
  }

  /**
   * Calcula a severidade do erro de cor
   */
  calculateColorErrorSeverity(targetColor, selectedColor, context) {
    let severity = 0.5; // Base
    
    // Ajuste por distância entre cores
    const distance = this.calculateColorDistance(targetColor, selectedColor);
    severity += distance * 0.3;
    
    // Ajuste por tempo de resposta
    if (context.responseTime > 5000) severity += 0.1; // Muito lento
    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade
    
    // Ajuste por dificuldade
    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis
    
    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1
  }

  /**
   * Coleta erros espaciais (posição incorreta)
   */
  collectSpatialError(targetPosition, selectedPosition, context) {
    const spatialError = {
      timestamp: new Date().toISOString(),
      targetPosition,
      selectedPosition,
      distance: this.calculateSpatialDistance(targetPosition, selectedPosition),
      direction: this.calculateDirection(targetPosition, selectedPosition),
      context: {
        gridSize: context.gridSize || '3x3',
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0
      },
      severity: this.calculateSpatialErrorSeverity(targetPosition, selectedPosition, context)
    };

    this.errorData.spatialErrors.push(spatialError);
    this.detectSpatialPattern(spatialError);

    return spatialError;
  }

  /**
   * Coleta erros de atenção (cliques em áreas não-alvo)
   */
  collectAttentionError(clickPosition, validTargets, context) {
    const attentionError = {
      timestamp: new Date().toISOString(),
      clickPosition,
      validTargets,
      nearestTarget: this.findNearestTarget(clickPosition, validTargets),
      context: {
        distractorCount: context.distractorCount || 0,
        timeRemaining: context.timeRemaining || 0,
        currentLevel: context.currentLevel || 1
      },
      errorType: this.classifyAttentionError(clickPosition, validTargets),
      severity: this.calculateAttentionErrorSeverity(clickPosition, validTargets, context)
    };

    this.errorData.attentionErrors.push(attentionError);
    this.detectAttentionPattern(attentionError);

    return attentionError;
  }

  /**
   * Detecta padrões persistentes de erro de cor
   */
  detectPersistentColorError(confusionKey, confusionData) {
    if (!this.errorData.persistentErrors[confusionKey]) {
      this.errorData.persistentErrors[confusionKey] = [];
    }

    this.errorData.persistentErrors[confusionKey].push(confusionData);

    // Se o erro ocorreu múltiplas vezes, marcar como persistente
    if (this.errorData.persistentErrors[confusionKey].length >= this.errorThresholds.persistent) {
      confusionData.isPersistent = true;
      this.flagForIntervention(confusionKey, 'color_confusion');
    }
  }

  /**
   * Detecta problemas de percepção de cor
   */
  detectColorPerceptionIssues(confusionData) {
    const { targetColor, selectedColor } = confusionData;
    
    // Detectar possível daltonismo
    if (this.isPossibleColorBlindnessError(targetColor, selectedColor)) {
      const issueKey = 'color_blindness_indicator';
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }

    // Detectar dificuldades com saturação
    if (this.isSaturationIssue(targetColor, selectedColor)) {
      const issueKey = 'saturation_difficulty';
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }
  }

  /**
   * Analisa padrões de erro coletados
   */
  analyzeErrorPatterns() {
    const analysis = {
      timestamp: new Date().toISOString(),
      sessionDuration: Date.now() - this.sessionStartTime,
      totalErrors: this.getTotalErrorCount(),
      errorCategories: this.categorizeErrors(),
      persistentPatterns: this.identifyPersistentPatterns(),
      learningProgress: this.assessLearningProgress(),
      interventionRecommendations: this.generateInterventionRecommendations(),
      severity: this.calculateOverallSeverity()
    };

    return analysis;
  }

  /**
   * Gera métricas de erro estruturadas
   */
  generateErrorMetrics() {
    const metrics = {
      colorConfusion: {
        total: Object.keys(this.errorData.colorConfusions).length,
        mostCommon: this.getMostCommonColorError(),
        severity: this.getAverageColorErrorSeverity()
      },
      spatialErrors: {
        total: this.errorData.spatialErrors.length,
        averageDistance: this.getAverageSpatialDistance(),
        commonDirections: this.getCommonSpatialDirections()
      },
      attentionErrors: {
        total: this.errorData.attentionErrors.length,
        severity: this.getAverageAttentionErrorSeverity(),
        patterns: this.getAttentionErrorPatterns()
      },
      perceptionIssues: {
        colorBlindnessIndicators: this.errorData.colorPerceptionIssues.color_blindness_indicator?.length || 0,
        saturationDifficulties: this.errorData.colorPerceptionIssues.saturation_difficulty?.length || 0
      },
      learningIndicators: this.generateLearningIndicators()
    };

    return metrics;
  }

  // Métodos auxiliares
  getColorGroup(color, colorGroups) {
    for (const [group, colors] of Object.entries(colorGroups)) {
      if (colors.includes(color.toLowerCase())) {
        return group;
      }
    }
    return 'unknown';
  }

  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    
    Object.entries(this.errorData.colorConfusions).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    
    return {
      error: mostCommonError,
      count: maxCount
    };
  }

  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    
    Object.values(this.errorData.colorConfusions).forEach(errors => {
      errors.forEach(error => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }

  calculateColorDistance(color1, color2) {
    // Simulação de distância de cor (em implementação real, usar espaço de cor LAB)
    const colorMap = {
      red: [255, 0, 0],
      green: [0, 255, 0],
      blue: [0, 0, 255],
      yellow: [255, 255, 0],
      orange: [255, 165, 0],
      purple: [128, 0, 128],
      pink: [255, 192, 203],
      cyan: [0, 255, 255],
      black: [0, 0, 0],
      white: [255, 255, 255],
      gray: [128, 128, 128],
      brown: [165, 42, 42]
    };

    const rgb1 = colorMap[color1.toLowerCase()] || [128, 128, 128];
    const rgb2 = colorMap[color2.toLowerCase()] || [128, 128, 128];

    const distance = Math.sqrt(
      Math.pow(rgb1[0] - rgb2[0], 2) +
      Math.pow(rgb1[1] - rgb2[1], 2) +
      Math.pow(rgb1[2] - rgb2[2], 2)
    );

    return distance / (Math.sqrt(3) * 255); // Normalizar para 0-1
  }

  /**
   * Calcula a severidade do erro de cor
   */
  calculateColorErrorSeverity(targetColor, selectedColor, context) {
    let severity = 0.5; // Base
    
    // Ajuste por distância entre cores
    const distance = this.calculateColorDistance(targetColor, selectedColor);
    severity += distance * 0.3;
    
    // Ajuste por tempo de resposta
    if (context.responseTime > 5000) severity += 0.1; // Muito lento
    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade
    
    // Ajuste por dificuldade
    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis
    
    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1
  }

  /**
   * Coleta erros espaciais (posição incorreta)
   */
  collectSpatialError(targetPosition, selectedPosition, context) {
    const spatialError = {
      timestamp: new Date().toISOString(),
      targetPosition,
      selectedPosition,
      distance: this.calculateSpatialDistance(targetPosition, selectedPosition),
      direction: this.calculateDirection(targetPosition, selectedPosition),
      context: {
        gridSize: context.gridSize || '3x3',
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0
      },
      severity: this.calculateSpatialErrorSeverity(targetPosition, selectedPosition, context)
    };

    this.errorData.spatialErrors.push(spatialError);
    this.detectSpatialPattern(spatialError);

    return spatialError;
  }

  /**
   * Coleta erros de atenção (cliques em áreas não-alvo)
   */
  collectAttentionError(clickPosition, validTargets, context) {
    const attentionError = {
      timestamp: new Date().toISOString(),
      clickPosition,
      validTargets,
      nearestTarget: this.findNearestTarget(clickPosition, validTargets),
      context: {
        distractorCount: context.distractorCount || 0,
        timeRemaining: context.timeRemaining || 0,
        currentLevel: context.currentLevel || 1
      },
      errorType: this.classifyAttentionError(clickPosition, validTargets),
      severity: this.calculateAttentionErrorSeverity(clickPosition, validTargets, context)
    };

    this.errorData.attentionErrors.push(attentionError);
    this.detectAttentionPattern(attentionError);

    return attentionError;
  }

  /**
   * Detecta padrões persistentes de erro de cor
   */
  detectPersistentColorError(confusionKey, confusionData) {
    if (!this.errorData.persistentErrors[confusionKey]) {
      this.errorData.persistentErrors[confusionKey] = [];
    }

    this.errorData.persistentErrors[confusionKey].push(confusionData);

    // Se o erro ocorreu múltiplas vezes, marcar como persistente
    if (this.errorData.persistentErrors[confusionKey].length >= this.errorThresholds.persistent) {
      confusionData.isPersistent = true;
      this.flagForIntervention(confusionKey, 'color_confusion');
    }
  }

  /**
   * Detecta problemas de percepção de cor
   */
  detectColorPerceptionIssues(confusionData) {
    const { targetColor, selectedColor } = confusionData;
    
    // Detectar possível daltonismo
    if (this.isPossibleColorBlindnessError(targetColor, selectedColor)) {
      const issueKey = 'color_blindness_indicator';
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }

    // Detectar dificuldades com saturação
    if (this.isSaturationIssue(targetColor, selectedColor)) {
      const issueKey = 'saturation_difficulty';
      if (!this.errorData.colorPerceptionIssues[issueKey]) {
        this.errorData.colorPerceptionIssues[issueKey] = [];
      }
      this.errorData.colorPerceptionIssues[issueKey].push(confusionData);
    }
  }

  /**
   * Analisa padrões de erro coletados
   */
  analyzeErrorPatterns() {
    const analysis = {
      timestamp: new Date().toISOString(),
      sessionDuration: Date.now() - this.sessionStartTime,
      totalErrors: this.getTotalErrorCount(),
      errorCategories: this.categorizeErrors(),
      persistentPatterns: this.identifyPersistentPatterns(),
      learningProgress: this.assessLearningProgress(),
      interventionRecommendations: this.generateInterventionRecommendations(),
      severity: this.calculateOverallSeverity()
    };

    return analysis;
  }

  /**
   * Gera métricas de erro estruturadas
   */
  generateErrorMetrics() {
    const metrics = {
      colorConfusion: {
        total: Object.keys(this.errorData.colorConfusions).length,
        mostCommon: this.getMostCommonColorError(),
        severity: this.getAverageColorErrorSeverity()
      },
      spatialErrors: {
        total: this.errorData.spatialErrors.length,
        averageDistance: this.getAverageSpatialDistance(),
        commonDirections: this.getCommonSpatialDirections()
      },
      attentionErrors: {
        total: this.errorData.attentionErrors.length,
        severity: this.getAverageAttentionErrorSeverity(),
        patterns: this.getAttentionErrorPatterns()
      },
      perceptionIssues: {
        colorBlindnessIndicators: this.errorData.colorPerceptionIssues.color_blindness_indicator?.length || 0,
        saturationDifficulties: this.errorData.colorPerceptionIssues.saturation_difficulty?.length || 0
      },
      learningIndicators: this.generateLearningIndicators()
    };

    return metrics;
  }

  // Métodos auxiliares
  getColorGroup(color, colorGroups) {
    for (const [group, colors] of Object.entries(colorGroups)) {
      if (colors.includes(color.toLowerCase())) {
        return group;
      }
    }
    return 'unknown';
  }

  calculateColorDistance(color1, color2) {
    // Simulação de distância de cor (em implementação real, usar espaço de cor LAB)
    const colorMap = {
      red: [255, 0, 0],
      green: [0, 255, 0],
      blue: [0, 0, 255],
      yellow: [255, 255, 0],
      orange: [255, 165, 0],
      purple: [128, 0, 128],
      pink: [255, 192, 203],
      cyan: [0, 255, 255],
      black: [0, 0, 0],
      white: [255, 255, 255],
      gray: [128, 128, 128],
      brown: [165, 42, 42]
    };

    const rgb1 = colorMap[color1.toLowerCase()] || [128, 128, 128];
    const rgb2 = colorMap[color2.toLowerCase()] || [128, 128, 128];

    const distance = Math.sqrt(
      Math.pow(rgb1[0] - rgb2[0], 2) +
      Math.pow(rgb1[1] - rgb2[1], 2) +
      Math.pow(rgb1[2] - rgb2[2], 2)
    );

    return distance / (Math.sqrt(3) * 255); // Normalizar para 0-1
  }

  areSimilarColors(color1, color2) {
    return this.calculateColorDistance(color1, color2) < 0.3;
  }

  areComplementaryColors(color1, color2) {
    const complementaryPairs = [
      ['red', 'green'],
      ['blue', 'orange'],
      ['yellow', 'purple'],
      ['cyan', 'red']
    ];

    return complementaryPairs.some(pair =>
      (pair.includes(color1.toLowerCase()) && pair.includes(color2.toLowerCase()))
    );
  }

  updateConfusionFrequency(confusionKey) {
    const current = this.getErrorFrequency(confusionKey);
    return current + 1;
  }

  getErrorFrequency(confusionKey) {
    return this.errorData.colorConfusions[confusionKey]?.length || 0;
  }

  calculateSpatialDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 0;
    return Math.sqrt(Math.pow(pos1.x - pos2.x, 2) + Math.pow(pos1.y - pos2.y, 2));
  }

  calculateDirection(from, to) {
    if (!from || !to) return 'unknown';
    const dx = to.x - from.x;
    const dy = to.y - from.y;
    
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right' : 'left';
    } else {
      return dy > 0 ? 'down' : 'up';
    }
  }

  calculateSpatialErrorSeverity(targetPos, selectedPos, context) {
    const distance = this.calculateSpatialDistance(targetPos, selectedPos);
    const maxDistance = Math.sqrt(Math.pow(context.gridSize || 3, 2) * 2);
    return Math.min(distance / maxDistance, 1.0);
  }

  findNearestTarget(clickPos, targets) {
    if (!targets || targets.length === 0) return null;
    
    return targets.reduce((nearest, target) => {
      const distance = this.calculateSpatialDistance(clickPos, target.position);
      if (!nearest || distance < nearest.distance) {
        return { target, distance };
      }
      return nearest;
    }, null);
  }

  classifyAttentionError(clickPos, validTargets) {
    const nearest = this.findNearestTarget(clickPos, validTargets);
    if (!nearest) return 'random';
    
    if (nearest.distance < 50) return 'near-miss';
    if (nearest.distance < 100) return 'adjacent';
    return 'distant';
  }

  calculateAttentionErrorSeverity(clickPos, validTargets, context) {
    const nearest = this.findNearestTarget(clickPos, validTargets);
    if (!nearest) return 1.0;
    
    let severity = nearest.distance / 200; // Normalizar distância
    
    // Ajustar por contexto
    if (context.distractorCount > 5) severity *= 0.8; // Mais distrações = menor severidade
    if (context.timeRemaining < 5000) severity *= 1.2; // Pouco tempo = maior severidade
    
    return Math.min(severity, 1.0);
  }

  detectSpatialPattern(spatialError) {
    // Implementar detecção de padrões espaciais
    const recentSpatialErrors = this.errorData.spatialErrors.slice(-5);
    const commonDirection = this.getMostCommonDirection(recentSpatialErrors);
    
    if (commonDirection.frequency >= 3) {
      spatialError.patternDetected = `consistent_${commonDirection.direction}_bias`;
    }
  }

  detectAttentionPattern(attentionError) {
    // Implementar detecção de padrões de atenção
    const recentAttentionErrors = this.errorData.attentionErrors.slice(-5);
    const errorTypes = recentAttentionErrors.map(e => e.errorType);
    
    if (errorTypes.filter(t => t === 'random').length >= 3) {
      attentionError.patternDetected = 'attention_deficit_indicator';
    }
  }

  isPossibleColorBlindnessError(target, selected) {
    const colorBlindPatterns = [
      ['red', 'green'],
      ['green', 'red'],
      ['blue', 'purple'],
      ['purple', 'blue']
    ];
    
    return colorBlindPatterns.some(pattern =>
      pattern[0] === target.toLowerCase() && pattern[1] === selected.toLowerCase()
    );
  }

  isSaturationIssue(target, selected) {
    // Detectar problemas com saturação de cor
    const lowSaturationColors = ['gray', 'lightgray', 'darkgray'];
    const highSaturationColors = ['red', 'blue', 'green', 'yellow'];
    
    return (lowSaturationColors.includes(target.toLowerCase()) && 
            highSaturationColors.includes(selected.toLowerCase())) ||
           (highSaturationColors.includes(target.toLowerCase()) && 
            lowSaturationColors.includes(selected.toLowerCase()));
  }

  flagForIntervention(errorKey, errorType) {
    // Implementar sistema de flags para intervenção
    console.warn(`Padrão persistente detectado: ${errorKey} (${errorType})`);
  }

  getTotalErrorCount() {
    const colorErrors = Object.values(this.errorData.colorConfusions)
      .reduce((sum, errors) => sum + errors.length, 0);
    
    return colorErrors + 
           this.errorData.spatialErrors.length + 
           this.errorData.attentionErrors.length;
  }

  categorizeErrors() {
    return {
      colorConfusion: Object.keys(this.errorData.colorConfusions).length,
      spatial: this.errorData.spatialErrors.length,
      attention: this.errorData.attentionErrors.length,
      perception: Object.keys(this.errorData.colorPerceptionIssues).length
    };
  }

  identifyPersistentPatterns() {
    return Object.entries(this.errorData.persistentErrors)
      .filter(([key, errors]) => errors.length >= this.errorThresholds.persistent)
      .map(([key, errors]) => ({
        pattern: key,
        frequency: errors.length,
        severity: errors.reduce((sum, e) => sum + e.severity, 0) / errors.length
      }));
  }

  assessLearningProgress() {
    // Implementar avaliação de progresso de aprendizagem
    const recentErrors = this.getTimeWindowErrors(300000); // Últimos 5 minutos
    const olderErrors = this.getTimeWindowErrors(600000, 300000); // 5-10 minutos atrás
    
    return {
      errorReduction: olderErrors.length > 0 ? 
        (olderErrors.length - recentErrors.length) / olderErrors.length : 0,
      improvementTrend: this.calculateImprovementTrend(),
      learningRate: this.calculateLearningRate()
    };
  }

  generateInterventionRecommendations() {
    const recommendations = [];
    
    // Baseado em padrões persistentes
    const persistentPatterns = this.identifyPersistentPatterns();
    persistentPatterns.forEach(pattern => {
      if (pattern.pattern.includes('color_confusion')) {
        recommendations.push({
          type: 'color_training',
          priority: 'high',
          description: `Treino específico para confusão: ${pattern.pattern}`
        });
      }
    });
    
    // Baseado em problemas de percepção
    if (this.errorData.colorPerceptionIssues.color_blindness_indicator?.length > 2) {
      recommendations.push({
        type: 'accessibility_adjustment',
        priority: 'high',
        description: 'Considerar ajustes para daltonismo'
      });
    }
    
    return recommendations;
  }

  calculateOverallSeverity() {
    const allErrors = this.getAllErrors();
    if (allErrors.length === 0) return 0;
    
    const totalSeverity = allErrors.reduce((sum, error) => sum + (error.severity || 0), 0);
    return totalSeverity / allErrors.length;
  }

  getAllErrors() {
    const colorErrors = Object.values(this.errorData.colorConfusions).flat();
    return [...colorErrors, ...this.errorData.spatialErrors, ...this.errorData.attentionErrors];
  }

  getTimeWindowErrors(windowMs, offsetMs = 0) {
    const now = Date.now();
    const startTime = now - windowMs - offsetMs;
    const endTime = now - offsetMs;
    
    return this.getAllErrors().filter(error => {
      const errorTime = new Date(error.timestamp).getTime();
      return errorTime >= startTime && errorTime <= endTime;
    });
  }

  calculateImprovementTrend() {
    // Implementar cálculo de tendência de melhoria
    const errorsByTime = this.getErrorsByTimeSlices(5); // 5 fatias de tempo
    if (errorsByTime.length < 2) return 0;
    
    let improvements = 0;
    for (let i = 1; i < errorsByTime.length; i++) {
      if (errorsByTime[i] < errorsByTime[i-1]) improvements++;
    }
    
    return improvements / (errorsByTime.length - 1);
  }

  calculateLearningRate() {
    // Implementar cálculo de taxa de aprendizagem
    const totalTime = Date.now() - this.sessionStartTime;
    const totalErrors = this.getTotalErrorCount();
    
    if (totalTime === 0) return 0;
    return Math.max(0, 1 - (totalErrors / (totalTime / 60000))); // Erros por minuto invertido
  }

  getErrorsByTimeSlices(slices) {
    const sessionDuration = Date.now() - this.sessionStartTime;
    const sliceDuration = sessionDuration / slices;
    const sliceErrors = [];
    
    for (let i = 0; i < slices; i++) {
      const sliceStart = this.sessionStartTime + (i * sliceDuration);
      const sliceEnd = sliceStart + sliceDuration;
      
      const errorsInSlice = this.getAllErrors().filter(error => {
        const errorTime = new Date(error.timestamp).getTime();
        return errorTime >= sliceStart && errorTime < sliceEnd;
      });
      
      sliceErrors.push(errorsInSlice.length);
    }
    
    return sliceErrors;
  }

  getMostCommonColorError() {
    const errorCounts = {};
    Object.entries(this.errorData.colorConfusions).forEach(([key, errors]) => {
      errorCounts[key] = errors.length;
    });
    
    return Object.entries(errorCounts).reduce((max, [key, count]) => 
      count > max.count ? { pattern: key, count } : max, 
      { pattern: null, count: 0 }
    );
  }

  getAverageColorErrorSeverity() {
    const allColorErrors = Object.values(this.errorData.colorConfusions).flat();
    if (allColorErrors.length === 0) return 0;
    
    const totalSeverity = allColorErrors.reduce((sum, error) => sum + error.severity, 0);
    return totalSeverity / allColorErrors.length;
  }

  getAverageSpatialDistance() {
    if (this.errorData.spatialErrors.length === 0) return 0;
    
    const totalDistance = this.errorData.spatialErrors.reduce((sum, error) => sum + error.distance, 0);
    return totalDistance / this.errorData.spatialErrors.length;
  }

  getCommonSpatialDirections() {
    const directionCounts = {};
    this.errorData.spatialErrors.forEach(error => {
      directionCounts[error.direction] = (directionCounts[error.direction] || 0) + 1;
    });
    
    return Object.entries(directionCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([direction, count]) => ({ direction, count }));
  }

  getMostCommonDirection(errors) {
    const directionCounts = {};
    errors.forEach(error => {
      directionCounts[error.direction] = (directionCounts[error.direction] || 0) + 1;
    });
    
    const mostCommon = Object.entries(directionCounts)
      .reduce((max, [dir, count]) => count > max.frequency ? 
        { direction: dir, frequency: count } : max, 
        { direction: null, frequency: 0 }
      );
    
    return mostCommon;
  }

  getAverageAttentionErrorSeverity() {
    if (this.errorData.attentionErrors.length === 0) return 0;
    
    const totalSeverity = this.errorData.attentionErrors.reduce((sum, error) => sum + error.severity, 0);
    return totalSeverity / this.errorData.attentionErrors.length;
  }

  getAttentionErrorPatterns() {
    const patterns = {};
    this.errorData.attentionErrors.forEach(error => {
      if (error.patternDetected) {
        patterns[error.patternDetected] = (patterns[error.patternDetected] || 0) + 1;
      }
    });
    
    return patterns;
  }

  generateLearningIndicators() {
    return {
      adaptationRate: this.calculateAdaptationRate(),
      errorRecovery: this.calculateErrorRecoveryRate(),
      patternRecognition: this.calculatePatternRecognitionImprovement(),
      consistencyScore: this.calculateConsistencyScore()
    };
  }

  calculateAdaptationRate() {
    // Taxa de adaptação baseada na redução de erros ao longo do tempo
    const errorsBySlice = this.getErrorsByTimeSlices(10);
    if (errorsBySlice.length < 3) return 0;
    
    const firstHalf = errorsBySlice.slice(0, 5).reduce((sum, count) => sum + count, 0);
    const secondHalf = errorsBySlice.slice(5).reduce((sum, count) => sum + count, 0);
    
    if (firstHalf === 0) return 1;
    return Math.max(0, (firstHalf - secondHalf) / firstHalf);
  }

  calculateErrorRecoveryRate() {
    // Taxa de recuperação após erros
    let recoveryCount = 0;
    let totalErrors = 0;
    
    Object.values(this.errorData.colorConfusions).forEach(errors => {
      for (let i = 0; i < errors.length - 1; i++) {
        totalErrors++;
        const currentError = errors[i];
        const nextError = errors[i + 1];
        
        // Se o próximo erro tem severidade menor, conta como recuperação
        if (nextError.severity < currentError.severity) {
          recoveryCount++;
        }
      }
    });
    
    return totalErrors > 0 ? recoveryCount / totalErrors : 0;
  }

  calculatePatternRecognitionImprovement() {
    // Melhoria no reconhecimento de padrões
    const persistentPatterns = this.identifyPersistentPatterns();
    if (persistentPatterns.length === 0) return 1;
    
    // Verificar se a severidade dos padrões persistentes está diminuindo
    let improvementCount = 0;
    persistentPatterns.forEach(pattern => {
      const errors = this.errorData.persistentErrors[pattern.pattern];
      if (errors.length >= 2) {
        const recentSeverity = errors.slice(-2).reduce((sum, e) => sum + e.severity, 0) / 2;
        const olderSeverity = errors.slice(0, 2).reduce((sum, e) => sum + e.severity, 0) / 2;
        
        if (recentSeverity < olderSeverity) {
          improvementCount++;
        }
      }
    });
    
    return persistentPatterns.length > 0 ? improvementCount / persistentPatterns.length : 0;
  }

  calculateConsistencyScore() {
    // Score de consistência baseado na variabilidade dos erros
    const allErrors = this.getAllErrors();
    if (allErrors.length < 2) return 1;
    
    const severities = allErrors.map(e => e.severity || 0);
    const mean = severities.reduce((sum, s) => sum + s, 0) / severities.length;
    const variance = severities.reduce((sum, s) => sum + Math.pow(s - mean, 2), 0) / severities.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Consistência alta = baixo desvio padrão (invertido e normalizado)
    return Math.max(0, 1 - (standardDeviation / mean));
  }

  /**
   * Reset dos dados de erro (para nova sessão)
   */
  reset() {
    this.errorData = {
      colorConfusions: {},
      visualErrors: [],
      spatialErrors: [],
      attentionErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      colorPerceptionIssues: {}
    };
    this.sessionStartTime = Date.now();
  }

  /**
   * Exporta dados para análise externa
   */
  exportData() {
    return {
      ...this.errorData,
      sessionDuration: Date.now() - this.sessionStartTime,
      analysis: this.analyzeErrorPatterns(),
      metrics: this.generateErrorMetrics()
    };
  }
}

export default ErrorPatternCollector;
