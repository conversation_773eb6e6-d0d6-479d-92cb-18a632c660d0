import{j as e}from"./index-CrlBCiRw.js";import{r as n}from"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const a={container:"_container_1ik09_15",loginContainer:"_loginContainer_1ik09_37",loginIcon:"_loginIcon_1ik09_59",loginTitle:"_loginTitle_1ik09_69",loginSubtitle:"_loginSubtitle_1ik09_83",loginInput:"_loginInput_1ik09_97",loginError:"_loginError_1ik09_131",loginButton:"_loginButton_1ik09_151",loginFooter:"_loginFooter_1ik09_189",adminContainer:"_adminContainer_1ik09_203",adminHeader:"_adminHeader_1ik09_221",headerContent:"_headerContent_1ik09_233",adminTitle:"_adminTitle_1ik09_245",adminSubtitle:"_adminSubtitle_1ik09_257",logoutButton:"_logoutButton_1ik09_269",infoBox:"_infoBox_1ik09_305",sectionTitle:"_sectionTitle_1ik09_327",statsGrid:"_statsGrid_1ik09_343",statCard:"_statCard_1ik09_357",statNumber:"_statNumber_1ik09_409",statLabel:"_statLabel_1ik09_421",blue:"_blue_1ik09_435",green:"_green_1ik09_437",orange:"_orange_1ik09_439",purple:"_purple_1ik09_441",pink:"_pink_1ik09_443",cyan:"_cyan_1ik09_445",gamesList:"_gamesList_1ik09_451",gameCard:"_gameCard_1ik09_463",gameIcon:"_gameIcon_1ik09_497",gameInfo:"_gameInfo_1ik09_523",gameName:"_gameName_1ik09_531",gameStats:"_gameStats_1ik09_545",gameStatsDetail:"_gameStatsDetail_1ik09_563",controlsSection:"_controlsSection_1ik09_575",controlsTitle:"_controlsTitle_1ik09_591",controlsSubtitle:"_controlsSubtitle_1ik09_605",buttonGroup:"_buttonGroup_1ik09_617",actionButton:"_actionButton_1ik09_629",danger:"_danger_1ik09_671",lastUpdateInfo:"_lastUpdateInfo_1ik09_691",versionInfo:"_versionInfo_1ik09_711",backButton:"_backButton_1ik09_725"};function o({onBack:o}){const[i,s]=n.useState(!1),[t,l]=n.useState(""),[m,r]=n.useState(""),[c,d]=n.useState(!1),[u,p]=n.useState({totalGames:8,totalUsage:156,mostPlayed:"letter-recognition",lastUpdate:Date.now()}),[N,b]=n.useState({"letter-recognition":45,"memory-game":32,"color-match":28,"musical-sequence":25,"number-counting":15,"image-association":11}),j={"memory-game":{name:"Jogo da Memória",icon:"🧠",color:"blue"},"color-match":{name:"Combinação de Cores",icon:"🌈",color:"green"},"image-association":{name:"Associação de Imagens",icon:"🧩",color:"orange"},"musical-sequence":{name:"Sequência Musical",icon:"🎵",color:"purple"},"letter-recognition":{name:"Reconhecimento de Letras",icon:"🔤",color:"pink"},"number-counting":{name:"Números e Contagem",icon:"🔢",color:"cyan"}},P=(e,n="success")=>{const a=document.createElement("div");a.className=`notification ${n}`,a.textContent=e,Object.assign(a.style,{position:"fixed",top:"20px",right:"20px",padding:"12px 24px",borderRadius:"8px",color:"white",fontWeight:"600",fontSize:"14px",zIndex:"10000",transform:"translateX(100%)",transition:"transform 0.3s ease",boxShadow:"0 4px 12px rgba(0,0,0,0.2)"});const o={success:"#10b981",error:"#ef4444",warning:"#f59e0b"};a.style.background=o[n]||o.success,document.body.appendChild(a),setTimeout(()=>{a.style.transform="translateX(0)"},100),setTimeout(()=>{a.style.transform="translateX(100%)",setTimeout(()=>a.remove(),300)},3e3)},g=()=>{"betina2025"===t?(s(!0),r(""),localStorage.setItem("portalBetina_adminAuth","true"),P("Login realizado com sucesso!","success")):(r("Senha incorreta. Tente novamente."),l(""),P("Senha incorreta","error"))};n.useEffect(()=>{const e="true"===localStorage.getItem("portalBetina_adminAuth");s(e)},[]);const v=e=>new Date(e).toLocaleString("pt-BR"),h=e=>{const n=Math.floor((Date.now()-e)/864e5);return 0===n?"Hoje":1===n?"Ontem":`${n} dias atrás`},x=()=>Object.entries(N).map(([e,n])=>({id:e,...j[e],count:n,lastPlayed:Date.now()-7*Math.random()*24*60*60*1e3})).sort((e,n)=>n.count-e.count);return i?e.jsxDEV("div",{className:a.container,children:e.jsxDEV("div",{className:a.adminContainer,children:[e.jsxDEV("div",{className:a.adminHeader,children:e.jsxDEV("div",{className:a.headerContent,children:[e.jsxDEV("div",{children:[e.jsxDEV("h1",{className:a.adminTitle,children:"🔐 Painel Administrativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:246,columnNumber:15},this),e.jsxDEV("p",{className:a.adminSubtitle,children:"Métricas e estatísticas detalhadas do Portal Betina"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:247,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:245,columnNumber:13},this),e.jsxDEV("button",{className:a.logoutButton,onClick:()=>{s(!1),localStorage.removeItem("portalBetina_adminAuth"),P("Logout realizado","success")},children:"🚪 Sair"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:249,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:244,columnNumber:11},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:243,columnNumber:9},this),e.jsxDEV("div",{className:a.infoBox,children:["📊 ",e.jsxDEV("strong",{children:"Status do Sistema:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:256,columnNumber:14},this)," Este painel mostra estatísticas em tempo real do uso dos jogos. Os dados são salvos localmente no navegador e atualizados automaticamente conforme o uso."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:255,columnNumber:9},this),e.jsxDEV("h2",{className:a.sectionTitle,children:"📈 Estatísticas Gerais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:261,columnNumber:9},this),e.jsxDEV("div",{className:a.statsGrid,children:[e.jsxDEV("div",{className:`${a.statCard} ${a.blue}`,children:[e.jsxDEV("div",{className:a.statNumber,children:u.totalUsage},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:264,columnNumber:13},this),e.jsxDEV("div",{className:a.statLabel,children:"Total de Jogadas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:265,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:263,columnNumber:11},this),e.jsxDEV("div",{className:`${a.statCard} ${a.green}`,children:[e.jsxDEV("div",{className:a.statNumber,children:u.totalGames},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:268,columnNumber:13},this),e.jsxDEV("div",{className:a.statLabel,children:"Jogos Utilizados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:269,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:267,columnNumber:11},this),e.jsxDEV("div",{className:`${a.statCard} ${a.orange}`,children:[e.jsxDEV("div",{className:a.statNumber,children:u.mostPlayed&&j[u.mostPlayed]?.name?.split(" ")[0]||"N/A"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:272,columnNumber:13},this),e.jsxDEV("div",{className:a.statLabel,children:"Jogo Mais Jogado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:275,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:271,columnNumber:11},this),e.jsxDEV("div",{className:`${a.statCard} ${a.purple}`,children:[e.jsxDEV("div",{className:a.statNumber,children:x().length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:278,columnNumber:13},this),e.jsxDEV("div",{className:a.statLabel,children:"Jogos no Ranking"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:279,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:277,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:262,columnNumber:9},this),e.jsxDEV("h2",{className:a.sectionTitle,children:"🎮 Detalhes por Jogo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:283,columnNumber:9},this),0===x().length?e.jsxDEV("div",{className:a.infoBox,children:"🎯 Ainda não há dados de uso registrados. Os jogos aparecerão aqui conforme forem sendo utilizados."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:285,columnNumber:11},this):e.jsxDEV("div",{className:a.gamesList,children:x().map(n=>e.jsxDEV("div",{className:a.gameCard,children:[e.jsxDEV("div",{className:`${a.gameIcon} ${a[n.color]}`,children:n.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:293,columnNumber:17},this),e.jsxDEV("div",{className:a.gameInfo,children:[e.jsxDEV("h3",{className:a.gameName,children:n.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:297,columnNumber:19},this),e.jsxDEV("div",{className:a.gameStats,children:[e.jsxDEV("div",{children:[e.jsxDEV("strong",{children:n.count},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:299,columnNumber:26},this)," jogadas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:299,columnNumber:21},this),e.jsxDEV("div",{children:["Última vez: ",h(n.lastPlayed)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:300,columnNumber:21},this),e.jsxDEV("div",{className:a.gameStatsDetail,children:v(n.lastPlayed)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:301,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:298,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:296,columnNumber:17},this)]},n.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:292,columnNumber:15},this))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:290,columnNumber:11},this),e.jsxDEV("h2",{className:a.sectionTitle,children:"⚙️ Controles Administrativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:311,columnNumber:9},this),e.jsxDEV("div",{className:a.controlsSection,children:[e.jsxDEV("h3",{className:a.controlsTitle,children:"Ações Disponíveis"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:313,columnNumber:11},this),e.jsxDEV("p",{className:a.controlsSubtitle,children:"Use estes controles para gerenciar os dados do portal:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:314,columnNumber:11},this),e.jsxDEV("div",{className:a.buttonGroup,children:[e.jsxDEV("button",{className:a.actionButton,onClick:()=>{const e={stats:u,gameUsage:N,exportDate:(new Date).toISOString()},n=JSON.stringify(e,null,2),a=new Blob([n],{type:"application/json"}),o=URL.createObjectURL(a),i=document.createElement("a");i.href=o,i.download=`portal-betina-dados-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(o),P("Dados exportados com sucesso!","success")},children:"📥 Exportar Dados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:318,columnNumber:13},this),e.jsxDEV("button",{className:a.actionButton,onClick:()=>{localStorage.removeItem("portalBetina_gameStats"),P("Cache limpo com sucesso!","success")},children:"🧹 Limpar Cache"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:321,columnNumber:13},this),e.jsxDEV("button",{className:`${a.actionButton} ${a.danger}`,onClick:()=>{window.confirm("⚠️ Tem certeza que deseja resetar todas as estatísticas? Esta ação não pode ser desfeita.")&&(b({}),p({totalGames:0,totalUsage:0,mostPlayed:null,lastUpdate:Date.now()}),P("Estatísticas resetadas com sucesso!","success"))},children:"🗑️ Resetar Estatísticas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:324,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:317,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:312,columnNumber:9},this),e.jsxDEV("div",{className:a.lastUpdateInfo,children:[e.jsxDEV("div",{children:["Última atualização: ",v(u.lastUpdate)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:334,columnNumber:11},this),e.jsxDEV("div",{className:a.versionInfo,children:"Portal Betina v3.0.0 - Sistema de Métricas Inteligente"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:335,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:333,columnNumber:9},this),e.jsxDEV("button",{className:a.backButton,onClick:o,children:"← Voltar ao Menu Principal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:340,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:242,columnNumber:7},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:241,columnNumber:5},this):e.jsxDEV("div",{className:a.container,children:e.jsxDEV("div",{className:a.loginContainer,children:[e.jsxDEV("div",{className:a.loginIcon,children:"🔐"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:199,columnNumber:11},this),e.jsxDEV("h2",{className:a.loginTitle,children:"Acesso Administrativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:200,columnNumber:11},this),e.jsxDEV("p",{className:a.loginSubtitle,children:"Digite a senha para acessar o painel administrativo do Portal Betina"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:201,columnNumber:11},this),e.jsxDEV("input",{type:"password",className:a.loginInput,placeholder:"Digite a senha de administrador",value:t,onChange:e=>l(e.target.value),onKeyPress:e=>"Enter"===e.key&&g()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:205,columnNumber:11},this),m&&e.jsxDEV("div",{className:a.loginError,children:m},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:215,columnNumber:13},this),e.jsxDEV("button",{className:a.loginButton,onClick:g,children:"🚀 Entrar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:220,columnNumber:11},this),e.jsxDEV("div",{className:a.loginFooter,children:"Portal Betina - Sistema de Métricas v3.0"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:227,columnNumber:11},this),e.jsxDEV("button",{className:a.backButton,onClick:o,children:"← Voltar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:231,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:198,columnNumber:9},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:197,columnNumber:7},this)}export{o as default};
